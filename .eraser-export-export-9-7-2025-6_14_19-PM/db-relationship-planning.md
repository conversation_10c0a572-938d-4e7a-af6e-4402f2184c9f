# Prompt

- Note: this prompt was fed to claude too early on accident.I pressed "enter" instead of shift+enter for a new line at one point, causing it to submit the prompt mid-sentence (oops q__q)

```
# Database Relationship Planning for Chocolate and Art Show Website

- help me plan and design a database relationship diagram for the database tables which would store all of the data needed to populate the website of a long-running art gallery pop-up event series. The website is built with Next.js/Tailwind CSS and has a Convex database and Clerk authentication. During development, it is deployed on Vercel automatically when changes are pushed to the remote repository. The website will need to include user and admin dashboard to allow in-browser profile management, form submissions, and data visualization, user management, show management, and make content management easy for the admin (who does not need to be technically savvy to manage the website from mobile or desktop).

## Context about the shows:
- Chocolate and Art Show is a 2-night art gallery and underground music pop-up event series that occurs every few weeks in one of the rotating list of cities across the nation.
-The next show occurs in Dallas, followed by Houston a few weeks later, and then Atlanta, Los Angeles, Phoenix, and then back to Dallas, Orlando, etc.
   - Dallas has been having  shows 2 times a year for the last 10 years. Same for Los Angeles (minus skipping a year in 2021).
   - The other cities have 1 show a year, with new cities added to the rotation occasionally.

## Database needs:
- The database would need to store data regarding:
    - user profiles
    - form submissions (separate artist and vendor applications from each show - artist application allow users to upload up to 3 photos to include with their application)
    - Shows table containing all information for each show (date/time, venue information, ticket information, list of artists/vendors selected from the applications for that show)
    - info for the venues for each city
    - list of cities (has relationship with venues and shows)
    - contact form for general questions and inquiries (from both logged-in users and website visitors who have not registered an account)(has relationship with users)
    - general media gallery showing photos/video from past shows (separate album for each show)



## BADGES SYSTEM
- `attendee` badges are automatically activated when user has purchased a ticket for a show.
   * TECHNICAL NOTE:
        - Use the Eventbrite api to sync ticket purchases data to ticket holders private dashboard.
        - If multiple tickets are purchased for a single show, ticket holder will be able to attach a users account (via search interface that tries to find a matching `fullName` in the `users` table) to each additional ticket. This will automatically grant the attendee badge for that show to  the attached user's account.
- How to earn an `artist`/`vendor` badge:
    1. Submit artist/vendor application for a show.
    2. Once the application has been approved, the user must meet various pre-show deadlines for submitting certain information.
    3. IMPORTANT: Artists/vendors must check-in during designated art drop-off/set-up time. Without checking in, the artist/vendor will not receive a badge.
    4. Badges are awarded automatically once the show has ended and check-in's are entered into the system. Artists/vendors who were approved but did not check-in for the show will not receive a badge.
- How to earn a `crew` badge:
    1. Admin manually awards the badge to users who have worked for a show. There will be `User Management` section in the admin dashboard with a `Badges` tab where the admin can search for users and award badges.
## Account role + role-based profile features
- `user` account role:
    - Default role for all users when new account is created.
    - Default profile page features:
        - Editable profile info (bio, location, general stuff)
        - Upload samples of personal artwork to display on their public-facing profile page.
        - View their previously submitted vendor/artist applications and the current status
        - View their contact form submissions, if they were logged in when submitting the contact form. (side note: contact form submissions by guests will only appear in the admin dashboard on the website. A copy of the submission will not appear on the user's dashboard since they did not have an account when submitting the form. However, a copy of the submission will be sent to the email address that was submitted with the form.)
- `artist` or `vendor` account roles
    - Reqirement: 1 artist/vendor badge
    - Account role will upgrade from 'user' to `artist` or `vendor` after they have received 1 artist/vendor badge.
- `crew` account role
    - Reqirement: 1 crew badge
    - Account role will upgrade from 'user' to `crew` after they have received 1 crew badge.

## Website features based on role:
- Logged in users with the default `user` role will have:
    - personal private dashboard where they can:
        - edit their profile info (bio, location, general stuff)
        - upload samples of their personal artwork to display on their public-facing profile page.
        - view their previously submitted vendor/artist applications and the current status
        - view their contact form submissions, if they were logged in when submitting the contact form.
    - public-facing profile Page:
        - profile information (bio, location, general stuff)
        - samples of their personal artwork
        - section that displays a special badge for each show that person participated in as an artist/vendor, crew, OR attended (badges: artist, vendor, attendee, crew)


    - Accounts with the `artist`/`vendor` role will unlock the e-commerce features includes everything from the `user` role, plus:
        - Public-facing profile page:
            - adds artwork storefront,
            - adds option for "accepting commissions" banner + commission request form
        - Private dashboard:
            - Adds a section for managing their storefront to sell their own artwork. Listing artwork requires uploaded photo of artwork, set price and details of artwork, choose delievery method (in person and/or shipping)
            - Adds a section for managing their commission requests. This will display a list of all commission requests received, the status of each request, and the ability to accept or decline each request and privately message the potential customer.
```
