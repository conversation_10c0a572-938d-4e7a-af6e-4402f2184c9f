<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chocolate and Art Show - Database ERD</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2d1b69 0%, #11998e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .erd-container {
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .erd-diagram {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(4, auto);
            gap: 40px;
            min-height: 1200px;
            position: relative;
        }
        
        .entity {
            background: white;
            border: 3px solid #2d1b69;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            z-index: 10;
        }
        
        .entity:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            z-index: 20;
        }
        
        .entity-header {
            background: linear-gradient(135deg, #2d1b69 0%, #11998e 100%);
            color: white;
            padding: 15px;
            font-weight: bold;
            font-size: 1.1em;
            text-align: center;
        }
        
        .entity-body {
            padding: 0;
        }
        
        .field {
            padding: 8px 15px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9em;
            position: relative;
        }
        
        .field:last-child {
            border-bottom: none;
        }
        
        .field.primary-key {
            background: #fff3cd;
            font-weight: bold;
            border-left: 4px solid #ffc107;
        }
        
        .field.foreign-key {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        
        .field-type {
            color: #666;
            font-size: 0.8em;
            float: right;
        }
        
        /* SVG for relationship lines */
        .relationship-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .relationship-line {
            stroke: #666;
            stroke-width: 2;
            fill: none;
            opacity: 0.6;
        }
        
        .relationship-line.highlight {
            stroke: #2d1b69;
            stroke-width: 3;
            opacity: 0.8;
        }
        
        /* Grid positioning for entities */
        .users { grid-column: 1; grid-row: 1; }
        .cities { grid-column: 2; grid-row: 1; }
        .venues { grid-column: 3; grid-row: 1; }
        .shows { grid-column: 4; grid-row: 1; }
        .admin-users { grid-column: 5; grid-row: 1; }
        
        .artist-apps { grid-column: 1; grid-row: 2; }
        .vendor-apps { grid-column: 2; grid-row: 2; }
        .app-photos { grid-column: 3; grid-row: 2; }
        .show-participants { grid-column: 4; grid-row: 2; }
        .user-badges { grid-column: 5; grid-row: 2; }
        
        .user-artwork { grid-column: 1; grid-row: 3; }
        .contact-forms { grid-column: 2; grid-row: 3; }
        .media-gallery { grid-column: 3; grid-row: 3; }
        .products { grid-column: 4; grid-row: 3; }
        
        .legend {
            margin-top: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2d1b69;
        }
        
        .legend h3 {
            margin: 0 0 15px 0;
            color: #2d1b69;
        }
        
        .legend-item {
            display: inline-block;
            margin: 0 25px 10px 0;
            font-size: 0.9em;
        }
        
        .legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 3px;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        .pk { background: #ffc107; }
        .fk { background: #17a2b8; }
        
        .notes {
            margin-top: 30px;
            padding: 25px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        
        .notes h3 {
            margin: 0 0 15px 0;
            color: #155724;
        }
        
        .notes ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .notes li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Chocolate and Art Show</h1>
            <p>Database Entity Relationship Diagram</p>
        </div>
        
        <div class="erd-container">
            <div class="erd-diagram">
                <svg class="relationship-svg" id="relationshipSvg">
                    <!-- Relationship lines will be drawn here by JavaScript -->
                </svg>

                <!-- Row 1: Core entities -->
                <div class="entity users" data-entity="users">
                    <div class="entity-header">USERS</div>
                    <div class="entity-body">
                        <div class="field primary-key">user_id <span class="field-type">INT PK</span></div>
                        <div class="field">email <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">password_hash <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">first_name <span class="field-type">VARCHAR(100)</span></div>
                        <div class="field">last_name <span class="field-type">VARCHAR(100)</span></div>
                        <div class="field">phone <span class="field-type">VARCHAR(20)</span></div>
                        <div class="field">bio <span class="field-type">TEXT</span></div>
                        <div class="field">location <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">role <span class="field-type">ENUM</span></div>
                        <div class="field">profile_image <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">is_active <span class="field-type">BOOLEAN</span></div>
                        <div class="field">created_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">updated_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>

                <div class="entity cities" data-entity="cities">
                    <div class="entity-header">CITIES</div>
                    <div class="entity-body">
                        <div class="field primary-key">city_id <span class="field-type">INT PK</span></div>
                        <div class="field">city_name <span class="field-type">VARCHAR(100)</span></div>
                        <div class="field">state <span class="field-type">VARCHAR(50)</span></div>
                        <div class="field">country <span class="field-type">VARCHAR(50)</span></div>
                        <div class="field">shows_per_year <span class="field-type">INT</span></div>
                        <div class="field">is_active <span class="field-type">BOOLEAN</span></div>
                        <div class="field">added_date <span class="field-type">DATE</span></div>
                    </div>
                </div>

                <div class="entity venues" data-entity="venues">
                    <div class="entity-header">VENUES</div>
                    <div class="entity-body">
                        <div class="field primary-key">venue_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">city_id <span class="field-type">INT FK</span></div>
                        <div class="field">venue_name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">address <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">capacity <span class="field-type">INT</span></div>
                        <div class="field">contact_person <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">contact_email <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">contact_phone <span class="field-type">VARCHAR(20)</span></div>
                        <div class="field">notes <span class="field-type">TEXT</span></div>
                        <div class="field">is_active <span class="field-type">BOOLEAN</span></div>
                    </div>
                </div>

                <div class="entity shows" data-entity="shows">
                    <div class="entity-header">SHOWS</div>
                    <div class="entity-body">
                        <div class="field primary-key">show_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">city_id <span class="field-type">INT FK</span></div>
                        <div class="field foreign-key">venue_id <span class="field-type">INT FK</span></div>
                        <div class="field">show_title <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">show_date <span class="field-type">DATE</span></div>
                        <div class="field">start_time <span class="field-type">TIME</span></div>
                        <div class="field">end_time <span class="field-type">TIME</span></div>
                        <div class="field">description <span class="field-type">TEXT</span></div>
                        <div class="field">ticket_price <span class="field-type">DECIMAL(10,2)</span></div>
                        <div class="field">max_attendees <span class="field-type">INT</span></div>
                        <div class="field">status <span class="field-type">ENUM</span></div>
                        <div class="field">created_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>

                <div class="entity admin-users" data-entity="admin-users">
                    <div class="entity-header">ADMIN_USERS</div>
                    <div class="entity-body">
                        <div class="field primary-key">admin_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field">admin_level <span class="field-type">ENUM</span></div>
                        <div class="field">permissions <span class="field-type">JSON</span></div>
                        <div class="field">created_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">created_by <span class="field-type">INT</span></div>
                        <div class="field">is_active <span class="field-type">BOOLEAN</span></div>
                    </div>
                </div>

                <!-- Row 2: Application and participation entities -->
                <div class="entity artist-apps" data-entity="artist-apps">
                    <div class="entity-header">ARTIST_APPLICATIONS</div>
                    <div class="entity-body">
                        <div class="field primary-key">application_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field foreign-key">show_id <span class="field-type">INT FK</span></div>
                        <div class="field">artist_statement <span class="field-type">TEXT</span></div>
                        <div class="field">medium <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">experience_level <span class="field-type">ENUM</span></div>
                        <div class="field">special_requirements <span class="field-type">TEXT</span></div>
                        <div class="field">status <span class="field-type">ENUM</span></div>
                        <div class="field">submitted_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">reviewed_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">reviewed_by <span class="field-type">INT</span></div>
                        <div class="field">admin_notes <span class="field-type">TEXT</span></div>
                    </div>
                </div>

                <div class="entity vendor-apps" data-entity="vendor-apps">
                    <div class="entity-header">VENDOR_APPLICATIONS</div>
                    <div class="entity-body">
                        <div class="field primary-key">application_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field foreign-key">show_id <span class="field-type">INT FK</span></div>
                        <div class="field">business_name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">product_description <span class="field-type">TEXT</span></div>
                        <div class="field">booth_size_needed <span class="field-type">ENUM</span></div>
                        <div class="field">electrical_needed <span class="field-type">BOOLEAN</span></div>
                        <div class="field">special_requirements <span class="field-type">TEXT</span></div>
                        <div class="field">status <span class="field-type">ENUM</span></div>
                        <div class="field">submitted_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">reviewed_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">reviewed_by <span class="field-type">INT</span></div>
                        <div class="field">admin_notes <span class="field-type">TEXT</span></div>
                    </div>
                </div>

                <div class="entity app-photos" data-entity="app-photos">
                    <div class="entity-header">APPLICATION_PHOTOS</div>
                    <div class="entity-body">
                        <div class="field primary-key">photo_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">artist_application_id <span class="field-type">INT FK</span></div>
                        <div class="field">file_name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">file_path <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">file_size <span class="field-type">INT</span></div>
                        <div class="field">mime_type <span class="field-type">VARCHAR(100)</span></div>
                        <div class="field">caption <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">upload_order <span class="field-type">TINYINT</span></div>
                        <div class="field">uploaded_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>

                <div class="entity show-participants" data-entity="show-participants">
                    <div class="entity-header">SHOW_PARTICIPANTS</div>
                    <div class="entity-body">
                        <div class="field primary-key">participant_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">show_id <span class="field-type">INT FK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field">participant_type <span class="field-type">ENUM</span></div>
                        <div class="field">booth_number <span class="field-type">VARCHAR(20)</span></div>
                        <div class="field">setup_notes <span class="field-type">TEXT</span></div>
                        <div class="field">checked_in <span class="field-type">BOOLEAN</span></div>
                        <div class="field">check_in_time <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">added_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>

                <div class="entity user-badges" data-entity="user-badges">
                    <div class="entity-header">USER_BADGES</div>
                    <div class="entity-body">
                        <div class="field primary-key">badge_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field foreign-key">show_id <span class="field-type">INT FK</span></div>
                        <div class="field">badge_type <span class="field-type">ENUM</span></div>
                        <div class="field">earned_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">is_visible <span class="field-type">BOOLEAN</span></div>
                    </div>
                </div>

                <!-- Row 3: Content and media entities -->
                <div class="entity user-artwork" data-entity="user-artwork">
                    <div class="entity-header">USER_ARTWORK</div>
                    <div class="entity-body">
                        <div class="field primary-key">artwork_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field">title <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">description <span class="field-type">TEXT</span></div>
                        <div class="field">medium <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">dimensions <span class="field-type">VARCHAR(100)</span></div>
                        <div class="field">year_created <span class="field-type">YEAR</span></div>
                        <div class="field">file_name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">file_path <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">is_public <span class="field-type">BOOLEAN</span></div>
                        <div class="field">display_order <span class="field-type">INT</span></div>
                        <div class="field">uploaded_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>

                <div class="entity contact-forms" data-entity="contact-forms">
                    <div class="entity-header">CONTACT_FORMS</div>
                    <div class="entity-body">
                        <div class="field primary-key">contact_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field">name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">email <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">subject <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">message <span class="field-type">TEXT</span></div>
                        <div class="field">ip_address <span class="field-type">VARCHAR(45)</span></div>
                        <div class="field">user_agent <span class="field-type">TEXT</span></div>
                        <div class="field">status <span class="field-type">ENUM</span></div>
                        <div class="field">responded_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">responded_by <span class="field-type">INT</span></div>
                        <div class="field">submitted_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>

                <div class="entity media-gallery" data-entity="media-gallery">
                    <div class="entity-header">MEDIA_GALLERY</div>
                    <div class="entity-body">
                        <div class="field primary-key">media_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">show_id <span class="field-type">INT FK</span></div>
                        <div class="field">file_name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">file_path <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">file_type <span class="field-type">ENUM</span></div>
                        <div class="field">file_size <span class="field-type">INT</span></div>
                        <div class="field">caption <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">photographer <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">is_featured <span class="field-type">BOOLEAN</span></div>
                        <div class="field">display_order <span class="field-type">INT</span></div>
                        <div class="field">uploaded_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">uploaded_by <span class="field-type">INT</span></div>
                    </div>
                </div>

                <div class="entity products" data-entity="products">
                    <div class="entity-header">PRODUCTS</div>
                    <div class="entity-body">
                        <div class="field primary-key">product_id <span class="field-type">INT PK</span></div>
                        <div class="field foreign-key">user_id <span class="field-type">INT FK</span></div>
                        <div class="field">name <span class="field-type">VARCHAR(255)</span></div>
                        <div class="field">description <span class="field-type">TEXT</span></div>
                        <div class="field">price <span class="field-type">DECIMAL(10,2)</span></div>
                        <div class="field">category <span class="field-type">VARCHAR(100)</span></div>
                        <div class="field">stock_quantity <span class="field-type">INT</span></div>
                        <div class="field">image_path <span class="field-type">VARCHAR(500)</span></div>
                        <div class="field">is_active <span class="field-type">BOOLEAN</span></div>
                        <div class="field">created_at <span class="field-type">TIMESTAMP</span></div>
                        <div class="field">updated_at <span class="field-type">TIMESTAMP</span></div>
                    </div>
                </div>
            </div>
            
            <div class="legend">
                <h3>Legend</h3>
                <div class="legend-item">
                    <span class="legend-color pk"></span>
                    Primary Key (PK)
                </div>
                <div class="legend-item">
                    <span class="legend-color fk"></span>
                    Foreign Key (FK)
                </div>
            </div>
            
            <div class="notes">
                <h3>Key Relationships & Notes</h3>
                <ul>
                    <li><strong>Users → Applications:</strong> One-to-many relationship for both artist and vendor applications</li>
                    <li><strong>Shows → Applications:</strong> Each show can have multiple applications of both types</li>
                    <li><strong>Cities → Venues:</strong> One city can have multiple venues</li>
                    <li><strong>Shows → Participants:</strong> Approved applicants become show participants</li>
                    <li><strong>User Badges:</strong> Automatically created when applications are approved</li>
                    <li><strong>Role Upgrades:</strong> User roles automatically update from "user" to "artist"/"vendor" upon approval</li>
                    <li><strong>Contact Forms:</strong> Can be submitted by both registered users and guests (user_id can be NULL)</li>
                    <li><strong>Media Gallery:</strong> Each show has its own album of photos/videos</li>
                    <li><strong>E-commerce:</strong> Only available to users with "artist" or "vendor" roles</li>
                    <li><strong>Application Photos:</strong> Up to 3 photos per artist application (enforced at application level)</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Define relationships between entities
        const relationships = [
            // Core relationships
            { from: 'cities', to: 'venues', type: 'one-to-many' },
            { from: 'cities', to: 'shows', type: 'one-to-many' },
            { from: 'venues', to: 'shows', type: 'one-to-many' },
            { from: 'users', to: 'admin-users', type: 'one-to-one' },
            
            // Application relationships
            { from: 'users', to: 'artist-apps', type: 'one-to-many' },
            { from: 'users', to: 'vendor-apps', type: 'one-to-many' },
            { from: 'shows', to: 'artist-apps', type: 'one-to-many' },
            { from: 'shows', to: 'vendor-apps', type: 'one-to-many' },
            { from: 'artist-apps', to: 'app-photos', type: 'one-to-many' },
            
            // Participation and badges
            { from: 'users', to: 'show-participants', type: 'one-to-many' },
            { from: 'shows', to: 'show-participants', type: 'one-to-many' },
            { from: 'users', to: 'user-badges', type: 'one-to-many' },
            { from: 'shows', to: 'user-badges', type: 'one-to-many' },
            
            // Content relationships
            { from: 'users', to: 'user-artwork', type: 'one-to-many' },
            { from: 'users', to: 'contact-forms', type: 'one-to-many' },
            { from: 'shows', to: 'media-gallery', type: 'one-to-many' },
            { from: 'users', to: 'products', type: 'one-to-many' }
        ];

        function drawRelationships() {
            const svg = document.getElementById('relationshipSvg');
            const container = document.querySelector('.erd-container');
            const containerRect = container.getBoundingClientRect();
            
            // Clear existing lines
            svg.innerHTML = '';
            
            relationships.forEach(rel => {
                const fromEntity = document.querySelector(`[data-entity="${rel.from}"]`);
                const toEntity = document.querySelector(`[data-entity="${rel.to}"]`);
                
                if (fromEntity && toEntity) {
                    const fromRect = fromEntity.getBoundingClientRect();
                    const toRect = toEntity.getBoundingClientRect();
                    
                    // Calculate connection points (center of entities)
                    const fromX = fromRect.left + fromRect.width / 2 - containerRect.left;
                    const fromY = fromRect.top + fromRect.height / 2 - containerRect.top;
                    const toX = toRect.left + toRect.width / 2 - containerRect.left;
                    const toY = toRect.top + toRect.height / 2 - containerRect.top;
                    
                    // Create curved path for better visibility
                    const midX = (fromX + toX) / 2;
                    const midY = (fromY + toY) / 2;
                    const controlX = midX + (fromY - toY) * 0.2; // Perpendicular offset
                    const controlY = midY + (to