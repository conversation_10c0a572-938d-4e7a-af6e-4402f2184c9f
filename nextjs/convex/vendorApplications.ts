import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Submit vendor application
export const submitVendorApplication = mutation({
  args: {
    userId: v.id("users"),
    showId: v.id("shows"),
    businessName: v.string(),
    productDescription: v.string(),
    boothSizeNeeded: v.union(
      v.literal("small"),
      v.literal("medium"),
      v.literal("large"),
      v.literal("custom")
    ),
    electricalNeeded: v.boolean(),
    specialRequirements: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user exists
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if show exists
    const show = await ctx.db.get(args.showId);
    if (!show) {
      throw new Error("Show not found");
    }

    // Check if user already has an application for this show
    const existingApplication = await ctx.db
      .query("vendorApplications")
      .withIndex("by_user_show", (q) =>
        q.eq("userId", args.userId).eq("showId", args.showId)
      )
      .unique();

    if (existingApplication) {
      throw new Error("You have already submitted an application for this show");
    }

    // Create application
    return await ctx.db.insert("vendorApplications", {
      userId: args.userId,
      showId: args.showId,
      businessName: args.businessName,
      productDescription: args.productDescription,
      boothSizeNeeded: args.boothSizeNeeded,
      electricalNeeded: args.electricalNeeded,
      specialRequirements: args.specialRequirements,
      status: "pending",
      submittedAt: Date.now(),
    });
  },
});

// Get vendor application by ID
export const getVendorApplicationById = query({
  args: { applicationId: v.id("vendorApplications") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.applicationId);
  },
});

// Get vendor application with details (user, show)
export const getVendorApplicationWithDetails = query({
  args: { applicationId: v.id("vendorApplications") },
  handler: async (ctx, args) => {
    const application = await ctx.db.get(args.applicationId);
    if (!application) return null;

    const user = await ctx.db.get(application.userId);
    const show = await ctx.db.get(application.showId);

    return {
      ...application,
      user,
      show,
    };
  },
});

// Get user's vendor applications
export const getUserVendorApplications = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    const applications = await ctx.db
      .query("vendorApplications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);

    // Get show details for each application
    const applicationsWithShows = await Promise.all(
      applications.map(async (application) => {
        const show = await ctx.db.get(application.showId);
        return {
          ...application,
          show,
        };
      })
    );

    return applicationsWithShows;
  },
});

// Get applications for a show (admin function)
export const getShowVendorApplications = query({
  args: {
    showId: v.id("shows"),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("waitlisted")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;

    let applications;

    if (args.status) {
      // Query by status first, then filter by show
      const allByStatus = await ctx.db
        .query("vendorApplications")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc")
        .collect();

      applications = allByStatus
        .filter(app => app.showId === args.showId)
        .slice(0, limit);
    } else {
      // Query directly by show
      applications = await ctx.db
        .query("vendorApplications")
        .withIndex("by_show", (q) => q.eq("showId", args.showId))
        .order("desc")
        .take(limit);
    }

    // No need for additional filtering since we already filtered above
    const filteredApplications = applications;

    // Get user details for each application
    const applicationsWithUsers = await Promise.all(
      filteredApplications.map(async (application) => {
        const user = await ctx.db.get(application.userId);

        return {
          ...application,
          user,
        };
      })
    );

    return applicationsWithUsers;
  },
});

// Update application status (admin function)
export const updateVendorApplicationStatus = mutation({
  args: {
    applicationId: v.id("vendorApplications"),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("waitlisted")
    ),
    reviewedBy: v.id("users"),
    adminNotes: v.optional(v.string()),
    boothNumber: v.optional(v.string()),
    setupNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { applicationId, ...updates } = args;

    await ctx.db.patch(applicationId, {
      status: updates.status,
      reviewedAt: Date.now(),
      reviewedBy: updates.reviewedBy,
      adminNotes: updates.adminNotes,
      boothNumber: updates.boothNumber,
      setupNotes: updates.setupNotes,
    });

    // If approved, potentially update user role to vendor
    if (updates.status === "approved") {
      const application = await ctx.db.get(applicationId);
      if (application) {
        const user = await ctx.db.get(application.userId);
        if (user && user.role === "user") {
          await ctx.db.patch(application.userId, {
            role: "vendor",
            updatedAt: Date.now(),
          });
        }
      }
    }
  },
});

// Check in vendor for show
export const checkInVendor = mutation({
  args: {
    applicationId: v.id("vendorApplications"),
    checkedInBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    const application = await ctx.db.get(args.applicationId);
    if (!application) {
      throw new Error("Application not found");
    }

    if (application.status !== "approved") {
      throw new Error("Only approved applications can be checked in");
    }

    await ctx.db.patch(args.applicationId, {
      checkedIn: true,
      checkInTime: Date.now(),
    });
  },
});

// Get application statistics for a show
export const getShowVendorApplicationStats = query({
  args: { showId: v.id("shows") },
  handler: async (ctx, args) => {
    const applications = await ctx.db
      .query("vendorApplications")
      .withIndex("by_show", (q) => q.eq("showId", args.showId))
      .collect();

    const stats = {
      total: applications.length,
      pending: applications.filter(app => app.status === "pending").length,
      underReview: applications.filter(app => app.status === "under_review").length,
      approved: applications.filter(app => app.status === "approved").length,
      rejected: applications.filter(app => app.status === "rejected").length,
      waitlisted: applications.filter(app => app.status === "waitlisted").length,
      checkedIn: applications.filter(app => app.checkedIn === true).length,
    };

    return stats;
  },
});

// Delete application (user can only delete pending applications)
export const deleteVendorApplication = mutation({
  args: {
    applicationId: v.id("vendorApplications"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const application = await ctx.db.get(args.applicationId);
    if (!application) {
      throw new Error("Application not found");
    }

    // Check if user owns the application
    if (application.userId !== args.userId) {
      throw new Error("You can only delete your own applications");
    }

    // Check if application is still pending
    if (application.status !== "pending") {
      throw new Error("You can only delete pending applications");
    }

    // Delete the application
    await ctx.db.delete(args.applicationId);
  },
});

// Get all applications for a user (both artist and vendor)
export const getUserAllApplications = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    // Get artist applications
    const artistApplications = await ctx.db
      .query("artistApplications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Get vendor applications
    const vendorApplications = await ctx.db
      .query("vendorApplications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Combine and sort by submission date
    const allApplications = [
      ...artistApplications.map(app => ({ ...app, type: "artist" as const })),
      ...vendorApplications.map(app => ({ ...app, type: "vendor" as const })),
    ].sort((a, b) => b.submittedAt - a.submittedAt);

    // Get show details for each application
    const applicationsWithShows = await Promise.all(
      allApplications.slice(0, limit).map(async (application) => {
        const show = await ctx.db.get(application.showId);
        return {
          ...application,
          show,
        };
      })
    );

    return applicationsWithShows;
  },
});
