import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create or update user from Clerk authentication
export const createOrUpdateUser = mutation({
  args: {
    clerkId: v.string(),
    email: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    phone: v.optional(v.string()),
    profileImage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .unique();

    const now = Date.now();

    if (existingUser) {
      // Update existing user
      await ctx.db.patch(existingUser._id, {
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        phone: args.phone,
        profileImage: args.profileImage,
        updatedAt: now,
      });
      return existingUser._id;
    } else {
      // Create new user
      const userId = await ctx.db.insert("users", {
        clerkId: args.clerkId,
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        phone: args.phone,
        bio: undefined,
        location: undefined,
        role: "user", // Default role
        profileImage: args.profileImage,
        isActive: true,
        createdAt: now,
        updatedAt: now,
        attendedShows: [],
      });
      return userId;
    }
  },
});

// Get user by Clerk ID
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .unique();
  },
});

// Get user by ID
export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  },
});

// Update user profile
export const updateUserProfile = mutation({
  args: {
    userId: v.id("users"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    phone: v.optional(v.string()),
    bio: v.optional(v.string()),
    location: v.optional(v.string()),
    profileImage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;

    // Filter out undefined values
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(userId, {
      ...filteredUpdates,
      updatedAt: Date.now(),
    });
  },
});

// Update user role (admin function)
export const updateUserRole = mutation({
  args: {
    userId: v.id("users"),
    newRole: v.union(
      v.literal("user"),
      v.literal("attendee"),
      v.literal("artist"),
      v.literal("vendor"),
      v.literal("crew"),
      v.literal("admin"),
      v.literal("staff")
    ),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      role: args.newRole,
      updatedAt: Date.now(),
    });
  },
});

// Add show to user's attended shows
export const addAttendedShow = mutation({
  args: {
    userId: v.id("users"),
    showId: v.id("shows"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) throw new Error("User not found");

    const attendedShows = user.attendedShows || [];
    if (!attendedShows.includes(args.showId)) {
      attendedShows.push(args.showId);
      await ctx.db.patch(args.userId, {
        attendedShows,
        updatedAt: Date.now(),
      });
    }
  },
});

// Get all users (admin function with pagination)
export const getAllUsers = query({
  args: {
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    role: v.optional(v.union(
      v.literal("user"),
      v.literal("attendee"),
      v.literal("artist"),
      v.literal("vendor"),
      v.literal("crew"),
      v.literal("admin"),
      v.literal("staff")
    )),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    if (args.role) {
      return await ctx.db
        .query("users")
        .withIndex("by_role", (q) => q.eq("role", args.role!))
        .order("desc")
        .paginate({
          cursor: args.cursor || null,
          numItems: limit,
        });
    } else {
      return await ctx.db
        .query("users")
        .order("desc")
        .paginate({
          cursor: args.cursor || null,
          numItems: limit,
        });
    }
  },
});

// Search users by name or email
export const searchUsers = query({
  args: {
    searchTerm: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const searchTerm = args.searchTerm.toLowerCase();

    const users = await ctx.db.query("users").collect();

    return users
      .filter((user) => {
        const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
        const email = user.email.toLowerCase();
        return fullName.includes(searchTerm) || email.includes(searchTerm);
      })
      .slice(0, limit);
  },
});
