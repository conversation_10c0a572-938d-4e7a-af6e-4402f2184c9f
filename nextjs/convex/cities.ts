import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create a new city
export const createCity = mutation({
  args: {
    name: v.string(),
    state: v.string(),
    country: v.string(),
    showsPerYear: v.number(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if city already exists
    const existingCity = await ctx.db
      .query("cities")
      .withIndex("by_name_state", (q) => 
        q.eq("name", args.name).eq("state", args.state)
      )
      .unique();

    if (existingCity) {
      throw new Error(`City ${args.name}, ${args.state} already exists`);
    }

    return await ctx.db.insert("cities", {
      name: args.name,
      state: args.state,
      country: args.country,
      showsPerYear: args.showsPerYear,
      isActive: true,
      addedDate: Date.now(),
      notes: args.notes,
    });
  },
});

// Get all active cities
export const getActiveCities = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("cities")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .order("asc")
      .collect();
  },
});

// Get all cities (admin function)
export const getAllCities = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("cities")
      .order("asc")
      .collect();
  },
});

// Get city by ID
export const getCityById = query({
  args: { cityId: v.id("cities") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.cityId);
  },
});

// Update city
export const updateCity = mutation({
  args: {
    cityId: v.id("cities"),
    name: v.optional(v.string()),
    state: v.optional(v.string()),
    country: v.optional(v.string()),
    showsPerYear: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { cityId, ...updates } = args;
    
    // Filter out undefined values
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(cityId, filteredUpdates);
  },
});

// Delete city (admin function)
export const deleteCity = mutation({
  args: { cityId: v.id("cities") },
  handler: async (ctx, args) => {
    // Check if city has venues
    const venues = await ctx.db
      .query("venues")
      .withIndex("by_city", (q) => q.eq("cityId", args.cityId))
      .collect();

    if (venues.length > 0) {
      throw new Error("Cannot delete city with existing venues");
    }

    // Check if city has shows
    const shows = await ctx.db
      .query("shows")
      .withIndex("by_city", (q) => q.eq("cityId", args.cityId))
      .collect();

    if (shows.length > 0) {
      throw new Error("Cannot delete city with existing shows");
    }

    await ctx.db.delete(args.cityId);
  },
});

// Get cities with venue counts
export const getCitiesWithVenueCounts = query({
  handler: async (ctx) => {
    const cities = await ctx.db.query("cities").collect();
    
    const citiesWithCounts = await Promise.all(
      cities.map(async (city) => {
        const venues = await ctx.db
          .query("venues")
          .withIndex("by_city", (q) => q.eq("cityId", city._id))
          .collect();
        
        const activeVenues = venues.filter(v => v.isActive);
        
        return {
          ...city,
          totalVenues: venues.length,
          activeVenues: activeVenues.length,
        };
      })
    );

    return citiesWithCounts;
  },
});

// Get cities with show counts
export const getCitiesWithShowCounts = query({
  handler: async (ctx) => {
    const cities = await ctx.db.query("cities").collect();
    
    const citiesWithCounts = await Promise.all(
      cities.map(async (city) => {
        const shows = await ctx.db
          .query("shows")
          .withIndex("by_city", (q) => q.eq("cityId", city._id))
          .collect();
        
        const upcomingShows = shows.filter(s => s.date > Date.now());
        const pastShows = shows.filter(s => s.date <= Date.now());
        
        return {
          ...city,
          totalShows: shows.length,
          upcomingShows: upcomingShows.length,
          pastShows: pastShows.length,
        };
      })
    );

    return citiesWithCounts;
  },
});
