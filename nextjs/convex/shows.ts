import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new show
export const createShow = mutation({
  args: {
    cityId: v.id("cities"),
    venueId: v.id("venues"),
    title: v.string(),
    description: v.optional(v.string()),
    date: v.number(),
    startTime: v.string(),
    endTime: v.string(),
    ticketPrice: v.object({
      earlyBird: v.optional(v.number()),
      regular: v.number(),
      vip: v.optional(v.number()),
    }),
    earlyBirdStartDate: v.optional(v.number()),
    earlyBirdEndDate: v.optional(v.number()),
    regularSaleStartDate: v.number(),
    regularSaleEndDate: v.number(),
    maxAttendees: v.optional(v.number()),
    eventbriteEventId: v.optional(v.string()),
    eventbriteUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Verify city and venue exist
    const city = await ctx.db.get(args.cityId);
    const venue = await ctx.db.get(args.venueId);

    if (!city) throw new Error("City not found");
    if (!venue) throw new Error("Venue not found");
    if (venue.cityId !== args.cityId) {
      throw new Error("Venue does not belong to the specified city");
    }

    const now = Date.now();

    return await ctx.db.insert("shows", {
      cityId: args.cityId,
      venueId: args.venueId,
      title: args.title,
      description: args.description,
      date: args.date,
      startTime: args.startTime,
      endTime: args.endTime,
      ticketPrice: args.ticketPrice,
      earlyBirdStartDate: args.earlyBirdStartDate,
      earlyBirdEndDate: args.earlyBirdEndDate,
      regularSaleStartDate: args.regularSaleStartDate,
      regularSaleEndDate: args.regularSaleEndDate,
      maxAttendees: args.maxAttendees,
      status: "coming_soon",
      eventbriteEventId: args.eventbriteEventId,
      eventbriteUrl: args.eventbriteUrl,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Get upcoming shows
export const getUpcomingShows = query({
  args: {
    limit: v.optional(v.number()),
    cityId: v.optional(v.id("cities")),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    const now = Date.now();

    let allShows;

    if (args.cityId) {
      allShows = await ctx.db
        .query("shows")
        .withIndex("by_city", (q) => q.eq("cityId", args.cityId!))
        .collect();
    } else {
      allShows = await ctx.db
        .query("shows")
        .collect();
    }

    return allShows
      .filter(show => show.date >= now)
      .sort((a, b) => a.date - b.date)
      .slice(0, limit);
  },
});

// Get past shows
export const getPastShows = query({
  args: {
    limit: v.optional(v.number()),
    cityId: v.optional(v.id("cities")),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    const now = Date.now();

    let allShows;

    if (args.cityId) {
      allShows = await ctx.db
        .query("shows")
        .withIndex("by_city", (q) => q.eq("cityId", args.cityId!))
        .collect();
    } else {
      allShows = await ctx.db
        .query("shows")
        .collect();
    }

    return allShows
      .filter(show => show.date < now)
      .sort((a, b) => b.date - a.date)
      .slice(0, limit);
  },
});

// Get show by ID
export const getShowById = query({
  args: { showId: v.id("shows") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.showId);
  },
});

// Get show with city and venue information
export const getShowWithDetails = query({
  args: { showId: v.id("shows") },
  handler: async (ctx, args) => {
    const show = await ctx.db.get(args.showId);
    if (!show) return null;

    const city = await ctx.db.get(show.cityId);
    const venue = await ctx.db.get(show.venueId);

    return {
      ...show,
      city,
      venue,
    };
  },
});

// Get all shows (admin function)
export const getAllShows = query({
  args: {
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    status: v.optional(v.union(
      v.literal("coming_soon"),
      v.literal("early_bird"),
      v.literal("regular_sale"),
      v.literal("low_availability"),
      v.literal("sold_out"),
      v.literal("completed"),
      v.literal("cancelled")
    )),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    if (args.status) {
      return await ctx.db
        .query("shows")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc")
        .paginate({
          cursor: args.cursor || null,
          numItems: limit,
        });
    } else {
      return await ctx.db
        .query("shows")
        .order("desc")
        .paginate({
          cursor: args.cursor || null,
          numItems: limit,
        });
    }
  },
});

// Update show
export const updateShow = mutation({
  args: {
    showId: v.id("shows"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    date: v.optional(v.number()),
    startTime: v.optional(v.string()),
    endTime: v.optional(v.string()),
    ticketPrice: v.optional(v.object({
      earlyBird: v.optional(v.number()),
      regular: v.number(),
      vip: v.optional(v.number()),
    })),
    earlyBirdStartDate: v.optional(v.number()),
    earlyBirdEndDate: v.optional(v.number()),
    regularSaleStartDate: v.optional(v.number()),
    regularSaleEndDate: v.optional(v.number()),
    maxAttendees: v.optional(v.number()),
    status: v.optional(v.union(
      v.literal("coming_soon"),
      v.literal("early_bird"),
      v.literal("regular_sale"),
      v.literal("low_availability"),
      v.literal("sold_out"),
      v.literal("completed"),
      v.literal("cancelled")
    )),
    eventbriteEventId: v.optional(v.string()),
    eventbriteUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { showId, ...updates } = args;

    // Filter out undefined values
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(showId, {
      ...filteredUpdates,
      updatedAt: Date.now(),
    });
  },
});

// Delete show (admin function)
export const deleteShow = mutation({
  args: { showId: v.id("shows") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.showId);
  },
});

// Get shows by date range
export const getShowsByDateRange = query({
  args: {
    startDate: v.number(),
    endDate: v.number(),
    cityId: v.optional(v.id("cities")),
  },
  handler: async (ctx, args) => {
    let allShows;

    if (args.cityId) {
      allShows = await ctx.db
        .query("shows")
        .withIndex("by_city", (q) => q.eq("cityId", args.cityId!))
        .collect();
    } else {
      allShows = await ctx.db
        .query("shows")
        .collect();
    }

    return allShows.filter(show =>
      show.date >= args.startDate && show.date <= args.endDate
    );
  },
});

// Get shows by Eventbrite event ID
export const getShowByEventbriteId = query({
  args: { eventbriteEventId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("shows")
      .withIndex("by_eventbrite_id", (q) =>
        q.eq("eventbriteEventId", args.eventbriteEventId)
      )
      .unique();
  },
});
