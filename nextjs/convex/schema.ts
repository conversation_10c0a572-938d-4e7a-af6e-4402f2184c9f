import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
    // Core user profiles and authentication
    users: defineTable({
        // Clerk integration
        clerkId: v.string(), // Clerk user ID for authentication
        email: v.string(),

        // Basic profile information
        firstName: v.string(),
        lastName: v.string(),
        phone: v.optional(v.string()),
        bio: v.optional(v.string()),
        location: v.optional(v.string()),

        // User role and status
        role: v.union(
            v.literal("user"),
            v.literal("attendee"),
            v.literal("artist"),
            v.literal("vendor"),
            v.literal("crew"),
            v.literal("admin"),
            v.literal("staff")
        ),

        // Profile settings
        profileImage: v.optional(v.string()), // URL to profile image
        isActive: v.boolean(),

        // Metadata
        createdAt: v.number(),
        updatedAt: v.number(),

        // Show attendance tracking
        attendedShows: v.optional(v.array(v.id("shows"))),
    })
        .index("by_clerk_id", ["clerkId"])
        .index("by_email", ["email"])
        .index("by_role", ["role"])
        .index("by_created_at", ["createdAt"]),

    // Cities where shows are held
    cities: defineTable({
        name: v.string(),
        state: v.string(),
        country: v.string(),
        showsPerYear: v.number(),
        isActive: v.boolean(),
        addedDate: v.number(),

        // Metadata for admin management
        notes: v.optional(v.string()),
    })
        .index("by_name_state", ["name", "state"])
        .index("by_active", ["isActive"])
        .index("by_shows_per_year", ["showsPerYear"]),

    // Venues within cities
    venues: defineTable({
        cityId: v.id("cities"),
        name: v.string(),
        address: v.string(),
        capacity: v.optional(v.number()),

        // Contact information
        contactPerson: v.optional(v.string()),
        contactEmail: v.optional(v.string()),
        contactPhone: v.optional(v.string()),

        // Venue details
        notes: v.optional(v.string()),
        isActive: v.boolean(),

        // Parking options (multi-select)
        parkingOptions: v.optional(v.array(v.union(
            v.literal("free"),
            v.literal("paid"),
            v.literal("lot"),
            v.literal("street"),
            v.literal("valet"),
            v.literal("rideshare_area")
        ))),
    })
        .index("by_city", ["cityId"])
        .index("by_active", ["isActive"])
        .index("by_name", ["name"]),

    // Shows/Events
    shows: defineTable({
        cityId: v.id("cities"),
        venueId: v.id("venues"),

        // Basic show information
        title: v.string(),
        description: v.optional(v.string()),

        // Date and time
        date: v.number(), // Unix timestamp for the date
        startTime: v.string(), // Time in HH:MM format
        endTime: v.string(), // Time in HH:MM format

        // Ticket information
        ticketPrice: v.object({
            earlyBird: v.optional(v.number()),
            regular: v.number(),
            vip: v.optional(v.number()),
        }),

        // Ticket sale dates
        earlyBirdStartDate: v.optional(v.number()),
        earlyBirdEndDate: v.optional(v.number()),
        regularSaleStartDate: v.number(),
        regularSaleEndDate: v.number(),

        // Show management
        maxAttendees: v.optional(v.number()),
        status: v.union(
            v.literal("coming_soon"),
            v.literal("early_bird"),
            v.literal("regular_sale"),
            v.literal("low_availability"),
            v.literal("sold_out"),
            v.literal("completed"),
            v.literal("cancelled")
        ),

        // Eventbrite integration
        eventbriteEventId: v.optional(v.string()),
        eventbriteUrl: v.optional(v.string()),

        // Metadata
        createdAt: v.number(),
        updatedAt: v.number(),
    })
        .index("by_city", ["cityId"])
        .index("by_venue", ["venueId"])
        .index("by_date", ["date"])
        .index("by_status", ["status"])
        .index("by_eventbrite_id", ["eventbriteEventId"]),

    // Admin users with special permissions
    adminUsers: defineTable({
        userId: v.id("users"),
        adminLevel: v.union(
            v.literal("staff"),
            v.literal("admin"),
            v.literal("super_admin")
        ),

        // Permissions (JSON-like object for flexibility)
        permissions: v.object({
            canManageUsers: v.boolean(),
            canManageShows: v.boolean(),
            canManageApplications: v.boolean(),
            canManageContent: v.boolean(),
            canViewAnalytics: v.boolean(),
            canManageSettings: v.boolean(),
        }),

        // Admin metadata
        createdAt: v.number(),
        createdBy: v.id("users"), // Which admin created this admin user
        isActive: v.boolean(),
    })
        .index("by_user", ["userId"])
        .index("by_admin_level", ["adminLevel"])
        .index("by_active", ["isActive"])
        .index("by_created_by", ["createdBy"]),

    // Artist applications for shows
    artistApplications: defineTable({
        userId: v.id("users"),
        showId: v.id("shows"),

        // Application content
        artistStatement: v.string(),
        medium: v.string(),
        experienceLevel: v.union(
            v.literal("beginner"),
            v.literal("intermediate"),
            v.literal("advanced"),
            v.literal("professional")
        ),
        specialRequirements: v.optional(v.string()),

        // Application status and review
        status: v.union(
            v.literal("pending"),
            v.literal("under_review"),
            v.literal("approved"),
            v.literal("rejected"),
            v.literal("waitlisted")
        ),

        // Review information
        submittedAt: v.number(),
        reviewedAt: v.optional(v.number()),
        reviewedBy: v.optional(v.id("users")),
        adminNotes: v.optional(v.string()),

        // Show participation (if approved)
        boothNumber: v.optional(v.string()),
        setupNotes: v.optional(v.string()),
        checkedIn: v.optional(v.boolean()),
        checkInTime: v.optional(v.number()),
    })
        .index("by_user", ["userId"])
        .index("by_show", ["showId"])
        .index("by_status", ["status"])
        .index("by_submitted_at", ["submittedAt"])
        .index("by_user_show", ["userId", "showId"]),

    // Vendor applications for shows
    vendorApplications: defineTable({
        userId: v.id("users"),
        showId: v.id("shows"),

        // Business information
        businessName: v.string(),
        productDescription: v.string(),
        boothSizeNeeded: v.union(
            v.literal("small"),
            v.literal("medium"),
            v.literal("large"),
            v.literal("custom")
        ),
        electricalNeeded: v.boolean(),
        specialRequirements: v.optional(v.string()),

        // Application status and review
        status: v.union(
            v.literal("pending"),
            v.literal("under_review"),
            v.literal("approved"),
            v.literal("rejected"),
            v.literal("waitlisted")
        ),

        // Review information
        submittedAt: v.number(),
        reviewedAt: v.optional(v.number()),
        reviewedBy: v.optional(v.id("users")),
        adminNotes: v.optional(v.string()),

        // Show participation (if approved)
        boothNumber: v.optional(v.string()),
        setupNotes: v.optional(v.string()),
        checkedIn: v.optional(v.boolean()),
        checkInTime: v.optional(v.number()),
    })
        .index("by_user", ["userId"])
        .index("by_show", ["showId"])
        .index("by_status", ["status"])
        .index("by_submitted_at", ["submittedAt"])
        .index("by_user_show", ["userId", "showId"]),

    // Photos uploaded with artist applications
    applicationPhotos: defineTable({
        artistApplicationId: v.id("artistApplications"),

        // File information
        fileName: v.string(),
        filePath: v.string(), // S3 path or URL
        fileSize: v.number(),
        mimeType: v.string(),

        // Photo details
        caption: v.optional(v.string()),
        uploadOrder: v.number(), // Order in which photos were uploaded (1, 2, 3)

        // Metadata
        uploadedAt: v.number(),

        // Image processing (for future use)
        thumbnailPath: v.optional(v.string()),
        isProcessed: v.optional(v.boolean()),
    })
        .index("by_application", ["artistApplicationId"])
        .index("by_upload_order", ["artistApplicationId", "uploadOrder"])
        .index("by_uploaded_at", ["uploadedAt"]),
});