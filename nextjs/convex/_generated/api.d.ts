/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as adminUsers from "../adminUsers.js";
import type * as applicationPhotos from "../applicationPhotos.js";
import type * as artistApplications from "../artistApplications.js";
import type * as cities from "../cities.js";
import type * as shows from "../shows.js";
import type * as users from "../users.js";
import type * as vendorApplications from "../vendorApplications.js";
import type * as venues from "../venues.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  adminUsers: typeof adminUsers;
  applicationPhotos: typeof applicationPhotos;
  artistApplications: typeof artistApplications;
  cities: typeof cities;
  shows: typeof shows;
  users: typeof users;
  vendorApplications: typeof vendorApplications;
  venues: typeof venues;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
