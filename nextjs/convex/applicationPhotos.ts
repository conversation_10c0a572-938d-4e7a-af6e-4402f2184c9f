import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Add photo to artist application
export const addApplicationPhoto = mutation({
  args: {
    artistApplicationId: v.id("artistApplications"),
    fileName: v.string(),
    filePath: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    caption: v.optional(v.string()),
    uploadOrder: v.number(),
  },
  handler: async (ctx, args) => {
    // Verify application exists
    const application = await ctx.db.get(args.artistApplicationId);
    if (!application) {
      throw new Error("Artist application not found");
    }

    // Check if upload order is valid (1, 2, or 3)
    if (args.uploadOrder < 1 || args.uploadOrder > 3) {
      throw new Error("Upload order must be between 1 and 3");
    }

    // Check if a photo with this upload order already exists
    const existingPhoto = await ctx.db
      .query("applicationPhotos")
      .withIndex("by_upload_order", (q) =>
        q.eq("artistApplicationId", args.artistApplicationId)
          .eq("uploadOrder", args.uploadOrder)
      )
      .unique();

    if (existingPhoto) {
      throw new Error(`Photo ${args.uploadOrder} already exists for this application`);
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(args.mimeType)) {
      throw new Error("Invalid file type. Only JPEG, PNG, and WebP images are allowed");
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (args.fileSize > maxSize) {
      throw new Error("File size too large. Maximum size is 10MB");
    }

    return await ctx.db.insert("applicationPhotos", {
      artistApplicationId: args.artistApplicationId,
      fileName: args.fileName,
      filePath: args.filePath,
      fileSize: args.fileSize,
      mimeType: args.mimeType,
      caption: args.caption,
      uploadOrder: args.uploadOrder,
      uploadedAt: Date.now(),
      isProcessed: false,
    });
  },
});

// Get photos for an artist application
export const getApplicationPhotos = query({
  args: { artistApplicationId: v.id("artistApplications") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("applicationPhotos")
      .withIndex("by_application", (q) => q.eq("artistApplicationId", args.artistApplicationId))
      .order("asc")
      .collect();
  },
});

// Get photo by ID
export const getApplicationPhotoById = query({
  args: { photoId: v.id("applicationPhotos") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.photoId);
  },
});

// Update photo caption
export const updatePhotoCaption = mutation({
  args: {
    photoId: v.id("applicationPhotos"),
    caption: v.string(),
    userId: v.id("users"), // For permission checking
  },
  handler: async (ctx, args) => {
    const photo = await ctx.db.get(args.photoId);
    if (!photo) {
      throw new Error("Photo not found");
    }

    // Check if user owns the application
    const application = await ctx.db.get(photo.artistApplicationId);
    if (!application) {
      throw new Error("Application not found");
    }

    if (application.userId !== args.userId) {
      throw new Error("You can only update photos for your own applications");
    }

    // Only allow updates for pending applications
    if (application.status !== "pending") {
      throw new Error("You can only update photos for pending applications");
    }

    await ctx.db.patch(args.photoId, {
      caption: args.caption,
    });
  },
});

// Delete application photo
export const deleteApplicationPhoto = mutation({
  args: {
    photoId: v.id("applicationPhotos"),
    userId: v.id("users"), // For permission checking
  },
  handler: async (ctx, args) => {
    const photo = await ctx.db.get(args.photoId);
    if (!photo) {
      throw new Error("Photo not found");
    }

    // Check if user owns the application
    const application = await ctx.db.get(photo.artistApplicationId);
    if (!application) {
      throw new Error("Application not found");
    }

    if (application.userId !== args.userId) {
      throw new Error("You can only delete photos for your own applications");
    }

    // Only allow deletion for pending applications
    if (application.status !== "pending") {
      throw new Error("You can only delete photos for pending applications");
    }

    await ctx.db.delete(args.photoId);
  },
});

// Replace application photo (delete old, add new)
export const replaceApplicationPhoto = mutation({
  args: {
    artistApplicationId: v.id("artistApplications"),
    uploadOrder: v.number(),
    fileName: v.string(),
    filePath: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    caption: v.optional(v.string()),
    userId: v.id("users"), // For permission checking
  },
  handler: async (ctx, args) => {
    // Verify application exists and user owns it
    const application = await ctx.db.get(args.artistApplicationId);
    if (!application) {
      throw new Error("Artist application not found");
    }

    if (application.userId !== args.userId) {
      throw new Error("You can only update photos for your own applications");
    }

    // Only allow updates for pending applications
    if (application.status !== "pending") {
      throw new Error("You can only update photos for pending applications");
    }

    // Find existing photo with this upload order
    const existingPhoto = await ctx.db
      .query("applicationPhotos")
      .withIndex("by_upload_order", (q) =>
        q.eq("artistApplicationId", args.artistApplicationId)
          .eq("uploadOrder", args.uploadOrder)
      )
      .unique();

    // Delete existing photo if it exists
    if (existingPhoto) {
      await ctx.db.delete(existingPhoto._id);
    }

    // Validate new file
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(args.mimeType)) {
      throw new Error("Invalid file type. Only JPEG, PNG, and WebP images are allowed");
    }

    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (args.fileSize > maxSize) {
      throw new Error("File size too large. Maximum size is 10MB");
    }

    // Add new photo
    return await ctx.db.insert("applicationPhotos", {
      artistApplicationId: args.artistApplicationId,
      fileName: args.fileName,
      filePath: args.filePath,
      fileSize: args.fileSize,
      mimeType: args.mimeType,
      caption: args.caption,
      uploadOrder: args.uploadOrder,
      uploadedAt: Date.now(),
      isProcessed: false,
    });
  },
});

// Mark photo as processed (for image processing workflows)
export const markPhotoAsProcessed = mutation({
  args: {
    photoId: v.id("applicationPhotos"),
    thumbnailPath: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.photoId, {
      isProcessed: true,
      thumbnailPath: args.thumbnailPath,
    });
  },
});

// Get application photos count for an application
export const getApplicationPhotosCount = query({
  args: { artistApplicationId: v.id("artistApplications") },
  handler: async (ctx, args) => {
    const photos = await ctx.db
      .query("applicationPhotos")
      .withIndex("by_application", (q) => q.eq("artistApplicationId", args.artistApplicationId))
      .collect();

    return {
      total: photos.length,
      byOrder: {
        1: photos.find(p => p.uploadOrder === 1) ? 1 : 0,
        2: photos.find(p => p.uploadOrder === 2) ? 1 : 0,
        3: photos.find(p => p.uploadOrder === 3) ? 1 : 0,
      },
    };
  },
});

// Get all photos for admin review
export const getAllApplicationPhotos = query({
  args: {
    limit: v.optional(v.number()),
    showId: v.optional(v.id("shows")),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;

    let photos = await ctx.db
      .query("applicationPhotos")
      .order("desc")
      .take(limit);

    // If showId is provided, filter by show
    if (args.showId) {
      const photosWithApplications = await Promise.all(
        photos.map(async (photo) => {
          const application = await ctx.db.get(photo.artistApplicationId);
          return { photo, application };
        })
      );

      photos = photosWithApplications
        .filter(({ application }) => application?.showId === args.showId)
        .map(({ photo }) => photo);
    }

    // Get application and user details for each photo
    const photosWithDetails = await Promise.all(
      photos.map(async (photo) => {
        const application = await ctx.db.get(photo.artistApplicationId);
        const user = application ? await ctx.db.get(application.userId) : null;

        return {
          ...photo,
          application,
          user,
        };
      })
    );

    return photosWithDetails;
  },
});
