import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create admin user
export const createAdminUser = mutation({
  args: {
    userId: v.id("users"),
    adminLevel: v.union(
      v.literal("staff"),
      v.literal("admin"),
      v.literal("super_admin")
    ),
    permissions: v.object({
      canManageUsers: v.boolean(),
      canManageShows: v.boolean(),
      canManageApplications: v.boolean(),
      canManageContent: v.boolean(),
      canViewAnalytics: v.boolean(),
      canManageSettings: v.boolean(),
    }),
    createdBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Check if user exists
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if user is already an admin
    const existingAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (existingAdmin) {
      throw new Error("User is already an admin");
    }

    // Create admin user record
    const adminUserId = await ctx.db.insert("adminUsers", {
      userId: args.userId,
      adminLevel: args.adminLevel,
      permissions: args.permissions,
      createdAt: Date.now(),
      createdBy: args.createdBy,
      isActive: true,
    });

    // Update user role
    await ctx.db.patch(args.userId, {
      role: args.adminLevel === "staff" ? "staff" : "admin",
      updatedAt: Date.now(),
    });

    return adminUserId;
  },
});

// Get admin user by user ID
export const getAdminUserByUserId = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();
  },
});

// Get admin user with user details
export const getAdminUserWithDetails = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser) return null;

    const user = await ctx.db.get(adminUser.userId);
    const createdByUser = await ctx.db.get(adminUser.createdBy);

    return {
      ...adminUser,
      user,
      createdByUser,
    };
  },
});

// Get all admin users
export const getAllAdminUsers = query({
  args: {
    adminLevel: v.optional(v.union(
      v.literal("staff"),
      v.literal("admin"),
      v.literal("super_admin")
    )),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let adminUsers;

    if (args.adminLevel) {
      adminUsers = await ctx.db
        .query("adminUsers")
        .withIndex("by_admin_level", (q) =>
          q.eq("adminLevel", args.adminLevel!)
        )
        .collect();
    } else {
      adminUsers = await ctx.db
        .query("adminUsers")
        .collect();
    }

    // Filter by active status if specified
    const filteredAdminUsers = args.isActive !== undefined
      ? adminUsers.filter(admin => admin.isActive === args.isActive)
      : adminUsers;

    // Get user details for each admin
    const adminUsersWithDetails = await Promise.all(
      filteredAdminUsers.map(async (adminUser) => {
        const user = await ctx.db.get(adminUser.userId);
        const createdByUser = await ctx.db.get(adminUser.createdBy);

        return {
          ...adminUser,
          user,
          createdByUser,
        };
      })
    );

    return adminUsersWithDetails;
  },
});

// Update admin user permissions
export const updateAdminUserPermissions = mutation({
  args: {
    userId: v.id("users"),
    permissions: v.object({
      canManageUsers: v.boolean(),
      canManageShows: v.boolean(),
      canManageApplications: v.boolean(),
      canManageContent: v.boolean(),
      canViewAnalytics: v.boolean(),
      canManageSettings: v.boolean(),
    }),
  },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    await ctx.db.patch(adminUser._id, {
      permissions: args.permissions,
    });
  },
});

// Update admin level
export const updateAdminLevel = mutation({
  args: {
    userId: v.id("users"),
    adminLevel: v.union(
      v.literal("staff"),
      v.literal("admin"),
      v.literal("super_admin")
    ),
  },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    // Update admin level
    await ctx.db.patch(adminUser._id, {
      adminLevel: args.adminLevel,
    });

    // Update user role
    await ctx.db.patch(args.userId, {
      role: args.adminLevel === "staff" ? "staff" : "admin",
      updatedAt: Date.now(),
    });
  },
});

// Deactivate admin user
export const deactivateAdminUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    // Deactivate admin user
    await ctx.db.patch(adminUser._id, {
      isActive: false,
    });

    // Revert user role to regular user
    await ctx.db.patch(args.userId, {
      role: "user",
      updatedAt: Date.now(),
    });
  },
});

// Reactivate admin user
export const reactivateAdminUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    // Reactivate admin user
    await ctx.db.patch(adminUser._id, {
      isActive: true,
    });

    // Restore user role
    await ctx.db.patch(args.userId, {
      role: adminUser.adminLevel === "staff" ? "staff" : "admin",
      updatedAt: Date.now(),
    });
  },
});

// Check if user has specific permission
export const checkUserPermission = query({
  args: {
    userId: v.id("users"),
    permission: v.union(
      v.literal("canManageUsers"),
      v.literal("canManageShows"),
      v.literal("canManageApplications"),
      v.literal("canManageContent"),
      v.literal("canViewAnalytics"),
      v.literal("canManageSettings")
    ),
  },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser || !adminUser.isActive) {
      return false;
    }

    return adminUser.permissions[args.permission];
  },
});

// Get user permissions
export const getUserPermissions = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!adminUser || !adminUser.isActive) {
      return null;
    }

    return {
      adminLevel: adminUser.adminLevel,
      permissions: adminUser.permissions,
    };
  },
});
