import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Submit artist application
export const submitArtistApplication = mutation({
  args: {
    userId: v.id("users"),
    showId: v.id("shows"),
    artistStatement: v.string(),
    medium: v.string(),
    experienceLevel: v.union(
      v.literal("beginner"),
      v.literal("intermediate"),
      v.literal("advanced"),
      v.literal("professional")
    ),
    specialRequirements: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user exists
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if show exists
    const show = await ctx.db.get(args.showId);
    if (!show) {
      throw new Error("Show not found");
    }

    // Check if user already has an application for this show
    const existingApplication = await ctx.db
      .query("artistApplications")
      .withIndex("by_user_show", (q) =>
        q.eq("userId", args.userId).eq("showId", args.showId)
      )
      .unique();

    if (existingApplication) {
      throw new Error("You have already submitted an application for this show");
    }

    // Create application
    return await ctx.db.insert("artistApplications", {
      userId: args.userId,
      showId: args.showId,
      artistStatement: args.artistStatement,
      medium: args.medium,
      experienceLevel: args.experienceLevel,
      specialRequirements: args.specialRequirements,
      status: "pending",
      submittedAt: Date.now(),
    });
  },
});

// Get artist application by ID
export const getArtistApplicationById = query({
  args: { applicationId: v.id("artistApplications") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.applicationId);
  },
});

// Get artist application with details (user, show, photos)
export const getArtistApplicationWithDetails = query({
  args: { applicationId: v.id("artistApplications") },
  handler: async (ctx, args) => {
    const application = await ctx.db.get(args.applicationId);
    if (!application) return null;

    const user = await ctx.db.get(application.userId);
    const show = await ctx.db.get(application.showId);

    // Get application photos
    const photos = await ctx.db
      .query("applicationPhotos")
      .withIndex("by_application", (q) => q.eq("artistApplicationId", args.applicationId))
      .order("asc")
      .collect();

    return {
      ...application,
      user,
      show,
      photos,
    };
  },
});

// Get user's artist applications
export const getUserArtistApplications = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    const applications = await ctx.db
      .query("artistApplications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);

    // Get show details for each application
    const applicationsWithShows = await Promise.all(
      applications.map(async (application) => {
        const show = await ctx.db.get(application.showId);
        return {
          ...application,
          show,
        };
      })
    );

    return applicationsWithShows;
  },
});

// Get applications for a show (admin function)
export const getShowArtistApplications = query({
  args: {
    showId: v.id("shows"),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("waitlisted")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;

    let applications;

    if (args.status) {
      // Query by status first, then filter by show
      const allByStatus = await ctx.db
        .query("artistApplications")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc")
        .collect();

      applications = allByStatus
        .filter(app => app.showId === args.showId)
        .slice(0, limit);
    } else {
      // Query directly by show
      applications = await ctx.db
        .query("artistApplications")
        .withIndex("by_show", (q) => q.eq("showId", args.showId))
        .order("desc")
        .take(limit);
    }

    // No need for additional filtering since we already filtered above
    const filteredApplications = applications;

    // Get user details for each application
    const applicationsWithUsers = await Promise.all(
      filteredApplications.map(async (application) => {
        const user = await ctx.db.get(application.userId);
        const photos = await ctx.db
          .query("applicationPhotos")
          .withIndex("by_application", (q) => q.eq("artistApplicationId", application._id))
          .collect();

        return {
          ...application,
          user,
          photos,
        };
      })
    );

    return applicationsWithUsers;
  },
});

// Update application status (admin function)
export const updateApplicationStatus = mutation({
  args: {
    applicationId: v.id("artistApplications"),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("waitlisted")
    ),
    reviewedBy: v.id("users"),
    adminNotes: v.optional(v.string()),
    boothNumber: v.optional(v.string()),
    setupNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { applicationId, ...updates } = args;

    await ctx.db.patch(applicationId, {
      status: updates.status,
      reviewedAt: Date.now(),
      reviewedBy: updates.reviewedBy,
      adminNotes: updates.adminNotes,
      boothNumber: updates.boothNumber,
      setupNotes: updates.setupNotes,
    });

    // If approved, potentially update user role to artist
    if (updates.status === "approved") {
      const application = await ctx.db.get(applicationId);
      if (application) {
        const user = await ctx.db.get(application.userId);
        if (user && user.role === "user") {
          await ctx.db.patch(application.userId, {
            role: "artist",
            updatedAt: Date.now(),
          });
        }
      }
    }
  },
});

// Check in artist for show
export const checkInArtist = mutation({
  args: {
    applicationId: v.id("artistApplications"),
    checkedInBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    const application = await ctx.db.get(args.applicationId);
    if (!application) {
      throw new Error("Application not found");
    }

    if (application.status !== "approved") {
      throw new Error("Only approved applications can be checked in");
    }

    await ctx.db.patch(args.applicationId, {
      checkedIn: true,
      checkInTime: Date.now(),
    });
  },
});

// Get application statistics for a show
export const getShowApplicationStats = query({
  args: { showId: v.id("shows") },
  handler: async (ctx, args) => {
    const applications = await ctx.db
      .query("artistApplications")
      .withIndex("by_show", (q) => q.eq("showId", args.showId))
      .collect();

    const stats = {
      total: applications.length,
      pending: applications.filter(app => app.status === "pending").length,
      underReview: applications.filter(app => app.status === "under_review").length,
      approved: applications.filter(app => app.status === "approved").length,
      rejected: applications.filter(app => app.status === "rejected").length,
      waitlisted: applications.filter(app => app.status === "waitlisted").length,
      checkedIn: applications.filter(app => app.checkedIn === true).length,
    };

    return stats;
  },
});

// Delete application (user can only delete pending applications)
export const deleteArtistApplication = mutation({
  args: {
    applicationId: v.id("artistApplications"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const application = await ctx.db.get(args.applicationId);
    if (!application) {
      throw new Error("Application not found");
    }

    // Check if user owns the application
    if (application.userId !== args.userId) {
      throw new Error("You can only delete your own applications");
    }

    // Check if application is still pending
    if (application.status !== "pending") {
      throw new Error("You can only delete pending applications");
    }

    // Delete associated photos first
    const photos = await ctx.db
      .query("applicationPhotos")
      .withIndex("by_application", (q) => q.eq("artistApplicationId", args.applicationId))
      .collect();

    for (const photo of photos) {
      await ctx.db.delete(photo._id);
    }

    // Delete the application
    await ctx.db.delete(args.applicationId);
  },
});
