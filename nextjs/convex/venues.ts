import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new venue
export const createVenue = mutation({
  args: {
    cityId: v.id("cities"),
    name: v.string(),
    address: v.string(),
    capacity: v.optional(v.number()),
    contactPerson: v.optional(v.string()),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    notes: v.optional(v.string()),
    parkingOptions: v.optional(v.array(v.union(
      v.literal("free"),
      v.literal("paid"),
      v.literal("lot"),
      v.literal("street"),
      v.literal("valet"),
      v.literal("rideshare_area")
    ))),
  },
  handler: async (ctx, args) => {
    // Verify city exists
    const city = await ctx.db.get(args.cityId);
    if (!city) {
      throw new Error("City not found");
    }

    return await ctx.db.insert("venues", {
      cityId: args.cityId,
      name: args.name,
      address: args.address,
      capacity: args.capacity,
      contactPerson: args.contactPerson,
      contactEmail: args.contactEmail,
      contactPhone: args.contactPhone,
      notes: args.notes,
      isActive: true,
      parkingOptions: args.parkingOptions,
    });
  },
});

// Get venues by city
export const getVenuesByCity = query({
  args: { cityId: v.id("cities") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("venues")
      .withIndex("by_city", (q) => q.eq("cityId", args.cityId))
      .collect();
  },
});

// Get active venues by city
export const getActiveVenuesByCity = query({
  args: { cityId: v.id("cities") },
  handler: async (ctx, args) => {
    const venues = await ctx.db
      .query("venues")
      .withIndex("by_city", (q) => q.eq("cityId", args.cityId))
      .collect();

    return venues.filter(venue => venue.isActive);
  },
});

// Get all venues (admin function)
export const getAllVenues = query({
  handler: async (ctx) => {
    return await ctx.db.query("venues").collect();
  },
});

// Get venue by ID
export const getVenueById = query({
  args: { venueId: v.id("venues") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.venueId);
  },
});

// Get venue with city information
export const getVenueWithCity = query({
  args: { venueId: v.id("venues") },
  handler: async (ctx, args) => {
    const venue = await ctx.db.get(args.venueId);
    if (!venue) return null;

    const city = await ctx.db.get(venue.cityId);

    return {
      ...venue,
      city,
    };
  },
});

// Update venue
export const updateVenue = mutation({
  args: {
    venueId: v.id("venues"),
    name: v.optional(v.string()),
    address: v.optional(v.string()),
    capacity: v.optional(v.number()),
    contactPerson: v.optional(v.string()),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    parkingOptions: v.optional(v.array(v.union(
      v.literal("free"),
      v.literal("paid"),
      v.literal("lot"),
      v.literal("street"),
      v.literal("valet"),
      v.literal("rideshare_area")
    ))),
  },
  handler: async (ctx, args) => {
    const { venueId, ...updates } = args;

    // Filter out undefined values
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(venueId, filteredUpdates);
  },
});

// Delete venue (admin function)
export const deleteVenue = mutation({
  args: { venueId: v.id("venues") },
  handler: async (ctx, args) => {
    // Check if venue has shows
    const shows = await ctx.db
      .query("shows")
      .withIndex("by_venue", (q) => q.eq("venueId", args.venueId))
      .collect();

    if (shows.length > 0) {
      throw new Error("Cannot delete venue with existing shows");
    }

    await ctx.db.delete(args.venueId);
  },
});

// Get venues with show counts
export const getVenuesWithShowCounts = query({
  args: { cityId: v.optional(v.id("cities")) },
  handler: async (ctx, args) => {
    let venues;

    if (args.cityId) {
      venues = await ctx.db
        .query("venues")
        .withIndex("by_city", (q) => q.eq("cityId", args.cityId!))
        .collect();
    } else {
      venues = await ctx.db.query("venues").collect();
    }

    const venuesWithCounts = await Promise.all(
      venues.map(async (venue) => {
        const shows = await ctx.db
          .query("shows")
          .withIndex("by_venue", (q) => q.eq("venueId", venue._id))
          .collect();

        const upcomingShows = shows.filter(s => s.date > Date.now());
        const pastShows = shows.filter(s => s.date <= Date.now());

        return {
          ...venue,
          totalShows: shows.length,
          upcomingShows: upcomingShows.length,
          pastShows: pastShows.length,
        };
      })
    );

    return venuesWithCounts;
  },
});

// Search venues by name
export const searchVenues = query({
  args: {
    searchTerm: v.string(),
    cityId: v.optional(v.id("cities")),
  },
  handler: async (ctx, args) => {
    let venues;

    if (args.cityId) {
      venues = await ctx.db
        .query("venues")
        .withIndex("by_city", (q) => q.eq("cityId", args.cityId!))
        .collect();
    } else {
      venues = await ctx.db.query("venues").collect();
    }

    const searchTerm = args.searchTerm.toLowerCase();

    return venues.filter((venue) => {
      return venue.name.toLowerCase().includes(searchTerm) ||
        venue.address.toLowerCase().includes(searchTerm);
    });
  },
});
