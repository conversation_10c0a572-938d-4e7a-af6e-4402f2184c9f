self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f64842629f116d912e768dfe126e5de1f001ffdea\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"collectKeylessMetadata\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"rsc\",\n        \"app/artists/page\": \"rsc\",\n        \"app/contact/page\": \"rsc\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"rsc\",\n        \"app/faq/page\": \"rsc\",\n        \"app/page\": \"rsc\",\n        \"app/shows/page\": \"rsc\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\",\n      \"exportedName\": \"collectKeylessMetadata\"\n    },\n    \"7fcc05179a8be791535b2046c2236ac5b31bfd6d8e\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"formatMetadataHeaders\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"rsc\",\n        \"app/artists/page\": \"rsc\",\n        \"app/contact/page\": \"rsc\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"rsc\",\n        \"app/faq/page\": \"rsc\",\n        \"app/page\": \"rsc\",\n        \"app/shows/page\": \"rsc\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js\",\n      \"exportedName\": \"formatMetadataHeaders\"\n    },\n    \"7f541f6a6294e686d62358bb6bcf6fa2764fca6bad\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/artists/page\": \"action-browser\",\n        \"app/contact/page\": \"action-browser\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"action-browser\",\n        \"app/faq/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/shows/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"detectKeylessEnvDriftAction\"\n    },\n    \"7f95b6e38c656ae49d547cade07a98b8a57492fd37\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/artists/page\": \"action-browser\",\n        \"app/contact/page\": \"action-browser\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"action-browser\",\n        \"app/faq/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/shows/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"deleteKeylessAction\"\n    },\n    \"7fad64fa5ff530b7a2a61ee4a3992a2b9d1744c457\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/artists/page\": \"action-browser\",\n        \"app/contact/page\": \"action-browser\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"action-browser\",\n        \"app/faq/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/shows/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"syncKeylessConfigAction\"\n    },\n    \"7ffbe2201daca55456363b459da503f74da9069bab\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/artists/page\": \"action-browser\",\n        \"app/contact/page\": \"action-browser\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"action-browser\",\n        \"app/faq/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/shows/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"createOrReadKeylessAction\"\n    },\n    \"7f3c9e2a440a1e50f9c2da54ef392224ff3d4d400e\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/artists/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/artists/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/events/dallas-tx-2025-09-18-19/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/events/dallas-tx-2025-09-18-19/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/faq/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/faq/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/shows/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/shows/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/server/keyless-custom-headers.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/artists/page\": \"action-browser\",\n        \"app/contact/page\": \"action-browser\",\n        \"app/events/dallas-tx-2025-09-18-19/page\": \"action-browser\",\n        \"app/faq/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/shows/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\",\n      \"exportedName\": \"invalidateCacheAction\"\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"i9YfwTnb2I47TWtDFHUYViaQZZB7rlo9471PxpYm7JQ=\"\n}"