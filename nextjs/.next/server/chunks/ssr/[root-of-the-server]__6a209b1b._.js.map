{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqHeader.tsx"], "sourcesContent": ["export default function FaqHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Frequently Asked Questions</h1>\n      <p>\n        Everything you need to know about Chocolate & Art Show Dallas. Can&apos;t find what you&apos;re looking for? Contact us!\n      </p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAKT", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqSections.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/FaqSections.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/FaqSections.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqSections.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/FaqSections.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/FaqSections.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqCta.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function FaqCta() {\n  return (\n    <section className=\"faq-cta\">\n      <h2>Still Have Questions?</h2>\n      <p>Can&apos;t find the answer you&apos;re looking for? We&apos;re here to help!</p>\n      <Link href=\"/contact\" className=\"btn btn-primary\">\n        Contact Us\n      </Link>\n      <Link href=\"/events/dallas-tx-2025-09-18-19/\" className=\"btn btn-secondary\">\n        Get Tickets\n      </Link>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;0BACH,8OAAC,uKAAI;gBAAC,MAAK;gBAAW,WAAU;0BAAkB;;;;;;0BAGlD,8OAAC,uKAAI;gBAAC,MAAK;gBAAmC,WAAU;0BAAoB;;;;;;;;;;;;AAKlF", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/HeroSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/HeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/HeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/HeroSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/HeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/HeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/RgbWall.tsx"], "sourcesContent": ["export default function RgbWall() {\n  return (\n    <section className=\"rgb-wall\" aria-label=\"Intermission banner\">\n      <div className=\"rgb-hero\">\n        <span className=\"rgb-word\" data-txt=\"ART\">ART</span>\n        <span className=\"rgb-plus\">+</span>\n        <span className=\"rgb-word\" data-txt=\"MUSIC\">MUSIC</span>\n        <span className=\"rgb-plus\">+</span>\n        <span className=\"rgb-word\" data-txt=\"CHOCOLATE\">CHOCOLATE</span>\n        <span className=\"rgb-eq\">=</span>\n        <span className=\"rgb-word\" data-txt=\"DALLAS\">DALLAS</span>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAW,cAAW;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAM;;;;;;8BAC1C,8OAAC;oBAAK,WAAU;8BAAW;;;;;;8BAC3B,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAQ;;;;;;8BAC5C,8OAAC;oBAAK,WAAU;8BAAW;;;;;;8BAC3B,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAY;;;;;;8BAChD,8OAAC;oBAAK,WAAU;8BAAS;;;;;;8BACzB,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAS;;;;;;;;;;;;;;;;;AAIrD", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/AboutSection.tsx"], "sourcesContent": ["export default function AboutSection() {\n  return (\n    <section id=\"about\" className=\"about-section\" aria-label=\"About the show\">\n      <div className=\"container\">\n        <h2>About Chocolate & Art Show</h2>\n        <p>\n          An immersive experience celebrating art, music, and artisan chocolate. \n          Join us for two unforgettable nights in Dallas.\n        </p>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;QAAgB,cAAW;kBACvD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;8BAAE;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/CtaBar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/CtaBar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/CtaBar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/CtaBar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/CtaBar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/CtaBar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/GallerySection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/GallerySection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/GallerySection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/GallerySection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/GallerySection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/GallerySection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ArtistsHeader.tsx"], "sourcesContent": ["export default function ArtistsHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Artists & Creators</h1>\n      <p>\n        Join <PERSON>&apos;s most immersive art experience. Apply to showcase your work,\n        perform, or sell at Chocolate & Art Show.\n      </p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ApplicationGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ApplicationGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ApplicationGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ApplicationGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ApplicationGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ApplicationGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/InfoSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/InfoSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/InfoSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/InfoSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/InfoSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/InfoSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactHeader.tsx"], "sourcesContent": ["export default function ContactHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Contact</h1>\n      <p>Get in touch with questions about the event, artist submissions, and vendor applications.</p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ContactGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ContactGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ContactGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ContactGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/TransportationSection.tsx"], "sourcesContent": ["import { TruckIcon } from '@heroicons/react/24/outline';\n\nexport default function TransportationSection() {\n  return (\n    <section className=\"transportation\">\n      <h2>\n        <TruckIcon className=\"section-icon\" />\n        Getting There\n      </h2>\n      <div className=\"transport-grid\">\n        <div className=\"transport-option bwhc-card\">\n          <h3>Driving</h3>\n          <p>Parking is available at the venue. We recommend arriving early as spaces are limited.</p>\n        </div>\n\n        <div className=\"transport-option bwhc-card\">\n          <h3>Rideshare</h3>\n          <p>\n            Uber and Lyft are recommended for convenience. The venue is easily accessible for pickup and drop-off.\n          </p>\n        </div>\n\n        <div className=\"transport-option bwhc-card\">\n          <h3>Public Transit</h3>\n          <p>Check local Dallas transit options for routes to the venue area.</p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;;kCACC,8OAAC,8NAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAGxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ImportantNotesSection.tsx"], "sourcesContent": ["import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';\n\nexport default function ImportantNotesSection() {\n  return (\n    <section className=\"important-notes\">\n      <h2>\n        <ExclamationTriangleIcon className=\"section-icon\" />\n        Important Information\n      </h2>\n      <div className=\"notes-grid\">\n        <div className=\"note-card bwhc-card\">\n          <h3>ID Required</h3>\n          <p>This is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.</p>\n        </div>\n\n        <div className=\"note-card bwhc-card\">\n          <h3>No Re-entry</h3>\n          <p>Once you leave the venue, re-entry is not permitted. Please plan accordingly.</p>\n        </div>\n\n        <div className=\"note-card bwhc-card\">\n          <h3>Capacity Limited</h3>\n          <p>This is an intimate event with limited capacity. Advance ticket purchase is strongly recommended.</p>\n        </div>\n\n        <div className=\"note-card bwhc-card\">\n          <h3>Dress Code</h3>\n          <p>\n            Come as you are! We encourage creative expression and comfortable attire for an evening of art and music.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;;kCACC,8OAAC,wQAAuB;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAGtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactCta.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function ContactCta() {\n  return (\n    <section className=\"contact-cta\">\n      <h2>Ready to Join <PERSON>?</h2>\n      <p>Secure your spot at Dallas&apos;s most immersive art experience.</p>\n      <div className=\"cta-buttons\">\n        <Link href=\"/events/dallas-tx-2025-09-18-19/\" className=\"btn btn-primary\">\n          Get Tickets\n        </Link>\n        <Link href=\"/artists\" className=\"btn btn-secondary\">\n          Apply to Participate\n        </Link>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uKAAI;wBAAC,MAAK;wBAAmC,WAAU;kCAAkB;;;;;;kCAG1E,8OAAC,uKAAI;wBAAC,MAAK;wBAAW,WAAU;kCAAoB;;;;;;;;;;;;;;;;;;AAM5D", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventHeader.tsx"], "sourcesContent": ["export default function EventHeader() {\n  return (\n    <section className=\"event-header\">\n      <div className=\"container\">\n        <h1>Chocolate & Art Show</h1>\n        <div className=\"event-subtitle\">Dallas, Texas</div>\n        <div className=\"event-description\">\n          An immersive experience celebrating art, live music, body painting, and artisan chocolate.\n          Two unforgettable nights at Lofty Spaces in the heart of Dallas.\n        </div>\n\n        {/* Cialdini-framed ticket CTAs */}\n        <div className=\"ticket-section\">\n          <a \n            href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\"\n            className=\"ticket-btn\" \n            target=\"_blank\" \n            rel=\"noopener\"\n          >\n            Lock your spot — Thursday & Friday only, 21+\n          </a>\n        </div>\n        <div className=\"urgency-text\">\n          Limited capacity • Advance purchase strongly recommended\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;oBAAI,WAAU;8BAAiB;;;;;;8BAChC,8OAAC;oBAAI,WAAU;8BAAoB;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,QAAO;wBACP,KAAI;kCACL;;;;;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BAAe;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventDetails.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventDetails.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventDetails.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventDetails.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventDetails.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventDetails.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventCta.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventCta.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventCta.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventCta.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventCta.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventCta.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1019, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsHeader.tsx"], "sourcesContent": ["export default function EventsHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Shows & Cities</h1>\n      <p>Immersive art experiences coming to cities across the country. Find your city and secure your spot.</p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventsGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventsGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventsGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventsGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/index.ts"], "sourcesContent": ["export { default as HeroSection } from './HeroSection';\nexport { default as RgbWall } from './RgbWall';\nexport { default as AboutSection } from './AboutSection';\nexport { default as CtaBar } from './CtaBar';\nexport { default as GallerySection } from './GallerySection';\nexport { default as ArtistsHeader } from './ArtistsHeader';\nexport { default as ApplicationGrid } from './ApplicationGrid';\nexport { default as InfoSection } from './InfoSection';\nexport { default as ContactHeader } from './ContactHeader';\nexport { default as ContactGrid } from './ContactGrid';\nexport { default as TransportationSection } from './TransportationSection';\nexport { default as ImportantNotesSection } from './ImportantNotesSection';\nexport { default as ContactCta } from './ContactCta';\nexport { default as FaqHeader } from './FaqHeader';\nexport { default as FaqSections } from './FaqSections';\nexport { default as FaqCta } from './FaqCta';\nexport { default as EventHeader } from './EventHeader';\nexport { default as EventDetails } from './EventDetails';\nexport { default as EventCta } from './EventCta';\nexport { default as EventsHeader } from './EventsHeader';\nexport { default as EventsGrid } from './EventsGrid';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/FaqStructuredData.tsx"], "sourcesContent": ["export default function FaqStructuredData() {\n  const faqSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"FAQPage\",\n    \"name\": \"Chocolate & Art Show FAQ\",\n    \"description\": \"Frequently asked questions about Chocolate & Art Show Dallas - event details, tickets, venue information, and more.\",\n    \"url\": \"https://chocolateandartshow.com/faq\",\n    \"mainEntity\": [\n      {\n        \"@type\": \"Question\",\n        \"name\": \"What is Chocolate & Art Show?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"Chocolate & Art Show is an immersive 21+ experience combining visual art, live music, body painting, and artisan chocolate. It's a curated event featuring local Dallas artists, live performances, and interactive art experiences in a gallery-style setting.\"\n        }\n      },\n      {\n        \"@type\": \"Question\",\n        \"name\": \"When and where is the Dallas event?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"The Dallas event takes place September 18-19, 2025, from 7:00 PM to 1:00 AM both nights at Lofty Spaces (816 Montgomery St, Dallas, TX 75215).\"\n        }\n      },\n      {\n        \"@type\": \"Question\",\n        \"name\": \"How much are tickets?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"Early bird tickets (before September 17): $15 per night or $30 for both nights. Regular admission (after September 17 or at the door): $25 per night. VIP packages: $55 per night, including priority entry, complimentary drinks, and exclusive artist meet & greets. All prices subject to additional taxes and fees.\"\n        }\n      },\n      {\n        \"@type\": \"Question\",\n        \"name\": \"Is this event 21+ only?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"Yes, this is a 21+ event. Valid government-issued photo ID is required for entry.\"\n        }\n      }\n    ]\n  };\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(faqSchema),\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,MAAM,YAAY;QAChB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,cAAc;YACZ;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;SACD;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/app/faq/page.tsx"], "sourcesContent": ["import type { Metadata } from 'next';\nimport FaqHeader from '@/components/sections/FaqHeader';\nimport FaqSections from '@/components/sections/FaqSections';\nimport FaqCta from '@/components/sections/FaqCta';\nimport { CtaBar } from '@/components/sections';\nimport FaqStructuredData from '@/components/FaqStructuredData';\n\nexport const metadata: Metadata = {\n  title: 'FAQ — Chocolate & Art Show Dallas | Frequently Asked Questions',\n  description: 'Get answers to frequently asked questions about Chocolate & Art Show Dallas. Age requirements, tickets, venue info, and more.',\n  keywords: ['FAQ', 'Chocolate Art Show', 'Dallas events', 'tickets', '21+ event', 'venue information', 'art show questions'],\n  openGraph: {\n    title: 'FAQ — Chocolate & Art Show Dallas | Frequently Asked Questions',\n    description: 'Get answers to frequently asked questions about Chocolate & Art Show Dallas. Age requirements, tickets, venue info, and more.',\n    type: 'website',\n    url: 'https://chocolateandartshow.com/faq',\n    images: [\n      {\n        url: 'https://chocolateandartshow.com/images/gallery/hero-image.jpg',\n        alt: 'Chocolate & Art Show Dallas FAQ',\n      },\n    ],\n    siteName: 'Chocolate & Art Show',\n  },\n  twitter: {\n    card: 'summary',\n    site: '@chocolateandartshow',\n    title: 'FAQ — Chocolate & Art Show Dallas',\n    description: 'Get answers to frequently asked questions about Chocolate & Art Show Dallas. Age requirements, tickets, venue info, and more.',\n    images: ['https://chocolateandartshow.com/images/gallery/hero-image.jpg'],\n  },\n};\n\nexport default function FaqPage() {\n  return (\n    <>\n      <FaqStructuredData />\n      <main id=\"main-content\">\n        <div className=\"container\">\n          <FaqHeader />\n          <FaqSections />\n          <FaqCta />\n          <CtaBar />\n        </div>\n      </main>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAO;QAAsB;QAAiB;QAAW;QAAa;QAAqB;KAAqB;IAC3H,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,KAAK;QACL,QAAQ;YACN;gBACE,KAAK;gBACL,KAAK;YACP;SACD;QACD,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgE;IAC3E;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,2IAAiB;;;;;0BAClB,8OAAC;gBAAK,IAAG;0BACP,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,+IAAS;;;;;sCACV,8OAAC,iJAAW;;;;;sCACZ,8OAAC,4IAAM;;;;;sCACP,8OAAC,gLAAM;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}