{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/ui/WelcomeBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useUser } from '@clerk/nextjs';\n\nexport default function WelcomeBanner() {\n  const { user, isSignedIn } = useUser();\n  const [showBanner, setShowBanner] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasShownBanner, setHasShownBanner] = useState(false);\n\n  useEffect(() => {\n    // Only show banner if user just signed in and we haven't shown it yet\n    if (isSignedIn && user?.firstName && !hasShownBanner) {\n      setHasShownBanner(true);\n      setIsVisible(true);\n\n      // Small delay to ensure smooth transition\n      const showTimer = setTimeout(() => {\n        setShowBanner(true);\n      }, 100);\n\n      // Start hiding after 4 seconds\n      const hideTimer = setTimeout(() => {\n        setShowBanner(false);\n      }, 4100);\n\n      // Remove from DOM after fade out completes\n      const removeTimer = setTimeout(() => {\n        setIsVisible(false);\n      }, 4600);\n\n      return () => {\n        clearTimeout(showTimer);\n        clearTimeout(hideTimer);\n        clearTimeout(removeTimer);\n      };\n    }\n  }, [isSignedIn, user?.firstName, hasShownBanner]);\n\n  // Reset banner state when user signs out\n  useEffect(() => {\n    if (!isSignedIn) {\n      setHasShownBanner(false);\n      setShowBanner(false);\n      setIsVisible(false);\n    }\n  }, [isSignedIn]);\n\n  if (!isSignedIn || !user?.firstName || !isVisible) {\n    return null;\n  }\n\n  return (\n    <div\n      className={`welcome-banner ${showBanner ? 'show' : 'hide'}`}\n      role=\"alert\"\n      aria-live=\"polite\"\n    >\n      Welcome, {user.firstName}! 🎨\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAA,uKAAO;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IAErD,IAAA,kNAAS,EAAC;QACR,sEAAsE;QACtE,IAAI,cAAc,MAAM,aAAa,CAAC,gBAAgB;YACpD,kBAAkB;YAClB,aAAa;YAEb,0CAA0C;YAC1C,MAAM,YAAY,WAAW;gBAC3B,cAAc;YAChB,GAAG;YAEH,+BAA+B;YAC/B,MAAM,YAAY,WAAW;gBAC3B,cAAc;YAChB,GAAG;YAEH,2CAA2C;YAC3C,MAAM,cAAc,WAAW;gBAC7B,aAAa;YACf,GAAG;YAEH,OAAO;gBACL,aAAa;gBACb,aAAa;gBACb,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAY,MAAM;QAAW;KAAe;IAEhD,yCAAyC;IACzC,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,YAAY;YACf,kBAAkB;YAClB,cAAc;YACd,aAAa;QACf;IACF,GAAG;QAAC;KAAW;IAEf,IAAI,CAAC,cAAc,CAAC,MAAM,aAAa,CAAC,WAAW;QACjD,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,eAAe,EAAE,aAAa,SAAS,QAAQ;QAC3D,MAAK;QACL,aAAU;;YACX;YACW,KAAK,SAAS;YAAC;;;;;;;AAG/B", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useUser, UserButton, SignInButton, SignUpButton } from '@clerk/nextjs';\nimport WelcomeBanner from '@/components/ui/WelcomeBanner';\n\nexport default function Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isClosing, setIsClosing] = useState(false);\n  const [isHydrated, setIsHydrated] = useState(false);\n  const mobileMenuRef = useRef<HTMLDivElement>(null);\n  const menuButtonRef = useRef<HTMLButtonElement>(null);\n  const pathname = usePathname();\n  const router = useRouter();\n  const { isSignedIn } = useUser();\n\n  // Handle hydration\n  useEffect(() => {\n    setIsHydrated(true);\n  }, []);\n\n  const toggleMobileMenu = () => {\n    if (isMobileMenuOpen) {\n      closeMobileMenu();\n    } else {\n      setIsMobileMenuOpen(true);\n      setIsClosing(false);\n    }\n  };\n\n  const closeMobileMenu = () => {\n    setIsClosing(true);\n    setTimeout(() => {\n      setIsMobileMenuOpen(false);\n      setIsClosing(false);\n    }, 400); // Match animation duration\n  };\n\n  const handleMobileNavClick = (href: string, event: React.MouseEvent) => {\n    event.preventDefault();\n\n    // Start closing animation\n    setIsClosing(true);\n\n    // Navigate after animation completes\n    setTimeout(() => {\n      setIsMobileMenuOpen(false);\n      setIsClosing(false);\n      router.push(href);\n    }, 600); // Allow time for staggered exit animation\n  };\n\n  const isActive = (href: string) => {\n    // Prevent hydration mismatch by only checking pathname after hydration\n    if (!isHydrated) return false;\n\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  // Focus management for mobile menu\n  useEffect(() => {\n    if (isMobileMenuOpen && !isClosing) {\n      // Focus the first link in the mobile menu when opened\n      const firstLink = mobileMenuRef.current?.querySelector('a');\n      firstLink?.focus();\n    } else if (!isMobileMenuOpen) {\n      // Return focus to menu button when closed\n      menuButtonRef.current?.focus();\n    }\n  }, [isMobileMenuOpen, isClosing]);\n\n  // Handle escape key to close mobile menu\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        closeMobileMenu();\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when menu is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = '';\n    };\n  }, [isMobileMenuOpen]);\n\n  return (\n    <>\n      {/* Welcome Banner */}\n      <WelcomeBanner />\n\n      {/* Navigation */}\n      <nav className=\"nav\" role=\"navigation\" aria-label=\"Main navigation\">\n        <div className=\"nav-container\">\n          <Link href=\"/\" className=\"nav-logo\" aria-label=\"Chocolate & Art Show home\">\n            <Image\n              src=\"/images/brand/choco-logo.png\"\n              alt=\"Chocolate & Art Show logo\"\n              width={48}\n              height={48}\n            />\n            <span>Chocolate & Art Show</span>\n          </Link>\n\n          {/* Navigation center section - menu + auth closer together */}\n          <div className=\"nav-center\">\n            <ul className=\"nav-menu\" role=\"menubar\">\n              <li role=\"none\">\n                <Link href=\"/\" role=\"menuitem\" className={isActive('/') ? 'active' : ''}>Home</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/artists\" role=\"menuitem\" className={isActive('/artists') ? 'active' : ''}>Artists</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/shows\" role=\"menuitem\" className={isActive('/shows') ? 'active' : ''}>Shows</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/gallery\" role=\"menuitem\" className={isActive('/gallery') ? 'active' : ''}>Gallery</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/faq\" role=\"menuitem\" className={isActive('/faq') ? 'active' : ''}>FAQ</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/contact\" role=\"menuitem\" className={isActive('/contact') ? 'active' : ''}>Contact</Link>\n              </li>\n            </ul>\n\n            {/* Authentication Section */}\n            <div className=\"auth-section\">\n              {isSignedIn ? (\n                <div className=\"auth-user-section\">\n                  <Link href=\"/dashboard\" className=\"auth-dashboard-link\">\n                    Dashboard\n                  </Link>\n                  <UserButton\n                    appearance={{\n                      elements: {\n                        avatarBox: 'w-8 h-8 border-2 border-pink-500 rounded-full hover:border-pink-400 transition-colors',\n                        userButtonPopoverCard: 'bg-gray-900 border border-pink-500',\n                        userButtonPopoverActionButton: 'text-white hover:bg-pink-500',\n                        userButtonPopoverActionButtonText: 'text-white',\n                        userButtonPopoverFooter: 'hidden'\n                      }\n                    }}\n                  />\n                </div>\n              ) : (\n                <div className=\"auth-buttons\">\n                  <SignInButton mode=\"modal\">\n                    <button className=\"auth-btn auth-btn-signin\">\n                      Sign In\n                    </button>\n                  </SignInButton>\n                  <SignUpButton mode=\"modal\">\n                    <button className=\"auth-btn auth-btn-signup\">\n                      Sign Up\n                    </button>\n                  </SignUpButton>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            ref={menuButtonRef}\n            className={`mobile-menu-btn${isMobileMenuOpen ? ' active' : ''}`}\n            aria-label=\"Toggle mobile menu\"\n            aria-expanded={isMobileMenuOpen}\n            onClick={toggleMobileMenu}\n          >\n            <span></span>\n            <span></span>\n            <span></span>\n          </button>\n        </div>\n      </nav>\n\n      {/* Mobile Menu Overlay */}\n      <div\n        className={`mobile-menu-overlay${isMobileMenuOpen ? ' active' : ''}${isClosing ? ' closing' : ''}`}\n        id=\"mobile-menu-overlay\"\n        role=\"dialog\"\n        aria-modal=\"true\"\n        aria-labelledby=\"mobile-menu-title\"\n        aria-hidden={!isMobileMenuOpen}\n        onClick={(e) => {\n          if (e.target === e.currentTarget) {\n            closeMobileMenu();\n          }\n        }}\n      >\n        <div ref={mobileMenuRef} className={`mobile-menu${isClosing ? ' closing' : ''}`}>\n          <h2 id=\"mobile-menu-title\" className=\"sr-only\">Mobile Navigation Menu</h2>\n          <nav role=\"navigation\" aria-label=\"Mobile navigation\">\n\n            {/* Mobile Authentication Section - Moved to Top */}\n            {isSignedIn ? (\n              <div\n                className=\"mobile-auth-top\"\n                style={{ '--delay': '0' } as React.CSSProperties}\n              >\n                <UserButton\n                  appearance={{\n                    elements: {\n                      avatarBox: 'w-8 h-8 border-2 border-pink-500 rounded-full',\n                      userButtonPopoverCard: 'bg-gray-900 border border-pink-500',\n                      userButtonPopoverActionButton: 'text-white hover:bg-pink-500',\n                      userButtonPopoverActionButtonText: 'text-white'\n                    }\n                  }}\n                />\n                <Link\n                  href=\"/dashboard\"\n                  onClick={(e) => handleMobileNavClick('/dashboard', e)}\n                  className={`mobile-dashboard-link ${isActive('/dashboard') ? 'active' : ''}`}\n                >\n                  Dashboard\n                </Link>\n              </div>\n            ) : (\n              <div\n                className=\"mobile-auth-top mobile-auth-buttons\"\n                style={{ '--delay': '0' } as React.CSSProperties}\n              >\n                <SignInButton mode=\"modal\">\n                  <button className=\"mobile-auth-btn mobile-auth-signin\">\n                    Sign In\n                  </button>\n                </SignInButton>\n                <SignUpButton mode=\"modal\">\n                  <button className=\"mobile-auth-btn mobile-auth-signup\">\n                    Sign Up\n                  </button>\n                </SignUpButton>\n              </div>\n            )}\n\n            {/* Navigation Items */}\n            <Link\n              href=\"/\"\n              onClick={(e) => handleMobileNavClick('/', e)}\n              className={`mobile-nav-item ${isActive('/') ? 'active' : ''}`}\n              style={{ '--delay': '1' } as React.CSSProperties}\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/artists\"\n              onClick={(e) => handleMobileNavClick('/artists', e)}\n              className={`mobile-nav-item ${isActive('/artists') ? 'active' : ''}`}\n              style={{ '--delay': '2' } as React.CSSProperties}\n            >\n              Artists\n            </Link>\n            <Link\n              href=\"/shows\"\n              onClick={(e) => handleMobileNavClick('/shows', e)}\n              className={`mobile-nav-item ${isActive('/shows') ? 'active' : ''}`}\n              style={{ '--delay': '3' } as React.CSSProperties}\n            >\n              Shows\n            </Link>\n            <Link\n              href=\"/gallery\"\n              onClick={(e) => handleMobileNavClick('/gallery', e)}\n              className={`mobile-nav-item ${isActive('/gallery') ? 'active' : ''}`}\n              style={{ '--delay': '4' } as React.CSSProperties}\n            >\n              Gallery\n            </Link>\n            <Link\n              href=\"/faq\"\n              onClick={(e) => handleMobileNavClick('/faq', e)}\n              className={`mobile-nav-item ${isActive('/faq') ? 'active' : ''}`}\n              style={{ '--delay': '5' } as React.CSSProperties}\n            >\n              FAQ\n            </Link>\n            <Link\n              href=\"/contact\"\n              onClick={(e) => handleMobileNavClick('/contact', e)}\n              className={`mobile-nav-item ${isActive('/contact') ? 'active' : ''}`}\n              style={{ '--delay': '6' } as React.CSSProperties}\n            >\n              Contact\n            </Link>\n          </nav>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAC;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,gBAAgB,IAAA,+MAAM,EAAiB;IAC7C,MAAM,gBAAgB,IAAA,+MAAM,EAAoB;IAChD,MAAM,WAAW,IAAA,iJAAW;IAC5B,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,uKAAO;IAE9B,mBAAmB;IACnB,IAAA,kNAAS,EAAC;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,kBAAkB;YACpB;QACF,OAAO;YACL,oBAAoB;YACpB,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,WAAW;YACT,oBAAoB;YACpB,aAAa;QACf,GAAG,MAAM,2BAA2B;IACtC;IAEA,MAAM,uBAAuB,CAAC,MAAc;QAC1C,MAAM,cAAc;QAEpB,0BAA0B;QAC1B,aAAa;QAEb,qCAAqC;QACrC,WAAW;YACT,oBAAoB;YACpB,aAAa;YACb,OAAO,IAAI,CAAC;QACd,GAAG,MAAM,0CAA0C;IACrD;IAEA,MAAM,WAAW,CAAC;QAChB,uEAAuE;QACvE,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,mCAAmC;IACnC,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB,CAAC,WAAW;YAClC,sDAAsD;YACtD,MAAM,YAAY,cAAc,OAAO,EAAE,cAAc;YACvD,WAAW;QACb,OAAO,IAAI,CAAC,kBAAkB;YAC5B,0CAA0C;YAC1C,cAAc,OAAO,EAAE;QACzB;IACF,GAAG;QAAC;QAAkB;KAAU;IAEhC,yCAAyC;IACzC,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,YAAY,kBAAkB;gBAC9C;YACF;QACF;QAEA,IAAI,kBAAkB;YACpB,SAAS,gBAAgB,CAAC,WAAW;YACrC,wCAAwC;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAiB;IAErB,qBACE;;0BAEE,8OAAC,6IAAa;;;;;0BAGd,8OAAC;gBAAI,WAAU;gBAAM,MAAK;gBAAa,cAAW;0BAChD,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;4BAAW,cAAW;;8CAC7C,8OAAC,wIAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;8CAEV,8OAAC;8CAAK;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAW,MAAK;;sDAC5B,8OAAC;4CAAG,MAAK;sDACP,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAI,MAAK;gDAAW,WAAW,SAAS,OAAO,WAAW;0DAAI;;;;;;;;;;;sDAE3E,8OAAC;4CAAG,MAAK;sDACP,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,MAAK;gDAAW,WAAW,SAAS,cAAc,WAAW;0DAAI;;;;;;;;;;;sDAEzF,8OAAC;4CAAG,MAAK;sDACP,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAS,MAAK;gDAAW,WAAW,SAAS,YAAY,WAAW;0DAAI;;;;;;;;;;;sDAErF,8OAAC;4CAAG,MAAK;sDACP,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,MAAK;gDAAW,WAAW,SAAS,cAAc,WAAW;0DAAI;;;;;;;;;;;sDAEzF,8OAAC;4CAAG,MAAK;sDACP,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAO,MAAK;gDAAW,WAAW,SAAS,UAAU,WAAW;0DAAI;;;;;;;;;;;sDAEjF,8OAAC;4CAAG,MAAK;sDACP,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,MAAK;gDAAW,WAAW,SAAS,cAAc,WAAW;0DAAI;;;;;;;;;;;;;;;;;8CAK3F,8OAAC;oCAAI,WAAU;8CACZ,2BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,uKAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAsB;;;;;;0DAGxD,8OAAC,qLAAU;gDACT,YAAY;oDACV,UAAU;wDACR,WAAW;wDACX,uBAAuB;wDACvB,+BAA+B;wDAC/B,mCAAmC;wDACnC,yBAAyB;oDAC3B;gDACF;;;;;;;;;;;6DAIJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2LAAY;gDAAC,MAAK;0DACjB,cAAA,8OAAC;oDAAO,WAAU;8DAA2B;;;;;;;;;;;0DAI/C,8OAAC,2LAAY;gDAAC,MAAK;0DACjB,cAAA,8OAAC;oDAAO,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUvD,8OAAC;4BACC,KAAK;4BACL,WAAW,CAAC,eAAe,EAAE,mBAAmB,YAAY,IAAI;4BAChE,cAAW;4BACX,iBAAe;4BACf,SAAS;;8CAET,8OAAC;;;;;8CACD,8OAAC;;;;;8CACD,8OAAC;;;;;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBACC,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,YAAY,KAAK,YAAY,aAAa,IAAI;gBAClG,IAAG;gBACH,MAAK;gBACL,cAAW;gBACX,mBAAgB;gBAChB,eAAa,CAAC;gBACd,SAAS,CAAC;oBACR,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;wBAChC;oBACF;gBACF;0BAEA,cAAA,8OAAC;oBAAI,KAAK;oBAAe,WAAW,CAAC,WAAW,EAAE,YAAY,aAAa,IAAI;;sCAC7E,8OAAC;4BAAG,IAAG;4BAAoB,WAAU;sCAAU;;;;;;sCAC/C,8OAAC;4BAAI,MAAK;4BAAa,cAAW;;gCAG/B,2BACC,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW;oCAAI;;sDAExB,8OAAC,qLAAU;4CACT,YAAY;gDACV,UAAU;oDACR,WAAW;oDACX,uBAAuB;oDACvB,+BAA+B;oDAC/B,mCAAmC;gDACrC;4CACF;;;;;;sDAEF,8OAAC,uKAAI;4CACH,MAAK;4CACL,SAAS,CAAC,IAAM,qBAAqB,cAAc;4CACnD,WAAW,CAAC,sBAAsB,EAAE,SAAS,gBAAgB,WAAW,IAAI;sDAC7E;;;;;;;;;;;yDAKH,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW;oCAAI;;sDAExB,8OAAC,2LAAY;4CAAC,MAAK;sDACjB,cAAA,8OAAC;gDAAO,WAAU;0DAAqC;;;;;;;;;;;sDAIzD,8OAAC,2LAAY;4CAAC,MAAK;sDACjB,cAAA,8OAAC;gDAAO,WAAU;0DAAqC;;;;;;;;;;;;;;;;;8CAQ7D,8OAAC,uKAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,KAAK;oCAC1C,WAAW,CAAC,gBAAgB,EAAE,SAAS,OAAO,WAAW,IAAI;oCAC7D,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,YAAY;oCACjD,WAAW,CAAC,gBAAgB,EAAE,SAAS,cAAc,WAAW,IAAI;oCACpE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,UAAU;oCAC/C,WAAW,CAAC,gBAAgB,EAAE,SAAS,YAAY,WAAW,IAAI;oCAClE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,YAAY;oCACjD,WAAW,CAAC,gBAAgB,EAAE,SAAS,cAAc,WAAW,IAAI;oCACpE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,QAAQ;oCAC7C,WAAW,CAAC,gBAAgB,EAAE,SAAS,UAAU,WAAW,IAAI;oCAChE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,YAAY;oCACjD,WAAW,CAAC,gBAAgB,EAAE,SAAS,cAAc,WAAW,IAAI;oCACpE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useEffect } from 'react';\nimport { SparklesIcon } from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  useEffect(() => {\n    // Easter Egg functionality for social icons\n    let hoverSequence: number[] = [];\n    const correctSequence = [0, 1, 2, 3, 4, 5, 5, 4, 3, 2, 1, 0]; // Left to right, then right to left\n    let isEasterEggActive = false;\n\n    const socialIcons = document.querySelectorAll('.footer-social a');\n\n    const resetSequence = () => {\n      hoverSequence = [];\n    };\n\n    const createSmokeParticles = (element: Element) => {\n      const rect = element.getBoundingClientRect();\n      const centerX = rect.left + rect.width / 2;\n      const centerY = rect.top + rect.height / 2;\n\n      for (let i = 0; i < 8; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'smoke-particle';\n        particle.style.position = 'fixed';\n        particle.style.left = centerX + (Math.random() - 0.5) * 20 + 'px';\n        particle.style.top = centerY + (Math.random() - 0.5) * 20 + 'px';\n        particle.style.zIndex = '9999';\n        document.body.appendChild(particle);\n\n        setTimeout(() => {\n          particle.remove();\n        }, 2000);\n      }\n    };\n\n    const triggerEasterEgg = () => {\n      if (isEasterEggActive) return;\n      isEasterEggActive = true;\n\n      // Phase 1: Glow effect\n      socialIcons.forEach((icon, index) => {\n        setTimeout(() => {\n          icon.classList.add('easter-egg-glow');\n        }, index * 100);\n      });\n\n      // Phase 2: Explode and scatter\n      setTimeout(() => {\n        socialIcons.forEach((icon) => {\n          icon.classList.remove('easter-egg-glow');\n          icon.classList.add('easter-egg-explode');\n\n          setTimeout(() => {\n            createSmokeParticles(icon);\n            icon.classList.remove('easter-egg-explode');\n            icon.classList.add('easter-egg-scatter');\n\n            // Random scatter directions\n            const randomX = (Math.random() - 0.5) * 400;\n            const randomY = (Math.random() - 0.5) * 400;\n            (icon as HTMLElement).style.transform = `translate(${randomX}px, ${randomY}px) scale(0.5) rotate(${Math.random() * 360}deg)`;\n          }, 400);\n        });\n      }, 1000);\n\n      // Phase 3: Return to original positions\n      setTimeout(() => {\n        socialIcons.forEach((icon, index) => {\n          setTimeout(() => {\n            icon.classList.remove('easter-egg-scatter');\n            icon.classList.add('easter-egg-return');\n            (icon as HTMLElement).style.transform = '';\n\n            setTimeout(() => {\n              icon.classList.remove('easter-egg-return');\n              isEasterEggActive = false;\n            }, 1000);\n          }, index * 150);\n        });\n      }, 3000);\n    };\n\n    socialIcons.forEach((icon, index) => {\n      icon.addEventListener('mouseenter', () => {\n        if (isEasterEggActive) return;\n\n        hoverSequence.push(index);\n\n        // Check if sequence matches the correct pattern\n        const isCorrectSoFar = hoverSequence.every((val, i) => val === correctSequence[i]);\n\n        if (!isCorrectSoFar) {\n          resetSequence();\n          hoverSequence.push(index);\n        }\n\n        // Check if complete sequence is achieved\n        if (hoverSequence.length === correctSequence.length &&\n          hoverSequence.every((val, i) => val === correctSequence[i])) {\n          triggerEasterEgg();\n          resetSequence();\n        }\n\n        // Reset if sequence gets too long without matching\n        if (hoverSequence.length > correctSequence.length) {\n          resetSequence();\n        }\n      });\n    });\n\n    // Reset sequence after period of inactivity\n    let resetTimer: NodeJS.Timeout;\n    const resetAfterDelay = () => {\n      clearTimeout(resetTimer);\n      resetTimer = setTimeout(resetSequence, 3000);\n    };\n\n    socialIcons.forEach(icon => {\n      icon.addEventListener('mouseenter', resetAfterDelay);\n    });\n\n    // Initialize confetti functionality\n    const confettiButton = document.getElementById('footer-confetti-trigger');\n    const canvas = document.getElementById('confetti-canvas') as HTMLCanvasElement;\n\n    if (confettiButton && canvas) {\n      confettiButton.addEventListener('click', () => {\n        // Confetti animation will be implemented later\n        console.log('Confetti triggered!');\n      });\n    }\n\n    return () => {\n      clearTimeout(resetTimer);\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Footer */}\n      <footer className=\"site-footer\" id=\"site-footer\" role=\"contentinfo\">\n        <div className=\"footer-content\">\n          {/* Brand Section */}\n          <div className=\"footer-brand\">\n            <Link className=\"brand-ft\" href=\"/\" aria-label=\"Chocolate & Art Show home\">\n              <Image\n                src=\"/images/brand/choco-logo.png\"\n                width={32}\n                height={32}\n                alt=\"Chocolate & Art Show\"\n              />\n              <span>Chocolate & Art Show</span>\n            </Link>\n            <p className=\"footer-tagline\">Where art meets chocolate, and creativity flows.</p>\n          </div>\n\n          {/* Quick Links Section */}\n          <div className=\"footer-section\">\n            <h3>Quick Links</h3>\n            <nav className=\"footer-nav\" aria-label=\"Footer navigation\">\n              <Link href=\"/events/dallas-tx-2025-09-18-19\">Buy Tickets</Link>\n              <Link href=\"/artists\">Meet Artists</Link>\n              <Link href=\"/shows\">Event Schedule</Link>\n              <Link href=\"/#gallery\">Photo Gallery</Link>\n            </nav>\n          </div>\n\n          {/* Get Involved Section */}\n          <div className=\"footer-section\">\n            <h3>Get Involved</h3>\n            <nav className=\"footer-nav\" aria-label=\"Get involved navigation\">\n              <Link\n                href=\"mailto:<EMAIL>?subject=Artist%20Submission%20—%20Dallas/September&body=Links%20+%20portfolio%20+%20dimensions\"\n              >\n                Apply as Artist\n              </Link>\n              <Link\n                href=\"mailto:<EMAIL>?subject=Vendor%20Application%20—%20Dallas/September&body=Line%20sheet%20+%20power%20needs\"\n              >\n                Apply as Vendor\n              </Link>\n              <Link href=\"/contact\">Contact Us</Link>\n            </nav>\n          </div>\n\n          {/* Upcoming Show Section */}\n          <div className=\"footer-section upcoming-show-card\">\n            <h3>Upcoming Show:</h3>\n            <div className=\"show-details\">\n              <div className=\"show-city\">Dallas, TX</div>\n              <ul className=\"show-info\">\n                <li>September 18-19, 2025</li>\n                <li>Lofty Spaces</li>\n                <li>21+ Event</li>\n                <li>Doors: 7:00 PM</li>\n              </ul>\n            </div>\n            {/* <div className=\"show-expand\">\n              <span className=\"expand-icon\">+</span>\n              <span className=\"expand-text\">see more</span>\n            </div> */}\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"footer-bottom\">\n          <div className=\"footer-bottom-content\">\n            <div className=\"footer-legal\">\n              <small>© 2025 Chocolate & Art Show. All rights reserved.</small>\n              <small>An immersive experience celebrating art, music, and artisan chocolate.</small>\n            </div>\n\n            {/* Social Icons */}\n            <nav className=\"footer-social\" aria-label=\"Social links\">\n              <a href=\"https://www.facebook.com/ChocolateAndArtShowDallas\" target=\"_blank\" rel=\"noopener\"\n                aria-label=\"Facebook\">\n                <Image src=\"/images/brand/ico/facebook-white.svg\" alt=\"Facebook\" width={24} height={24} />\n              </a>\n              <a href=\"https://www.instagram.com/chocolateandartshow/\" target=\"_blank\" rel=\"noopener\" aria-label=\"Instagram\">\n                <Image src=\"/images/brand/ico/instagram-white.svg\" alt=\"Instagram\" width={24} height={24} />\n              </a>\n              <a href=\"https://www.youtube.com/@ChocolateAndArtShow\" target=\"_blank\" rel=\"noopener\" aria-label=\"YouTube\">\n                <Image src=\"/images/brand/ico/youtube-white.svg\" alt=\"YouTube\" width={24} height={24} />\n              </a>\n              <a href=\"https://vimeo.com/chocolateandartshow\" target=\"_blank\" rel=\"noopener\" aria-label=\"Vimeo\">\n                <Image src=\"/images/brand/ico/vimeo-white.svg\" alt=\"Vimeo\" width={24} height={24} />\n              </a>\n              <a href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\" target=\"_blank\"\n                rel=\"noopener\" aria-label=\"Eventbrite\">\n                <Image src=\"/images/brand/ico/eventbrite-white.svg\" alt=\"Eventbrite\" width={24} height={24} />\n              </a>\n              <a href=\"mailto:<EMAIL>\" aria-label=\"Email\">\n                <Image src=\"/images/brand/ico/mail-white.svg\" alt=\"Email\" width={24} height={24} />\n              </a>\n            </nav>\n\n            {/* Celebrate button */}\n            <div className=\"footer-celebrate\">\n              <button className=\"btn-modern\" id=\"footer-confetti-trigger\" aria-label=\"Celebrate Dallas\">\n                <span className=\"btn-modern__inner\">\n                  <SparklesIcon className=\"celebrate-icon\" />\n                  Celebrate\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </footer>\n\n      <canvas id=\"confetti-canvas\" className=\"confetti-canvas\" aria-hidden=\"true\"></canvas>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,IAAA,kNAAS,EAAC;QACR,4CAA4C;QAC5C,IAAI,gBAA0B,EAAE;QAChC,MAAM,kBAAkB;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE,EAAE,oCAAoC;QAClG,IAAI,oBAAoB;QAExB,MAAM,cAAc,SAAS,gBAAgB,CAAC;QAE9C,MAAM,gBAAgB;YACpB,gBAAgB,EAAE;QACpB;QAEA,MAAM,uBAAuB,CAAC;YAC5B,MAAM,OAAO,QAAQ,qBAAqB;YAC1C,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;YACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;YAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,MAAM,WAAW,SAAS,aAAa,CAAC;gBACxC,SAAS,SAAS,GAAG;gBACrB,SAAS,KAAK,CAAC,QAAQ,GAAG;gBAC1B,SAAS,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK;gBAC7D,SAAS,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK;gBAC5D,SAAS,KAAK,CAAC,MAAM,GAAG;gBACxB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,WAAW;oBACT,SAAS,MAAM;gBACjB,GAAG;YACL;QACF;QAEA,MAAM,mBAAmB;YACvB,IAAI,mBAAmB;YACvB,oBAAoB;YAEpB,uBAAuB;YACvB,YAAY,OAAO,CAAC,CAAC,MAAM;gBACzB,WAAW;oBACT,KAAK,SAAS,CAAC,GAAG,CAAC;gBACrB,GAAG,QAAQ;YACb;YAEA,+BAA+B;YAC/B,WAAW;gBACT,YAAY,OAAO,CAAC,CAAC;oBACnB,KAAK,SAAS,CAAC,MAAM,CAAC;oBACtB,KAAK,SAAS,CAAC,GAAG,CAAC;oBAEnB,WAAW;wBACT,qBAAqB;wBACrB,KAAK,SAAS,CAAC,MAAM,CAAC;wBACtB,KAAK,SAAS,CAAC,GAAG,CAAC;wBAEnB,4BAA4B;wBAC5B,MAAM,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACxC,MAAM,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACvC,KAAqB,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE,QAAQ,sBAAsB,EAAE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC;oBAC9H,GAAG;gBACL;YACF,GAAG;YAEH,wCAAwC;YACxC,WAAW;gBACT,YAAY,OAAO,CAAC,CAAC,MAAM;oBACzB,WAAW;wBACT,KAAK,SAAS,CAAC,MAAM,CAAC;wBACtB,KAAK,SAAS,CAAC,GAAG,CAAC;wBAClB,KAAqB,KAAK,CAAC,SAAS,GAAG;wBAExC,WAAW;4BACT,KAAK,SAAS,CAAC,MAAM,CAAC;4BACtB,oBAAoB;wBACtB,GAAG;oBACL,GAAG,QAAQ;gBACb;YACF,GAAG;QACL;QAEA,YAAY,OAAO,CAAC,CAAC,MAAM;YACzB,KAAK,gBAAgB,CAAC,cAAc;gBAClC,IAAI,mBAAmB;gBAEvB,cAAc,IAAI,CAAC;gBAEnB,gDAAgD;gBAChD,MAAM,iBAAiB,cAAc,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,eAAe,CAAC,EAAE;gBAEjF,IAAI,CAAC,gBAAgB;oBACnB;oBACA,cAAc,IAAI,CAAC;gBACrB;gBAEA,yCAAyC;gBACzC,IAAI,cAAc,MAAM,KAAK,gBAAgB,MAAM,IACjD,cAAc,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,eAAe,CAAC,EAAE,GAAG;oBAC7D;oBACA;gBACF;gBAEA,mDAAmD;gBACnD,IAAI,cAAc,MAAM,GAAG,gBAAgB,MAAM,EAAE;oBACjD;gBACF;YACF;QACF;QAEA,4CAA4C;QAC5C,IAAI;QACJ,MAAM,kBAAkB;YACtB,aAAa;YACb,aAAa,WAAW,eAAe;QACzC;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,KAAK,gBAAgB,CAAC,cAAc;QACtC;QAEA,oCAAoC;QACpC,MAAM,iBAAiB,SAAS,cAAc,CAAC;QAC/C,MAAM,SAAS,SAAS,cAAc,CAAC;QAEvC,IAAI,kBAAkB,QAAQ;YAC5B,eAAe,gBAAgB,CAAC,SAAS;gBACvC,+CAA+C;gBAC/C,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,OAAO;YACL,aAAa;QACf;IACF,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC;gBAAO,WAAU;gBAAc,IAAG;gBAAc,MAAK;;kCACpD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCAAC,WAAU;wCAAW,MAAK;wCAAI,cAAW;;0DAC7C,8OAAC,wIAAK;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,KAAI;;;;;;0DAEN,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAU;wCAAa,cAAW;;0DACrC,8OAAC,uKAAI;gDAAC,MAAK;0DAAkC;;;;;;0DAC7C,8OAAC,uKAAI;gDAAC,MAAK;0DAAW;;;;;;0DACtB,8OAAC,uKAAI;gDAAC,MAAK;0DAAS;;;;;;0DACpB,8OAAC,uKAAI;gDAAC,MAAK;0DAAY;;;;;;;;;;;;;;;;;;0CAK3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAU;wCAAa,cAAW;;0DACrC,8OAAC,uKAAI;gDACH,MAAK;0DACN;;;;;;0DAGD,8OAAC,uKAAI;gDACH,MAAK;0DACN;;;;;;0DAGD,8OAAC,uKAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAM;;;;;;sDACP,8OAAC;sDAAM;;;;;;;;;;;;8CAIT,8OAAC;oCAAI,WAAU;oCAAgB,cAAW;;sDACxC,8OAAC;4CAAE,MAAK;4CAAqD,QAAO;4CAAS,KAAI;4CAC/E,cAAW;sDACX,cAAA,8OAAC,wIAAK;gDAAC,KAAI;gDAAuC,KAAI;gDAAW,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEtF,8OAAC;4CAAE,MAAK;4CAAiD,QAAO;4CAAS,KAAI;4CAAW,cAAW;sDACjG,cAAA,8OAAC,wIAAK;gDAAC,KAAI;gDAAwC,KAAI;gDAAY,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAExF,8OAAC;4CAAE,MAAK;4CAA+C,QAAO;4CAAS,KAAI;4CAAW,cAAW;sDAC/F,cAAA,8OAAC,wIAAK;gDAAC,KAAI;gDAAsC,KAAI;gDAAU,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEpF,8OAAC;4CAAE,MAAK;4CAAwC,QAAO;4CAAS,KAAI;4CAAW,cAAW;sDACxF,cAAA,8OAAC,wIAAK;gDAAC,KAAI;gDAAoC,KAAI;gDAAQ,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEhF,8OAAC;4CAAE,MAAK;4CAAmF,QAAO;4CAChG,KAAI;4CAAW,cAAW;sDAC1B,cAAA,8OAAC,wIAAK;gDAAC,KAAI;gDAAyC,KAAI;gDAAa,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAE1F,8OAAC;4CAAE,MAAK;4CAAsC,cAAW;sDACvD,cAAA,8OAAC,wIAAK;gDAAC,KAAI;gDAAmC,KAAI;gDAAQ,OAAO;gDAAI,QAAQ;;;;;;;;;;;;;;;;;8CAKjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;wCAAa,IAAG;wCAA0B,cAAW;kDACrE,cAAA,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,uOAAY;oDAAC,WAAU;;;;;;gDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvD,8OAAC;gBAAO,IAAG;gBAAkB,WAAU;gBAAkB,eAAY;;;;;;;;AAG3E", "debugId": null}}]}