{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/HeroSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/HeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/HeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/HeroSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/HeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/HeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/RgbWall.tsx"], "sourcesContent": ["export default function RgbWall() {\n  return (\n    <section className=\"rgb-wall\" aria-label=\"Intermission banner\">\n      <div className=\"rgb-hero\">\n        <span className=\"rgb-word\" data-txt=\"ART\">ART</span>\n        <span className=\"rgb-plus\">+</span>\n        <span className=\"rgb-word\" data-txt=\"MUSIC\">MUSIC</span>\n        <span className=\"rgb-plus\">+</span>\n        <span className=\"rgb-word\" data-txt=\"CHOCOLATE\">CHOCOLATE</span>\n        <span className=\"rgb-eq\">=</span>\n        <span className=\"rgb-word\" data-txt=\"DALLAS\">DALLAS</span>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAW,cAAW;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAM;;;;;;8BAC1C,8OAAC;oBAAK,WAAU;8BAAW;;;;;;8BAC3B,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAQ;;;;;;8BAC5C,8OAAC;oBAAK,WAAU;8BAAW;;;;;;8BAC3B,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAY;;;;;;8BAChD,8OAAC;oBAAK,WAAU;8BAAS;;;;;;8BACzB,8OAAC;oBAAK,WAAU;oBAAW,YAAS;8BAAS;;;;;;;;;;;;;;;;;AAIrD", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/AboutSection.tsx"], "sourcesContent": ["export default function AboutSection() {\n  return (\n    <section id=\"about\" className=\"about-section\" aria-label=\"About the show\">\n      <div className=\"container\">\n        <h2>About Chocolate & Art Show</h2>\n        <p>\n          An immersive experience celebrating art, music, and artisan chocolate. \n          Join us for two unforgettable nights in Dallas.\n        </p>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;QAAgB,cAAW;kBACvD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;8BAAE;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/CtaBar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/CtaBar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/CtaBar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/CtaBar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/CtaBar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/CtaBar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/GallerySection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/GallerySection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/GallerySection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/GallerySection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/GallerySection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/GallerySection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ArtistsHeader.tsx"], "sourcesContent": ["export default function ArtistsHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Artists & Creators</h1>\n      <p>\n        Join <PERSON>&apos;s most immersive art experience. Apply to showcase your work,\n        perform, or sell at Chocolate & Art Show.\n      </p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ApplicationGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ApplicationGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ApplicationGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ApplicationGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ApplicationGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ApplicationGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/InfoSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/InfoSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/InfoSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/InfoSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/InfoSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/InfoSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactHeader.tsx"], "sourcesContent": ["export default function ContactHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Contact</h1>\n      <p>Get in touch with questions about the event, artist submissions, and vendor applications.</p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ContactGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ContactGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/ContactGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/ContactGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/TransportationSection.tsx"], "sourcesContent": ["import { TruckIcon } from '@heroicons/react/24/outline';\n\nexport default function TransportationSection() {\n  return (\n    <section className=\"transportation\">\n      <h2>\n        <TruckIcon className=\"section-icon\" />\n        Getting There\n      </h2>\n      <div className=\"transport-grid\">\n        <div className=\"transport-option bwhc-card\">\n          <h3>Driving</h3>\n          <p>Parking is available at the venue. We recommend arriving early as spaces are limited.</p>\n        </div>\n\n        <div className=\"transport-option bwhc-card\">\n          <h3>Rideshare</h3>\n          <p>\n            Uber and Lyft are recommended for convenience. The venue is easily accessible for pickup and drop-off.\n          </p>\n        </div>\n\n        <div className=\"transport-option bwhc-card\">\n          <h3>Public Transit</h3>\n          <p>Check local Dallas transit options for routes to the venue area.</p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;;kCACC,8OAAC,8NAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAGxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ImportantNotesSection.tsx"], "sourcesContent": ["import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';\n\nexport default function ImportantNotesSection() {\n  return (\n    <section className=\"important-notes\">\n      <h2>\n        <ExclamationTriangleIcon className=\"section-icon\" />\n        Important Information\n      </h2>\n      <div className=\"notes-grid\">\n        <div className=\"note-card bwhc-card\">\n          <h3>ID Required</h3>\n          <p>This is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.</p>\n        </div>\n\n        <div className=\"note-card bwhc-card\">\n          <h3>No Re-entry</h3>\n          <p>Once you leave the venue, re-entry is not permitted. Please plan accordingly.</p>\n        </div>\n\n        <div className=\"note-card bwhc-card\">\n          <h3>Capacity Limited</h3>\n          <p>This is an intimate event with limited capacity. Advance ticket purchase is strongly recommended.</p>\n        </div>\n\n        <div className=\"note-card bwhc-card\">\n          <h3>Dress Code</h3>\n          <p>\n            Come as you are! We encourage creative expression and comfortable attire for an evening of art and music.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;;kCACC,8OAAC,wQAAuB;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAGtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactCta.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function ContactCta() {\n  return (\n    <section className=\"contact-cta\">\n      <h2>Ready to Join <PERSON>?</h2>\n      <p>Secure your spot at Dallas&apos;s most immersive art experience.</p>\n      <div className=\"cta-buttons\">\n        <Link href=\"/events/dallas-tx-2025-09-18-19/\" className=\"btn btn-primary\">\n          Get Tickets\n        </Link>\n        <Link href=\"/artists\" className=\"btn btn-secondary\">\n          Apply to Participate\n        </Link>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uKAAI;wBAAC,MAAK;wBAAmC,WAAU;kCAAkB;;;;;;kCAG1E,8OAAC,uKAAI;wBAAC,MAAK;wBAAW,WAAU;kCAAoB;;;;;;;;;;;;;;;;;;AAM5D", "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqHeader.tsx"], "sourcesContent": ["export default function FaqHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Frequently Asked Questions</h1>\n      <p>\n        Everything you need to know about Chocolate & Art Show Dallas. Can&apos;t find what you&apos;re looking for? Contact us!\n      </p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAKT", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqSections.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/FaqSections.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/FaqSections.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqSections.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/FaqSections.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/FaqSections.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqCta.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function FaqCta() {\n  return (\n    <section className=\"faq-cta\">\n      <h2>Still Have Questions?</h2>\n      <p>Can&apos;t find the answer you&apos;re looking for? We&apos;re here to help!</p>\n      <Link href=\"/contact\" className=\"btn btn-primary\">\n        Contact Us\n      </Link>\n      <Link href=\"/events/dallas-tx-2025-09-18-19/\" className=\"btn btn-secondary\">\n        Get Tickets\n      </Link>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;0BACH,8OAAC,uKAAI;gBAAC,MAAK;gBAAW,WAAU;0BAAkB;;;;;;0BAGlD,8OAAC,uKAAI;gBAAC,MAAK;gBAAmC,WAAU;0BAAoB;;;;;;;;;;;;AAKlF", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventHeader.tsx"], "sourcesContent": ["export default function EventHeader() {\n  return (\n    <section className=\"event-header\">\n      <div className=\"container\">\n        <h1>Chocolate & Art Show</h1>\n        <div className=\"event-subtitle\">Dallas, Texas</div>\n        <div className=\"event-description\">\n          An immersive experience celebrating art, live music, body painting, and artisan chocolate.\n          Two unforgettable nights at Lofty Spaces in the heart of Dallas.\n        </div>\n\n        {/* Cialdini-framed ticket CTAs */}\n        <div className=\"ticket-section\">\n          <a \n            href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\"\n            className=\"ticket-btn\" \n            target=\"_blank\" \n            rel=\"noopener\"\n          >\n            Lock your spot — Thursday & Friday only, 21+\n          </a>\n        </div>\n        <div className=\"urgency-text\">\n          Limited capacity • Advance purchase strongly recommended\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;oBAAI,WAAU;8BAAiB;;;;;;8BAChC,8OAAC;oBAAI,WAAU;8BAAoB;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,QAAO;wBACP,KAAI;kCACL;;;;;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BAAe;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventDetails.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventDetails.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventDetails.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventDetails.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventDetails.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventDetails.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventCta.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventCta.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventCta.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventCta.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventCta.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventCta.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1019, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsHeader.tsx"], "sourcesContent": ["export default function EventsHeader() {\n  return (\n    <section className=\"page-header\">\n      <h1>Shows & Cities</h1>\n      <p>Immersive art experiences coming to cities across the country. Find your city and secure your spot.</p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;0BAAE;;;;;;;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventsGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventsGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/sections/EventsGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/sections/EventsGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/index.ts"], "sourcesContent": ["export { default as HeroSection } from './HeroSection';\nexport { default as RgbWall } from './RgbWall';\nexport { default as AboutSection } from './AboutSection';\nexport { default as CtaBar } from './CtaBar';\nexport { default as GallerySection } from './GallerySection';\nexport { default as ArtistsHeader } from './ArtistsHeader';\nexport { default as ApplicationGrid } from './ApplicationGrid';\nexport { default as InfoSection } from './InfoSection';\nexport { default as ContactHeader } from './ContactHeader';\nexport { default as ContactGrid } from './ContactGrid';\nexport { default as TransportationSection } from './TransportationSection';\nexport { default as ImportantNotesSection } from './ImportantNotesSection';\nexport { default as ContactCta } from './ContactCta';\nexport { default as FaqHeader } from './FaqHeader';\nexport { default as FaqSections } from './FaqSections';\nexport { default as FaqCta } from './FaqCta';\nexport { default as EventHeader } from './EventHeader';\nexport { default as EventDetails } from './EventDetails';\nexport { default as EventCta } from './EventCta';\nexport { default as EventsHeader } from './EventsHeader';\nexport { default as EventsGrid } from './EventsGrid';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/app/page.tsx"], "sourcesContent": ["import {\n  HeroSection,\n  AboutSection,\n  CtaBar,\n  GallerySection\n} from \"@/components/sections\";\n\n\nexport default function Home() {\n  return (\n    <>\n      {/* Hero Section */}\n      <HeroSection />\n\n      {/* RGB Wall Interstitial */}\n      {/* <RgbWall /> */}\n\n      {/* About Section */}\n      <AboutSection />\n\n      {/* CTA Bar Component */}\n      <CtaBar />\n\n      {/* Gallery Section */}\n      <GallerySection />\n    </>\n  );\n};"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAQe,SAAS;IACtB,qBACE;;0BAEE,8OAAC,+LAAW;;;;;0BAMZ,8OAAC,kMAAY;;;;;0BAGb,8OAAC,gLAAM;;;;;0BAGP,8OAAC,wMAAc;;;;;;;AAGrB", "debugId": null}}]}