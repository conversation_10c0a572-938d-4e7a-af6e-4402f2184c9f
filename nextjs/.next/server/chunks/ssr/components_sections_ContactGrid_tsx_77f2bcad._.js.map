{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactGrid.tsx"], "sourcesContent": ["'use client';\n\n// import { EnvelopeIcon } from '@heroicons/react/24/outline';\n\nexport default function ContactGrid() {\n  const openCompose = (email: string, subject: string, body: string) => {\n    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <section className=\"contact-info\">\n      {/* <h2>\n        <EnvelopeIcon className=\"section-icon\" />\n        Get in Touch\n      </h2> */}\n      <div className=\"contact-methods\">\n        <div className=\"contact-method city-card\">\n          <div className=\"city-card__header\">\n            <h3 className=\"city-card__title\">GENERAL INQUIRIES</h3>\n          </div>\n          <div className=\"city-card__actions\">\n            <button\n              onClick={() => openCompose('<EMAIL>', 'General Inquiry', 'Hello, I have a question about the Chocolate & Art Show...')}\n              className=\"btn btn-primary\"\n            >\n              <EMAIL>\n            </button>\n          </div>\n        </div>\n        <div className=\"contact-method city-card\">\n          <div className=\"city-card__header\">\n            <h3 className=\"city-card__title\">ARTIST SUBMISSIONS</h3>\n          </div>\n          <div className=\"city-card__actions\">\n            <button\n              onClick={() => openCompose('<EMAIL>', 'Artist Submission — Dallas/September', 'Links + portfolio + dimensions')}\n              className=\"btn btn-primary\"\n            >\n              SUBMIT YOUR WORK\n            </button>\n          </div>\n        </div>\n        <div className=\"contact-method city-card\">\n          <div className=\"city-card__header\">\n            <h3 className=\"city-card__title\">VENDOR APPLICATIONS</h3>\n          </div>\n          <div className=\"city-card__actions\">\n            <button\n              onClick={() => openCompose('<EMAIL>', 'Vendor Application — Dallas/September', 'Line sheet + power needs')}\n              className=\"btn btn-primary\"\n            >\n              APPLY TO SELL\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,MAAM,cAAc,CAAC,OAAe,SAAiB;QACnD,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,SAAS,EAAE,mBAAmB,SAAS,MAAM,EAAE,mBAAmB,OAAO;QAC3G,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBAKjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,gCAAgC,mBAAmB;gCAC9E,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,6BAA6B,wCAAwC;gCAChG,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,gCAAgC,yCAAyC;gCACpG,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}