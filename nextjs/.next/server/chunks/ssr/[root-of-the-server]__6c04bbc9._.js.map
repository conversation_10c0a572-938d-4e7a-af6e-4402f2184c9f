{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/FaqSections.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  TicketIcon,\n  MapPinIcon,\n  PaintBrushIcon\n} from '@heroicons/react/24/outline';\n\ninterface FaqItem {\n  question: string;\n  answer: string | React.ReactElement;\n}\n\ninterface FaqSection {\n  title: string;\n  icon: React.ComponentType<{ className?: string }>;\n  items: FaqItem[];\n}\n\nexport default function FaqSections() {\n  const [activeItems, setActiveItems] = useState<Set<string>>(new Set());\n\n  const toggleFAQ = (sectionIndex: number, itemIndex: number) => {\n    const key = `${sectionIndex}-${itemIndex}`;\n    const newActiveItems = new Set(activeItems);\n\n    if (activeItems.has(key)) {\n      newActiveItems.delete(key);\n    } else {\n      newActiveItems.add(key);\n    }\n\n    setActiveItems(newActiveItems);\n  };\n\n  const faqSections: FaqSection[] = [\n    {\n      title: 'General Information',\n      icon: TicketIcon,\n      items: [\n        {\n          question: 'What is Chocolate & Art Show?',\n          answer: 'Chocolate & Art Show is an immersive experience combining visual art, live music, body painting, and artisan chocolate. It\\'s a 21+ event featuring local artists, live performances, and interactive art experiences in an intimate gallery setting.'\n        },\n        {\n          question: 'When and where is the Dallas event?',\n          answer: (\n            <>\n              <p><strong>Dates:</strong> September 18-19, 2025 (Thursday & Friday)</p>\n              <p><strong>Time:</strong> Doors open at 7:00 PM, last entry at 12:30 AM</p>\n              <p><strong>Venue:</strong> Lofty Spaces, 816 Montgomery St, Dallas, TX 75215</p>\n            </>\n          )\n        },\n        {\n          question: 'Is this event 21+ only?',\n          answer: (\n            <p><strong>Yes, this is strictly a 21+ event.</strong> Valid government-issued photo ID is required for entry. No exceptions will be made.</p>\n          )\n        },\n        {\n          question: 'What should I expect at the event?',\n          answer: (\n            <>\n              <p>You&apos;ll experience:</p>\n              <p>• Live art creation and body painting performances<br />\n                • Curated gallery of local artists&apos; work<br />\n                • Live music and DJ sets<br />\n                • Artisan chocolate tastings<br />\n                • Interactive art installations<br />\n                • Opportunity to purchase original artwork</p>\n            </>\n          )\n        }\n      ]\n    },\n    {\n      title: 'Tickets & Entry',\n      icon: TicketIcon,\n      items: [\n        {\n          question: 'How do I buy tickets?',\n          answer: 'Tickets are available through Eventbrite. You can purchase them on our website or directly through the Eventbrite link. We recommend buying in advance as this is a limited capacity event.'\n        },\n        {\n          question: 'Are tickets different for Thursday vs Friday?',\n          answer: 'Each night is a separate event with different artists and performances. You need separate tickets for each night you want to attend. Many guests choose to attend both nights for the full experience.'\n        },\n        {\n          question: 'Can I get a refund if I can\\'t attend?',\n          answer: 'Tickets are generally non-refundable. However, if the event is cancelled or postponed, full refunds will be issued. Please check our refund policy on Eventbrite for specific terms.'\n        },\n        {\n          question: 'Is re-entry allowed?',\n          answer: (\n            <p><strong>No, re-entry is not permitted.</strong> Once you leave the venue, you cannot return. Please plan accordingly and make sure you have everything you need before leaving.</p>\n          )\n        }\n      ]\n    },\n    {\n      title: 'Venue & Logistics',\n      icon: MapPinIcon,\n      items: [\n        {\n          question: 'Is parking available?',\n          answer: 'Yes, parking is available at the venue, but spaces are limited. We recommend arriving early or using rideshare services like Uber or Lyft for convenience.'\n        },\n        {\n          question: 'Is the venue accessible?',\n          answer: 'Yes, Lofty Spaces is ADA accessible. If you have specific accessibility needs, please contact us in advance so we can ensure the best possible experience.'\n        },\n        {\n          question: 'What\\'s the dress code?',\n          answer: 'Come as you are! We encourage creative expression and comfortable attire. Many guests dress up for the occasion, but there\\'s no strict dress code. Just be prepared for an evening of art and music.'\n        },\n        {\n          question: 'Can I bring a bag or purse?',\n          answer: 'Small bags and purses are allowed, but large bags or backpacks may be subject to search. We recommend bringing only essentials for the evening.'\n        }\n      ]\n    },\n    {\n      title: 'Art & Purchases',\n      icon: PaintBrushIcon,\n      items: [\n        {\n          question: 'Can I buy artwork at the event?',\n          answer: 'Yes! Most artwork displayed is available for purchase. We handle all transactions and provide secure payment processing. You can take smaller pieces home the same night or arrange pickup/delivery for larger works.'\n        },\n        {\n          question: 'How do I participate as an artist?',\n          answer: 'We\\'re always looking for talented local artists! Visit our Artists page to apply. We accept visual artists, live painters, body artists, and performance artists. Applications are reviewed on a rolling basis.'\n        },\n        {\n          question: 'Can I take photos at the event?',\n          answer: 'Personal photography is welcome! We encourage sharing your experience on social media. However, please be respectful of other guests and ask permission before photographing people. Professional photography requires prior approval.'\n        }\n      ]\n    }\n  ];\n\n  return (\n    <div className=\"faq-sections\">\n      {faqSections.map((section, sectionIndex) => (\n        <section key={sectionIndex} className=\"faq-section\">\n          <h2>\n            <section.icon className=\"faq-section-icon\" />\n            {section.title}\n          </h2>\n          <div className=\"faq-grid\">\n            {section.items.map((item, itemIndex) => {\n              const key = `${sectionIndex}-${itemIndex}`;\n              const isActive = activeItems.has(key);\n\n              return (\n                <div key={itemIndex} className=\"faq-item\">\n                  <button\n                    className={`faq-question ${isActive ? 'active' : ''}`}\n                    onClick={() => toggleFAQ(sectionIndex, itemIndex)}\n                    aria-expanded={isActive}\n                    aria-controls={`faq-answer-${key}`}\n                    id={`faq-question-${key}`}\n                  >\n                    {item.question}\n                  </button>\n                  <div\n                    className={`faq-answer ${isActive ? 'active' : ''}`}\n                    id={`faq-answer-${key}`}\n                    aria-labelledby={`faq-question-${key}`}\n                    role=\"region\"\n                  >\n                    {typeof item.answer === 'string' ? <p>{item.answer}</p> : item.answer}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </section>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAc,IAAI;IAEhE,MAAM,YAAY,CAAC,cAAsB;QACvC,MAAM,MAAM,GAAG,aAAa,CAAC,EAAE,WAAW;QAC1C,MAAM,iBAAiB,IAAI,IAAI;QAE/B,IAAI,YAAY,GAAG,CAAC,MAAM;YACxB,eAAe,MAAM,CAAC;QACxB,OAAO;YACL,eAAe,GAAG,CAAC;QACrB;QAEA,eAAe;IACjB;IAEA,MAAM,cAA4B;QAChC;YACE,OAAO;YACP,MAAM,iOAAU;YAChB,OAAO;gBACL;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,sBACE;;0CACE,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAe;;;;;;;0CAC1B,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAc;;;;;;;0CACzB,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAe;;;;;;;;;gBAGhC;gBACA;oBACE,UAAU;oBACV,sBACE,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAA2C;;;;;;;gBAE1D;gBACA;oBACE,UAAU;oBACV,sBACE;;0CACE,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;oCAAE;kDAAkD,8OAAC;;;;;oCAAK;kDACZ,8OAAC;;;;;oCAAK;kDAC3B,8OAAC;;;;;oCAAK;kDACF,8OAAC;;;;;oCAAK;kDACH,8OAAC;;;;;oCAAK;;;;;;;;;gBAI7C;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,iOAAU;YAChB,OAAO;gBACL;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,sBACE,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAuC;;;;;;;gBAEtD;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,iOAAU;YAChB,OAAO;gBACL;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,6OAAc;YACpB,OAAO;gBACL;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,QAAQ;gBACV;aACD;QACH;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,YAAY,GAAG,CAAC,CAAC,SAAS,6BACzB,8OAAC;gBAA2B,WAAU;;kCACpC,8OAAC;;0CACC,8OAAC,QAAQ,IAAI;gCAAC,WAAU;;;;;;4BACvB,QAAQ,KAAK;;;;;;;kCAEhB,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;4BACxB,MAAM,MAAM,GAAG,aAAa,CAAC,EAAE,WAAW;4BAC1C,MAAM,WAAW,YAAY,GAAG,CAAC;4BAEjC,qBACE,8OAAC;gCAAoB,WAAU;;kDAC7B,8OAAC;wCACC,WAAW,CAAC,aAAa,EAAE,WAAW,WAAW,IAAI;wCACrD,SAAS,IAAM,UAAU,cAAc;wCACvC,iBAAe;wCACf,iBAAe,CAAC,WAAW,EAAE,KAAK;wCAClC,IAAI,CAAC,aAAa,EAAE,KAAK;kDAExB,KAAK,QAAQ;;;;;;kDAEhB,8OAAC;wCACC,WAAW,CAAC,WAAW,EAAE,WAAW,WAAW,IAAI;wCACnD,IAAI,CAAC,WAAW,EAAE,KAAK;wCACvB,mBAAiB,CAAC,aAAa,EAAE,KAAK;wCACtC,MAAK;kDAEJ,OAAO,KAAK,MAAM,KAAK,yBAAW,8OAAC;sDAAG,KAAK,MAAM;;;;;mDAAQ,KAAK,MAAM;;;;;;;+BAhB/D;;;;;wBAoBd;;;;;;;eA/BU;;;;;;;;;;AAqCtB", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/monoton_c2604b7a.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"monoton_c2604b7a-module__wEEQta__className\",\n  \"variable\": \"monoton_c2604b7a-module__wEEQta__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/monoton_c2604b7a.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Monoton%22,%22arguments%22:[{%22weight%22:%22400%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22variable%22:%22--font-monoton%22}],%22variableName%22:%22monoton%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Monoton', 'Monoton Fallback'\",\n        fontWeight: 400,\nfontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,kKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,YAAY;QACpB,WAAW;IAEP;AACJ;AAEA,IAAI,kKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,kKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/lib/fonts.ts"], "sourcesContent": ["/**\n * Font configuration for Chocolate & Art Show\n * Using Next.js font optimization for performance\n */\n\nimport { Monoton } from 'next/font/google';\n\n// Monoton font for hero sections and neon effects\nexport const monoton = Monoton({\n  weight: '400', // Monoton only comes in 400 weight\n  subsets: ['latin'],\n  display: 'swap',\n  variable: '--font-monoton',\n});\n\n// Export font class names for use in components\nexport const fontClasses = {\n  monoton: monoton.className,\n  monotonVariable: monoton.variable,\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAaM,MAAM,cAAc;IACzB,SAAS,sJAAO,CAAC,SAAS;IAC1B,iBAAiB,sJAAO,CAAC,QAAQ;AACnC", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { monoton } from \"@/lib/fonts\";\n\nexport default function HeroSection() {\n  return (\n    <section className=\"hero\" aria-label=\"Dallas Hero\">\n      <div className=\"overlay\" aria-hidden=\"true\"></div>\n\n      <div className=\"hero-content\">\n        {/* Hidden h1 for accessibility */}\n        <h1 className=\"sr-only\">Chocolate & Art Show Dallas - September 18-19, 2025</h1>\n\n        {/* Neon Sign */}\n        <div className=\"neon-sign\" role=\"img\" aria-label=\"Chocolate and Art Show neon sign\">\n          <div className={`neon-line line-1 ${monoton.className}`} data-text=\"CHOCOLATE\" aria-hidden=\"true\">\n            CHOCOLATE\n          </div>\n          <div className={`neon-line line-2 ${monoton.className}`} data-text=\"& ART\" aria-hidden=\"true\">\n            AND ART\n          </div>\n          <div className={`neon-line line-3 ${monoton.className}`} data-text=\"SHOW\" aria-hidden=\"true\">\n            SHOW\n          </div>\n        </div>\n\n        {/* Cialdini-framed CTAs */}\n        <div className=\"hero-cta\">\n          <h2 className=\"cta-title\">Dallas — September 18-19, 2025</h2>\n          <div className=\"ticket-buttons\">\n            <a\n              className=\"btn-textured\"\n              href=\"/events/dallas-tx-2025-09-18-19/\"\n              aria-label=\"Dallas Tickets - 2 nights only\"\n            >\n              Dallas Tickets — 2 nights only (Thu + Fri). Join 10,000+ art lovers.\n            </a>\n          </div>\n          <div className=\"cta-secondary\">\n            <a href=\"#subscribe\" aria-label=\"Join the Insider List\">\n              Join the Insider List — early‑bird drops + lineup first.\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAO,cAAW;;0BACnC,8OAAC;gBAAI,WAAU;gBAAU,eAAY;;;;;;0BAErC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;kCAAU;;;;;;kCAGxB,8OAAC;wBAAI,WAAU;wBAAY,MAAK;wBAAM,cAAW;;0CAC/C,8OAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,4LAAO,CAAC,SAAS,EAAE;gCAAE,aAAU;gCAAY,eAAY;0CAAO;;;;;;0CAGlG,8OAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,4LAAO,CAAC,SAAS,EAAE;gCAAE,aAAU;gCAAQ,eAAY;0CAAO;;;;;;0CAG9F,8OAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,4LAAO,CAAC,SAAS,EAAE;gCAAE,aAAU;gCAAO,eAAY;0CAAO;;;;;;;;;;;;kCAM/F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAY;;;;;;0CAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,cAAW;8CACZ;;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAa,cAAW;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpE", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/CtaBar.tsx"], "sourcesContent": ["'use client';\n\nexport default function CtaBar() {\n  const openCompose = (email: string, subject: string, body: string) => {\n    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <section className=\"cta-bar\" aria-label=\"Apply to participate\">\n      <div className=\"cta-bar-container\">\n        <h2 className=\"cta-bar-title\">Join the Show</h2>\n        <div className=\"cta-buttons\">\n          {/* Artists CTA (Authority + Commitment) */}\n          <button \n            className=\"cta-btn cta-btn--artists\"\n            onClick={() => openCompose('<EMAIL>', 'Artist Submission — Dallas/September', 'Links + portfolio + dimensions')}\n            aria-label=\"Submit your artwork\"\n          >\n            <div className=\"cta-btn__content\">\n              <h3 className=\"cta-btn__title\">Show Your Work</h3>\n              <p className=\"cta-btn__subtitle\">Early submissions get priority placement.</p>\n            </div>\n          </button>\n\n          {/* Vendors CTA (Social Proof + Reciprocity) */}\n          <button \n            className=\"cta-btn cta-btn--vendors\"\n            onClick={() => openCompose('<EMAIL>', 'Vendor Application — Dallas/September', 'Line sheet + power needs')}\n            aria-label=\"Apply to sell with us\"\n          >\n            <div className=\"cta-btn__content\">\n              <h3 className=\"cta-btn__title\">Sell With Us</h3>\n              <p className=\"cta-btn__subtitle\">Average 3k+ attendees per night.</p>\n            </div>\n          </button>\n\n          {/* Music CTA (Authority + Commitment) */}\n          <button \n            className=\"cta-btn cta-btn--music\"\n            onClick={() => openCompose('<EMAIL>', 'Music/DJ Submission — Dallas/September', 'Links + tech rider')}\n            aria-label=\"Apply to perform\"\n          >\n            <div className=\"cta-btn__content\">\n              <h3 className=\"cta-btn__title\">Play Our Stage</h3>\n              <p className=\"cta-btn__subtitle\">Curated local lineup; limited slots.</p>\n            </div>\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,cAAc,CAAC,OAAe,SAAiB;QACnD,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,SAAS,EAAE,mBAAmB,SAAS,MAAM,EAAE,mBAAmB,OAAO;QAC3G,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAU,cAAW;kBACtC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgB;;;;;;8BAC9B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,YAAY,gCAAgC,wCAAwC;4BACnG,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiB;;;;;;kDAC/B,8OAAC;wCAAE,WAAU;kDAAoB;;;;;;;;;;;;;;;;;sCAKrC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,YAAY,gCAAgC,yCAAyC;4BACpG,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiB;;;;;;kDAC/B,8OAAC;wCAAE,WAAU;kDAAoB;;;;;;;;;;;;;;;;;sCAKrC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,YAAY,gCAAgC,0CAA0C;4BACrG,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiB;;;;;;kDAC/B,8OAAC;wCAAE,WAAU;kDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/GallerySection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport Image from 'next/image';\n\ninterface GalleryImage {\n  src: string;\n  alt: string;\n}\n\nconst galleryImages: GalleryImage[] = [\n  { src: '/images/gallery/01.jpg', alt: 'Chocolate & Art Show gallery image 1' },\n  { src: '/images/gallery/02.jpg', alt: 'Chocolate & Art Show gallery image 2' },\n  { src: '/images/gallery/03.jpg', alt: 'Chocolate & Art Show gallery image 3' },\n  { src: '/images/gallery/04.jpg', alt: 'Chocolate & Art Show gallery image 4' },\n  { src: '/images/gallery/05.jpg', alt: 'Chocolate & Art Show gallery image 5' },\n  { src: '/images/gallery/06.jpg', alt: 'Chocolate & Art Show gallery image 6' },\n];\n\nexport default function GallerySection() {\n  const [selectedImage, setSelectedImage] = useState<number | null>(null);\n\n  useEffect(() => {\n    // Initialize gallery functionality\n    // This will be enhanced with the actual gallery.js functionality later\n  }, []);\n\n  const openGallery = (index: number) => {\n    setSelectedImage(index);\n  };\n\n  const closeGallery = () => {\n    setSelectedImage(null);\n  };\n\n  const nextImage = useCallback(() => {\n    if (selectedImage !== null) {\n      setSelectedImage((selectedImage + 1) % galleryImages.length);\n    }\n  }, [selectedImage]);\n\n  const prevImage = useCallback(() => {\n    if (selectedImage !== null) {\n      setSelectedImage(selectedImage === 0 ? galleryImages.length - 1 : selectedImage - 1);\n    }\n  }, [selectedImage]);\n\n  // Keyboard navigation for lightbox\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (selectedImage === null) return;\n\n      switch (event.key) {\n        case 'Escape':\n          closeGallery();\n          break;\n        case 'ArrowLeft':\n          prevImage();\n          break;\n        case 'ArrowRight':\n          nextImage();\n          break;\n      }\n    };\n\n    if (selectedImage !== null) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when lightbox is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = '';\n    };\n  }, [selectedImage, nextImage, prevImage]);\n\n  return (\n    <>\n      <section id=\"gallery\" className=\"gallery-section\" aria-label=\"Event gallery\">\n        <div className=\"container\">\n          <h2 style={{\n            textAlign: 'center',\n            fontSize: 'clamp(2rem, 5vw, 3rem)',\n            fontWeight: 800,\n            marginBottom: '3rem',\n            color: 'var(--white)',\n            textTransform: 'uppercase',\n            letterSpacing: '0.1em'\n          }}>\n            Gallery\n          </h2>\n          <div className=\"gallery-grid\">\n            {galleryImages.map((image, index) => (\n              <div\n                key={index}\n                className=\"gallery-item\"\n                onClick={() => openGallery(index)}\n                onKeyDown={(e) => e.key === 'Enter' && openGallery(index)}\n                tabIndex={0}\n                role=\"button\"\n                aria-label={`Open gallery image ${index + 1}`}\n              >\n                <Image\n                  src={image.src}\n                  alt={image.alt}\n                  width={400}\n                  height={300}\n                  loading=\"lazy\"\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Gallery Lightbox */}\n      {selectedImage !== null && (\n        <div\n          className=\"gallery-lightbox\"\n          onClick={closeGallery}\n          role=\"dialog\"\n          aria-modal=\"true\"\n          aria-label=\"Gallery lightbox\"\n        >\n          <div className=\"gallery-lightbox-content\" onClick={(e) => e.stopPropagation()}>\n            <button className=\"gallery-close\" onClick={closeGallery} aria-label=\"Close gallery\">\n              ×\n            </button>\n            <button className=\"gallery-prev\" onClick={prevImage} aria-label=\"Previous image\">\n              ‹\n            </button>\n            <Image\n              src={galleryImages[selectedImage].src}\n              alt={galleryImages[selectedImage].alt}\n              width={800}\n              height={600}\n              className=\"gallery-lightbox-image\"\n            />\n            <button className=\"gallery-next\" onClick={nextImage} aria-label=\"Next image\">\n              ›\n            </button>\n            <div className=\"sr-only\" aria-live=\"polite\">\n              Image {selectedImage + 1} of {galleryImages.length}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,gBAAgC;IACpC;QAAE,KAAK;QAA0B,KAAK;IAAuC;IAC7E;QAAE,KAAK;QAA0B,KAAK;IAAuC;IAC7E;QAAE,KAAK;QAA0B,KAAK;IAAuC;IAC7E;QAAE,KAAK;QAA0B,KAAK;IAAuC;IAC7E;QAAE,KAAK;QAA0B,KAAK;IAAuC;IAC7E;QAAE,KAAK;QAA0B,KAAK;IAAuC;CAC9E;AAEc,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAgB;IAElE,IAAA,kNAAS,EAAC;IACR,mCAAmC;IACnC,uEAAuE;IACzE,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,iBAAiB;IACnB;IAEA,MAAM,eAAe;QACnB,iBAAiB;IACnB;IAEA,MAAM,YAAY,IAAA,oNAAW,EAAC;QAC5B,IAAI,kBAAkB,MAAM;YAC1B,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,cAAc,MAAM;QAC7D;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,YAAY,IAAA,oNAAW,EAAC;QAC5B,IAAI,kBAAkB,MAAM;YAC1B,iBAAiB,kBAAkB,IAAI,cAAc,MAAM,GAAG,IAAI,gBAAgB;QACpF;IACF,GAAG;QAAC;KAAc;IAElB,mCAAmC;IACnC,IAAA,kNAAS,EAAC;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,kBAAkB,MAAM;YAE5B,OAAQ,MAAM,GAAG;gBACf,KAAK;oBACH;oBACA;gBACF,KAAK;oBACH;oBACA;gBACF,KAAK;oBACH;oBACA;YACJ;QACF;QAEA,IAAI,kBAAkB,MAAM;YAC1B,SAAS,gBAAgB,CAAC,WAAW;YACrC,4CAA4C;YAC5C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAe;QAAW;KAAU;IAExC,qBACE;;0BACE,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;gBAAkB,cAAW;0BAC3D,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,OAAO;gCACT,WAAW;gCACX,UAAU;gCACV,YAAY;gCACZ,cAAc;gCACd,OAAO;gCACP,eAAe;gCACf,eAAe;4BACjB;sCAAG;;;;;;sCAGH,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,YAAY;oCACnD,UAAU;oCACV,MAAK;oCACL,cAAY,CAAC,mBAAmB,EAAE,QAAQ,GAAG;8CAE7C,cAAA,8OAAC,wIAAK;wCACJ,KAAK,MAAM,GAAG;wCACd,KAAK,MAAM,GAAG;wCACd,OAAO;wCACP,QAAQ;wCACR,SAAQ;;;;;;mCAbL;;;;;;;;;;;;;;;;;;;;;YAsBd,kBAAkB,sBACjB,8OAAC;gBACC,WAAU;gBACV,SAAS;gBACT,MAAK;gBACL,cAAW;gBACX,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;oBAA2B,SAAS,CAAC,IAAM,EAAE,eAAe;;sCACzE,8OAAC;4BAAO,WAAU;4BAAgB,SAAS;4BAAc,cAAW;sCAAgB;;;;;;sCAGpF,8OAAC;4BAAO,WAAU;4BAAe,SAAS;4BAAW,cAAW;sCAAiB;;;;;;sCAGjF,8OAAC,wIAAK;4BACJ,KAAK,aAAa,CAAC,cAAc,CAAC,GAAG;4BACrC,KAAK,aAAa,CAAC,cAAc,CAAC,GAAG;4BACrC,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;4BAAO,WAAU;4BAAe,SAAS;4BAAW,cAAW;sCAAa;;;;;;sCAG7E,8OAAC;4BAAI,WAAU;4BAAU,aAAU;;gCAAS;gCACnC,gBAAgB;gCAAE;gCAAK,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;AAOhE", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ApplicationGrid.tsx"], "sourcesContent": ["'use client';\n\ninterface ApplicationCardProps {\n  title: string;\n  emoji: string;\n  description: string;\n  benefits: string[];\n  buttonText: string;\n  emailSubject: string;\n  emailBody: string;\n}\n\nfunction ApplicationCard({\n  title,\n  emoji,\n  description,\n  benefits,\n  buttonText,\n  emailSubject,\n  emailBody\n}: ApplicationCardProps) {\n  const openCompose = () => {\n    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <article className=\"application-card city-card\">\n      <div className=\"city-card__header\">\n        <h2 className=\"city-card__title\">{emoji} {title}</h2>\n      </div>\n      <div className=\"city-card__details\">\n        <div className=\"city-card__detail\">{description}</div>\n        <ul>\n          {benefits.map((benefit, index) => (\n            <li key={index}>{benefit}</li>\n          ))}\n        </ul>\n      </div>\n      <div className=\"city-card__actions\">\n        <button className=\"btn btn-primary\" onClick={openCompose}>\n          {buttonText}\n        </button>\n      </div>\n    </article>\n  );\n}\n\nexport default function ApplicationGrid() {\n  const applications = [\n    {\n      title: 'Visual Artists',\n      emoji: '🎨',\n      description: 'Showcase your paintings, sculptures, digital art, or mixed media pieces in our curated gallery space.',\n      benefits: [\n        'Prime gallery wall space',\n        'Professional lighting',\n        'Direct sales opportunities',\n        'Artist meet & greet sessions',\n        'Social media promotion'\n      ],\n      buttonText: 'Apply to Exhibit',\n      emailSubject: 'Visual Artist Application - Dallas 2025',\n      emailBody: `Hi! I would like to apply to showcase my visual art at Chocolate & Art Show Dallas.\n\nArtist Name:\nArt Medium/Style:\nWebsite/Portfolio:\nInstagram:\nBrief Description of Work:\n\nI understand this is a 21+ event and I am available September 18-19, 2025.\n\nThank you!`\n    },\n    {\n      title: 'Body Artists',\n      emoji: '✨',\n      description: 'Create live body art and interactive painting experiences for our guests throughout the evening.',\n      benefits: [\n        'Live painting sessions',\n        'Interactive guest experiences',\n        'Professional setup provided',\n        'Materials and supplies included',\n        'Performance compensation'\n      ],\n      buttonText: 'Apply to Perform',\n      emailSubject: 'Body Artist Application - Dallas 2025',\n      emailBody: `Hi! I would like to apply to perform body art at Chocolate & Art Show Dallas.\n\nArtist Name:\nExperience with Body Art:\nWebsite/Portfolio:\nInstagram:\nBrief Description of Performance Style:\n\nI understand this is a 21+ event and I am available September 18-19, 2025.\n\nThank you!`\n    },\n    {\n      title: 'Live Painters',\n      emoji: '🖌️',\n      description: 'Paint live during the event, creating art in real-time while guests watch your creative process unfold.',\n      benefits: [\n        'Dedicated painting station',\n        'Easel and basic supplies provided',\n        'Audience interaction encouraged',\n        'Finished pieces available for sale',\n        'Artist spotlight features'\n      ],\n      buttonText: 'Apply to Paint Live',\n      emailSubject: 'Live Painter Application - Dallas 2025',\n      emailBody: `Hi! I would like to apply to paint live at Chocolate & Art Show Dallas.\n\nArtist Name:\nPainting Style/Medium:\nWebsite/Portfolio:\nInstagram:\nExperience with Live Painting:\n\nI understand this is a 21+ event and I am available September 18-19, 2025.\n\nThank you!`\n    },\n    {\n      title: 'Performance Artists',\n      emoji: '🎭',\n      description: 'Bring interactive performances, installations, or unique artistic experiences to our immersive environment.',\n      benefits: [\n        'Flexible performance space',\n        'Technical support available',\n        'Multiple performance slots',\n        'Creative freedom encouraged',\n        'Professional documentation'\n      ],\n      buttonText: 'Apply to Perform',\n      emailSubject: 'Performance Artist Application - Dallas 2025',\n      emailBody: `Hi! I would like to apply to perform at Chocolate & Art Show Dallas.\n\nArtist/Group Name:\nType of Performance:\nWebsite/Portfolio:\nInstagram:\nTechnical Requirements:\nPerformance Duration:\n\nI understand this is a 21+ event and I am available September 18-19, 2025.\n\nThank you!`\n    }\n  ];\n\n  return (\n    <section className=\"application-grid\">\n      {applications.map((app, index) => (\n        <ApplicationCard key={index} {...app} />\n      ))}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAYA,SAAS,gBAAgB,EACvB,KAAK,EACL,KAAK,EACL,WAAW,EACX,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,SAAS,EACY;IACrB,MAAM,cAAc;QAClB,MAAM,YAAY,CAAC,yCAAyC,EAAE,mBAAmB,cAAc,MAAM,EAAE,mBAAmB,YAAY;QACtI,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoB;wBAAM;wBAAE;;;;;;;;;;;;0BAE5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC;kCACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;0CAAgB;+BAAR;;;;;;;;;;;;;;;;0BAIf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAO,WAAU;oBAAkB,SAAS;8BAC1C;;;;;;;;;;;;;;;;;AAKX;AAEe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,cAAc;YACd,WAAW,CAAC;;;;;;;;;;UAUR,CAAC;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,cAAc;YACd,WAAW,CAAC;;;;;;;;;;UAUR,CAAC;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,cAAc;YACd,WAAW,CAAC;;;;;;;;;;UAUR,CAAC;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,cAAc;YACd,WAAW,CAAC;;;;;;;;;;;UAWR,CAAC;QACP;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBAChB,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;gBAA6B,GAAG,GAAG;eAAd;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/InfoSection.tsx"], "sourcesContent": ["'use client';\n\nexport default function InfoSection() {\n  const openCompose = () => {\n    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent('Artist Application Question')}&body=${encodeURIComponent('Hi! I have a question about applying to participate in Chocolate & Art Show Dallas...')}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <section className=\"info-section\">\n      <div className=\"container\">\n        <h2>Application Information</h2>\n        <div className=\"info-grid\">\n          <div className=\"info-card\">\n            <h3>📅 Important Dates</h3>\n            <p>\n              <strong>Event:</strong> September 18-19, 2025<br />\n              <strong>Application Deadline:</strong> August 15, 2025<br />\n              <strong>Artist Notifications:</strong> August 25, 2025<br />\n              <strong>Setup:</strong> September 17, 2025\n            </p>\n          </div>\n\n          <div className=\"info-card\">\n            <h3>💰 Compensation & Sales</h3>\n            <p>\n              Selected artists receive performance fees plus the opportunity to sell work\n              directly to attendees. We provide payment processing and handle all transactions\n              with a small commission.\n            </p>\n          </div>\n\n          <div className=\"info-card\">\n            <h3>📍 Venue Details</h3>\n            <p>\n              <strong>Lofty Spaces</strong><br />\n              816 Montgomery St, Dallas, TX<br />\n              Professional gallery lighting, climate controlled, accessible venue with\n              dedicated artist areas.\n            </p>\n          </div>\n\n          <div className=\"info-card\">\n            <h3>🎯 What We&apos;re Looking For</h3>\n            <p>\n              Original, engaging artwork that complements our immersive chocolate and art theme.\n              We prioritize local Dallas artists and unique, interactive experiences.\n            </p>\n          </div>\n\n          <div className=\"info-card\">\n            <h3>📋 Application Requirements</h3>\n            <p>\n              Portfolio samples, artist statement, availability confirmation, and social media\n              links. Professional references preferred but not required.\n            </p>\n          </div>\n\n          <div className=\"info-card\">\n            <h3>❓ Questions?</h3>\n            <p>\n              <button className=\"btn btn-secondary\" onClick={openCompose}>\n                Contact Artist Coordinator\n              </button>\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,cAAc;QAClB,MAAM,YAAY,CAAC,yCAAyC,EAAE,mBAAmB,+BAA+B,MAAM,EAAE,mBAAmB,0FAA0F;QACrO,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;wCAAe;sDAAsB,8OAAC;;;;;sDAC9C,8OAAC;sDAAO;;;;;;wCAA8B;sDAAgB,8OAAC;;;;;sDACvD,8OAAC;sDAAO;;;;;;wCAA8B;sDAAgB,8OAAC;;;;;sDACvD,8OAAC;sDAAO;;;;;;wCAAe;;;;;;;;;;;;;sCAI3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;sDAAqB,8OAAC;;;;;wCAAK;sDACN,8OAAC;;;;;wCAAK;;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CACC,cAAA,8OAAC;wCAAO,WAAU;wCAAoB,SAAS;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E", "debugId": null}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/ContactGrid.tsx"], "sourcesContent": ["'use client';\n\n// import { EnvelopeIcon } from '@heroicons/react/24/outline';\n\nexport default function ContactGrid() {\n  const openCompose = (email: string, subject: string, body: string) => {\n    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <section className=\"contact-info\">\n      {/* <h2>\n        <EnvelopeIcon className=\"section-icon\" />\n        Get in Touch\n      </h2> */}\n      <div className=\"contact-methods\">\n        <div className=\"contact-method city-card\">\n          <div className=\"city-card__header\">\n            <h3 className=\"city-card__title\">GENERAL INQUIRIES</h3>\n          </div>\n          <div className=\"city-card__actions\">\n            <button\n              onClick={() => openCompose('<EMAIL>', 'General Inquiry', 'Hello, I have a question about the Chocolate & Art Show...')}\n              className=\"btn btn-primary\"\n            >\n              <EMAIL>\n            </button>\n          </div>\n        </div>\n        <div className=\"contact-method city-card\">\n          <div className=\"city-card__header\">\n            <h3 className=\"city-card__title\">ARTIST SUBMISSIONS</h3>\n          </div>\n          <div className=\"city-card__actions\">\n            <button\n              onClick={() => openCompose('<EMAIL>', 'Artist Submission — Dallas/September', 'Links + portfolio + dimensions')}\n              className=\"btn btn-primary\"\n            >\n              SUBMIT YOUR WORK\n            </button>\n          </div>\n        </div>\n        <div className=\"contact-method city-card\">\n          <div className=\"city-card__header\">\n            <h3 className=\"city-card__title\">VENDOR APPLICATIONS</h3>\n          </div>\n          <div className=\"city-card__actions\">\n            <button\n              onClick={() => openCompose('<EMAIL>', 'Vendor Application — Dallas/September', 'Line sheet + power needs')}\n              className=\"btn btn-primary\"\n            >\n              APPLY TO SELL\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,MAAM,cAAc,CAAC,OAAe,SAAiB;QACnD,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,SAAS,EAAE,mBAAmB,SAAS,MAAM,EAAE,mBAAmB,OAAO;QAC3G,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBAKjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,gCAAgC,mBAAmB;gCAC9E,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,6BAA6B,wCAAwC;gCAChG,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,gCAAgC,yCAAyC;gCACpG,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventDetails.tsx"], "sourcesContent": ["'use client';\n\nexport default function EventDetails() {\n  const openCompose = (email: string, subject: string, body: string) => {\n    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <section className=\"event-details\">\n      <article className=\"detail-card\">\n        <h3>📅 Event Details</h3>\n        <p><strong>Dates:</strong> September 18-19, 2025 (Thursday & Friday)</p>\n        <p><strong>Doors Open:</strong> 7:00 PM both nights</p>\n        <p><strong>Last Entry:</strong> 12:30 AM</p>\n        <p><strong>Age Requirement:</strong> 21+ (Valid ID Required)</p>\n        <p><strong>Dress Code:</strong> Come as you are! Creative expression encouraged.</p>\n      </article>\n\n      <article className=\"detail-card\">\n        <h3>📍 Venue Information</h3>\n        <p><strong>Lofty Spaces</strong></p>\n        <p>816 Montgomery St<br />Dallas, TX 75215</p>\n        <p><strong>Parking:</strong> Available at venue (limited spaces)</p>\n        <p><strong>Rideshare:</strong> Uber/Lyft recommended</p>\n        <p><strong>Public Transit:</strong> Check Dallas transit for routes</p>\n      </article>\n\n      <article className=\"detail-card\">\n        <h3>🎨 What to Expect</h3>\n        <ul>\n          <li>Live art creation and installations</li>\n          <li>Body painting performances</li>\n          <li>Curated local music and DJs</li>\n          <li>Artisan chocolate tastings</li>\n          <li>Interactive art experiences</li>\n          <li>Immersive lighting and atmosphere</li>\n        </ul>\n      </article>\n\n      <article className=\"detail-card\">\n        <h3>🎫 Ticket Information</h3>\n        <p><strong>Capacity:</strong> Limited intimate setting</p>\n        <p><strong>Advance Purchase:</strong> Strongly recommended</p>\n        <p><strong>No Re-entry:</strong> Once you leave, re-entry not permitted</p>\n        <p><strong>Refunds:</strong> See Eventbrite policy</p>\n        <p>\n          <strong>Questions:</strong>{' '}\n          <button\n            onClick={() => openCompose('<EMAIL>', 'Dallas Event Question', 'I have a question about the Dallas Chocolate & Art Show...')}\n            style={{\n              background: 'none',\n              border: 'none',\n              color: 'var(--pink-2)',\n              textDecoration: 'underline',\n              cursor: 'pointer',\n              fontSize: 'inherit',\n              fontFamily: 'inherit'\n            }}\n          >\n            Contact us\n          </button>\n        </p>\n      </article>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,cAAc,CAAC,OAAe,SAAiB;QACnD,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,SAAS,EAAE,mBAAmB,SAAS,MAAM,EAAE,mBAAmB,OAAO;QAC3G,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAe;;;;;;;kCAC1B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAoB;;;;;;;kCAC/B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAoB;;;;;;;kCAC/B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAyB;;;;;;;kCACpC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAoB;;;;;;;;;;;;;0BAGjC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;kCAAE,cAAA,8OAAC;sCAAO;;;;;;;;;;;kCACX,8OAAC;;4BAAE;0CAAiB,8OAAC;;;;;4BAAK;;;;;;;kCAC1B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAiB;;;;;;;kCAC5B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAmB;;;;;;;kCAC9B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAwB;;;;;;;;;;;;;0BAGrC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;0CACC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;0BAIR,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAkB;;;;;;;kCAC7B,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAA0B;;;;;;;kCACrC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAqB;;;;;;;kCAChC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAiB;;;;;;;kCAC5B,8OAAC;;0CACC,8OAAC;0CAAO;;;;;;4BAAoB;0CAC5B,8OAAC;gCACC,SAAS,IAAM,YAAY,gCAAgC,yBAAyB;gCACpF,OAAO;oCACL,YAAY;oCACZ,QAAQ;oCACR,OAAO;oCACP,gBAAgB;oCAChB,QAAQ;oCACR,UAAU;oCACV,YAAY;gCACd;0CACD;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventCta.tsx"], "sourcesContent": ["'use client';\n\nexport default function EventCta() {\n  const openCompose = (email: string, subject: string, body: string) => {\n    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.location.href = mailtoUrl;\n  };\n\n  return (\n    <section style={{ textAlign: 'center', margin: '4rem 0' }}>\n      <h2 style={{ fontSize: '2rem', marginBottom: '2rem', color: 'var(--white)' }}>\n        Ready to Experience Dallas?\n      </h2>\n      <div className=\"ticket-section\">\n        <a \n          href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\"\n          className=\"ticket-btn\" \n          target=\"_blank\" \n          rel=\"noopener\"\n        >\n          Get Your Tickets Now\n        </a>\n      </div>\n      <p style={{ color: 'var(--fg)', marginTop: '1rem' }}>\n        Questions?{' '}\n        <button\n          onClick={() => openCompose('<EMAIL>', 'Dallas Event Question', 'I have a question about the Dallas Chocolate & Art Show...')}\n          style={{\n            background: 'none',\n            border: 'none',\n            color: 'var(--pink-2)',\n            textDecoration: 'underline',\n            cursor: 'pointer',\n            fontSize: 'inherit',\n            fontFamily: 'inherit'\n          }}\n        >\n          Contact us\n        </button>\n      </p>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,cAAc,CAAC,OAAe,SAAiB;QACnD,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,SAAS,EAAE,mBAAmB,SAAS,MAAM,EAAE,mBAAmB,OAAO;QAC3G,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAQ,OAAO;YAAE,WAAW;YAAU,QAAQ;QAAS;;0BACtD,8OAAC;gBAAG,OAAO;oBAAE,UAAU;oBAAQ,cAAc;oBAAQ,OAAO;gBAAe;0BAAG;;;;;;0BAG9E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,WAAU;oBACV,QAAO;oBACP,KAAI;8BACL;;;;;;;;;;;0BAIH,8OAAC;gBAAE,OAAO;oBAAE,OAAO;oBAAa,WAAW;gBAAO;;oBAAG;oBACxC;kCACX,8OAAC;wBACC,SAAS,IAAM,YAAY,gCAAgC,yBAAyB;wBACpF,OAAO;4BACL,YAAY;4BACZ,QAAQ;4BACR,OAAO;4BACP,gBAAgB;4BAChB,QAAQ;4BACR,UAAU;4BACV,YAAY;wBACd;kCACD;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 2035, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/sections/EventsGrid.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Image from 'next/image';\nimport { TicketIcon } from '@heroicons/react/24/outline';\n\nexport default function EventsGrid() {\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);\n  const [selectedCity, setSelectedCity] = useState('');\n  const [email, setEmail] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n\n  // Sticky ticket button state\n  const [isTicketSticky, setIsTicketSticky] = useState(false);\n  const [, setScrollY] = useState(0);\n  const [isWiggling, setIsWiggling] = useState(false);\n  const modalContentRef = useRef<HTMLDivElement>(null);\n  const ticketButtonRef = useRef<HTMLDivElement>(null);\n\n  const openNotificationModal = (city: string) => {\n    setSelectedCity(city);\n    setIsModalOpen(true);\n    setEmail('');\n    setSubmitMessage('');\n  };\n\n  const openDetailsModal = (city: string) => {\n    setSelectedCity(city);\n    setIsDetailsModalOpen(true);\n  };\n\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setSelectedCity('');\n    setEmail('');\n    setSubmitMessage('');\n  };\n\n  const closeDetailsModal = () => {\n    setIsDetailsModalOpen(false);\n    setSelectedCity('');\n    setIsTicketSticky(false);\n    setScrollY(0);\n    setIsWiggling(false);\n  };\n\n  // Handle scroll in modal to manage sticky ticket button\n  const handleModalScroll = (e: React.UIEvent<HTMLDivElement>) => {\n    const target = e.target as HTMLDivElement;\n    const scrollTop = target.scrollTop;\n    const ticketButton = ticketButtonRef.current;\n    const modalContent = modalContentRef.current;\n\n    if (ticketButton && modalContent) {\n      const buttonRect = ticketButton.getBoundingClientRect();\n      const modalRect = target.getBoundingClientRect();\n      const buttonTop = buttonRect.top - modalRect.top + scrollTop;\n\n      // Check if button is about to scroll out of view (with some offset)\n      const shouldBeSticky = scrollTop > buttonTop - 100;\n      const wasSticky = isTicketSticky;\n\n      // Update sticky state\n      setIsTicketSticky(shouldBeSticky);\n      setScrollY(scrollTop);\n\n      // Add/remove class for content shifting\n      if (shouldBeSticky && !wasSticky) {\n        modalContent.classList.add('has-sticky-ticket');\n      } else if (!shouldBeSticky && wasSticky) {\n        modalContent.classList.remove('has-sticky-ticket');\n        // Trigger wiggle animation when button returns\n        setIsWiggling(true);\n        setTimeout(() => setIsWiggling(false), 500);\n      }\n    }\n  };\n\n  // Reset sticky state when modal opens\n  useEffect(() => {\n    if (isDetailsModalOpen) {\n      setIsTicketSticky(false);\n      setScrollY(0);\n    }\n  }, [isDetailsModalOpen]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate API call - replace with actual implementation\n    try {\n      // Here you would integrate with your email service (Convex, etc.)\n      // await addToNotificationList(email, selectedCity);\n\n      setSubmitMessage(`✅ Success! You'll be notified when ${selectedCity} tickets go on sale.`);\n      setTimeout(() => {\n        closeModal();\n      }, 2000);\n    } catch {\n      setSubmitMessage('❌ Something went wrong. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const comingSoonCities = [\n    'Los Angeles, CA',\n    'Atlanta, GA',\n    'Miami, FL',\n    'Austin, TX',\n    'Nashville, TN'\n  ];\n\n  return (\n    <>\n      <section className=\"cities-grid\">\n        {/* Dallas Event Card */}\n        <article className=\"city-card\">\n          <div className=\"city-card__header\">\n            <h2 className=\"city-card__title\">Dallas, TX</h2>\n            <span className=\"city-card__status\">On Sale</span>\n          </div>\n\n          <div className=\"city-card__details\">\n            <div className=\"city-card__detail\">\n              <strong>Dates:</strong> September 18-19, 2025\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Venue:</strong> Lofty Spaces\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Address:</strong> 816 Montgomery St, Dallas, TX 75215\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Age:</strong> 21+ (ID Required)\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Doors:</strong> 7:00 PM both nights\n            </div>\n          </div>\n\n          <div className=\"city-card__actions\">\n            <a\n              href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"btn btn-primary\"\n            >\n              Get Tickets\n            </a>\n            <button\n              onClick={() => openDetailsModal('Dallas, TX')}\n              className=\"btn btn-secondary\"\n            >\n              DETAILS\n            </button>\n          </div>\n        </article>\n\n        {/* Houston Event Card */}\n        <article className=\"city-card\">\n          <div className=\"city-card__header\">\n            <h2 className=\"city-card__title\">Houston, TX</h2>\n            <span className=\"city-card__status\">On Sale</span>\n          </div>\n\n          <div className=\"city-card__details\">\n            <div className=\"city-card__detail\">\n              <strong>Dates:</strong> October 10-11, 2025\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Venue:</strong> Garage HTX\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Address:</strong> 1201 Oliver St #106, Houston, TX 77007\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Age:</strong> 21+ (ID Required)\n            </div>\n            <div className=\"city-card__detail\">\n              <strong>Time:</strong> 7:00 PM - 1:00 AM both nights\n            </div>\n          </div>\n\n          <div className=\"city-card__actions\">\n            <a\n              href=\"https://www.eventbrite.com/e/chocolate-and-art-show-houston-tickets-1564455190589\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"btn btn-primary\"\n            >\n              Get Tickets\n            </a>\n            <button\n              onClick={() => openDetailsModal('Houston, TX')}\n              className=\"btn btn-secondary\"\n            >\n              DETAILS\n            </button>\n          </div>\n        </article>\n      </section>\n\n      {/* Coming Soon Section */}\n      <section className=\"coming-soon-section\">\n        <h2 className=\"coming-soon-title\">Coming Soon...</h2>\n        <p className=\"coming-soon-subtitle\">\n          We&apos;re expanding to more cities! Get notified when tickets become available in your area.\n        </p>\n\n        <div className=\"coming-soon-list\">\n          {comingSoonCities.map((city) => (\n            <div key={city} className=\"coming-soon-item\">\n              <span className=\"coming-soon-city\">{city}</span>\n              <button\n                onClick={() => openNotificationModal(city)}\n                className=\"btn btn-notify\"\n              >\n                Notify Me\n              </button>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* Notification Modal */}\n      {isModalOpen && (\n        <div className=\"modal-overlay\" onClick={closeModal}>\n          <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n            <button className=\"modal-close\" onClick={closeModal} aria-label=\"Close modal\">\n              ×\n            </button>\n\n            <h3 className=\"modal-title\">Get Notified - {selectedCity}</h3>\n            <p className=\"modal-subtitle\">\n              Be the first to know when tickets go on sale for {selectedCity}.\n              You&apos;ll receive an exclusive early bird discount!\n            </p>\n\n            {submitMessage ? (\n              <div className=\"modal-message\">{submitMessage}</div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"modal-form\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"email\" className=\"form-label\">\n                    Email Address\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    required\n                    className=\"form-input\"\n                    placeholder=\"<EMAIL>\"\n                    disabled={isSubmitting}\n                  />\n                </div>\n\n                <div className=\"form-actions\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-secondary\"\n                    disabled={isSubmitting}\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-primary\"\n                    disabled={isSubmitting || !email}\n                  >\n                    {isSubmitting ? 'Subscribing...' : 'Notify Me'}\n                  </button>\n                </div>\n              </form>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Event Details Modal */}\n      {isDetailsModalOpen && (\n        <div className=\"modal-overlay\" onClick={closeDetailsModal}>\n          <div\n            className=\"modal-content modal-content--large\"\n            onClick={(e) => e.stopPropagation()}\n            onScroll={handleModalScroll}\n            ref={modalContentRef}\n          >\n            <button className=\"modal-close\" onClick={closeDetailsModal} aria-label=\"Close modal\">\n              ×\n            </button>\n\n            {/* Sticky Ticket Icon */}\n            {isTicketSticky && (\n              <div className=\"sticky-ticket-icon\">\n                <a\n                  href={selectedCity === 'Dallas, TX'\n                    ? \"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\"\n                    : \"https://www.eventbrite.com/e/chocolate-and-art-show-houston-tickets-1564455190589\"\n                  }\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"sticky-ticket-link\"\n                  aria-label=\"Get Tickets\"\n                >\n                  <TicketIcon className=\"sticky-ticket-svg\" />\n                </a>\n              </div>\n            )}\n\n            {selectedCity === 'Dallas, TX' && (\n              <>\n                <div className=\"modal-header\">\n                  <h3 className=\"modal-title\">Chocolate & Art Show Dallas</h3>\n                  <div className=\"modal-image\">\n                    <Image\n                      src=\"/images/gallery/dallas/webp/BEST of BEST.webp\"\n                      alt=\"Dallas Chocolate & Art Show\"\n                      width={600}\n                      height={200}\n                      style={{ width: '100%', height: '200px', objectFit: 'cover', borderRadius: '8px' }}\n                    />\n                  </div>\n\n                  {/* Get Tickets Button - positioned high in modal */}\n                  <div\n                    className={`modal-actions modal-actions--top ${isTicketSticky ? 'modal-actions--shifted' : ''}`}\n                    ref={selectedCity === 'Dallas, TX' ? ticketButtonRef : null}\n                  >\n                    <a\n                      href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`btn btn-primary ${isTicketSticky ? 'btn--hidden' : ''} ${isWiggling && selectedCity === 'Dallas, TX' ? 'wiggle' : ''}`}\n                    >\n                      Get Tickets\n                    </a>\n                  </div>\n                </div>\n\n                <div className=\"modal-details\">\n                  <div className=\"detail-section\">\n                    <h4>Event Information</h4>\n                    <div className=\"detail-grid\">\n                      <div className=\"detail-item\">\n                        <strong>Dates:</strong> Thursday & Friday, September 18-19, 2025\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Venue:</strong> Lofty Spaces\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Address:</strong> 816 Montgomery St, Dallas, TX 75215\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Age Requirement:</strong> 21+ (Valid ID Required)\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Doors Open:</strong> 7:00 PM both nights\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Last Entry:</strong> 12:30 AM\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>What to Expect</h4>\n                    <p>An immersive evening where art meets chocolate. Experience live painting, body art performances, interactive installations, and artisan chocolate tastings in an intimate gallery setting.</p>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>Important Notes</h4>\n                    <ul>\n                      <li>This is a 21+ event - Valid government-issued photo ID required</li>\n                      <li>No re-entry once you leave the venue</li>\n                      <li>Limited capacity - advance tickets strongly recommended</li>\n                      <li>Creative attire encouraged</li>\n                    </ul>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>Featured Artists</h4>\n                    <div className=\"detail-grid\">\n                      <div className=\"detail-item\">\n                        <strong>Visual Artists:</strong> Local Dallas painters, sculptors, and mixed media artists\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Body Artists:</strong> Live body painting performances throughout the evening\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Live Music:</strong> Curated DJ sets and live performances\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Interactive Art:</strong> Hands-on installations and collaborative pieces\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>Chocolate Experience</h4>\n                    <p>Indulge in artisan chocolate tastings from local chocolatiers, featuring unique flavors and artistic presentations that complement the visual art experience.</p>\n                    <div className=\"detail-grid\">\n                      <div className=\"detail-item\">\n                        <strong>Tasting Stations:</strong> Multiple chocolate vendors throughout the venue\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Pairings:</strong> Chocolate and wine/cocktail pairings available\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Purchase:</strong> Take home your favorite chocolates\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>Venue Details</h4>\n                    <p>Lofty Spaces is a premier event venue in Dallas, featuring high ceilings, industrial charm, and flexible spaces perfect for immersive art experiences.</p>\n                    <div className=\"detail-grid\">\n                      <div className=\"detail-item\">\n                        <strong>Accessibility:</strong> ADA compliant with elevator access\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Parking:</strong> Limited on-site parking, street parking available\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Public Transit:</strong> Accessible via DART rail and bus lines\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </>\n            )}\n\n            {selectedCity === 'Houston, TX' && (\n              <>\n                <div className=\"modal-header\">\n                  <h3 className=\"modal-title\">Chocolate & Art Show Houston</h3>\n                  <div className=\"modal-image\">\n                    <Image\n                      src=\"/images/gallery/dallas/webp/amazinggg.webp\"\n                      alt=\"Houston Chocolate & Art Show\"\n                      width={600}\n                      height={200}\n                      style={{ width: '100%', height: '200px', objectFit: 'cover', borderRadius: '8px' }}\n                    />\n                  </div>\n\n                  {/* Get Tickets Button - positioned high in modal */}\n                  <div\n                    className={`modal-actions modal-actions--top ${isTicketSticky ? 'modal-actions--shifted' : ''}`}\n                    ref={selectedCity === 'Houston, TX' ? ticketButtonRef : null}\n                  >\n                    <a\n                      href=\"https://www.eventbrite.com/e/chocolate-and-art-show-houston-tickets-1564455190589\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`btn btn-primary ${isTicketSticky ? 'btn--hidden' : ''} ${isWiggling && selectedCity === 'Houston, TX' ? 'wiggle' : ''}`}\n                    >\n                      Get Tickets\n                    </a>\n                  </div>\n                </div>\n\n                <div className=\"modal-details\">\n                  <div className=\"detail-section\">\n                    <h4>Event Information</h4>\n                    <div className=\"detail-grid\">\n                      <div className=\"detail-item\">\n                        <strong>Dates:</strong> Friday & Saturday, October 10-11, 2025\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Venue:</strong> Garage HTX\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Address:</strong> 1201 Oliver St #106, Houston, TX 77007\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Age Requirement:</strong> 21+ (Valid ID Required)\n                      </div>\n                      <div className=\"detail-item\">\n                        <strong>Time:</strong> 7:00 PM - 1:00 AM both nights\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>What to Expect</h4>\n                    <p>Houston&apos;s premier immersive art experience featuring local artists, live performances, interactive installations, and artisan chocolate in the heart of the city&apos;s arts district.</p>\n                  </div>\n\n                  <div className=\"detail-section\">\n                    <h4>Important Notes</h4>\n                    <ul>\n                      <li>This is a 21+ event - Valid government-issued photo ID required</li>\n                      <li>No re-entry once you leave the venue</li>\n                      <li>Limited capacity - advance tickets strongly recommended</li>\n                      <li>Street parking and nearby lots available</li>\n                    </ul>\n                  </div>\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,iNAAQ,EAAC;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IAEnD,6BAA6B;IAC7B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,GAAG,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAChC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,kBAAkB,IAAA,+MAAM,EAAiB;IAC/C,MAAM,kBAAkB,IAAA,+MAAM,EAAiB;IAE/C,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;QAChB,eAAe;QACf,SAAS;QACT,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,sBAAsB;IACxB;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,gBAAgB;QAChB,SAAS;QACT,iBAAiB;IACnB;IAEA,MAAM,oBAAoB;QACxB,sBAAsB;QACtB,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;QACX,cAAc;IAChB;IAEA,wDAAwD;IACxD,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,EAAE,MAAM;QACvB,MAAM,YAAY,OAAO,SAAS;QAClC,MAAM,eAAe,gBAAgB,OAAO;QAC5C,MAAM,eAAe,gBAAgB,OAAO;QAE5C,IAAI,gBAAgB,cAAc;YAChC,MAAM,aAAa,aAAa,qBAAqB;YACrD,MAAM,YAAY,OAAO,qBAAqB;YAC9C,MAAM,YAAY,WAAW,GAAG,GAAG,UAAU,GAAG,GAAG;YAEnD,oEAAoE;YACpE,MAAM,iBAAiB,YAAY,YAAY;YAC/C,MAAM,YAAY;YAElB,sBAAsB;YACtB,kBAAkB;YAClB,WAAW;YAEX,wCAAwC;YACxC,IAAI,kBAAkB,CAAC,WAAW;gBAChC,aAAa,SAAS,CAAC,GAAG,CAAC;YAC7B,OAAO,IAAI,CAAC,kBAAkB,WAAW;gBACvC,aAAa,SAAS,CAAC,MAAM,CAAC;gBAC9B,+CAA+C;gBAC/C,cAAc;gBACd,WAAW,IAAM,cAAc,QAAQ;YACzC;QACF;IACF;IAEA,sCAAsC;IACtC,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB;YACtB,kBAAkB;YAClB,WAAW;QACb;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,yDAAyD;QACzD,IAAI;YACF,kEAAkE;YAClE,oDAAoD;YAEpD,iBAAiB,CAAC,mCAAmC,EAAE,aAAa,oBAAoB,CAAC;YACzF,WAAW;gBACT;YACF,GAAG;QACL,EAAE,OAAM;YACN,iBAAiB;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE;;0BACE,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAGtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAiB;;;;;;;kDAE3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAa;;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAe;;;;;;;;;;;;;0CAI3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAGtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAiB;;;;;;;kDAE3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAa;;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAc;;;;;;;;;;;;;0CAI1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAoB;;;;;;kCAClC,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;gCAAe,WAAU;;kDACxB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;;+BALO;;;;;;;;;;;;;;;;YAcf,6BACC,8OAAC;gBAAI,WAAU;gBAAgB,SAAS;0BACtC,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAC9D,8OAAC;4BAAO,WAAU;4BAAc,SAAS;4BAAY,cAAW;sCAAc;;;;;;sCAI9E,8OAAC;4BAAG,WAAU;;gCAAc;gCAAgB;;;;;;;sCAC5C,8OAAC;4BAAE,WAAU;;gCAAiB;gCACsB;gCAAa;;;;;;;wBAIhE,8BACC,8OAAC;4BAAI,WAAU;sCAAiB;;;;;iDAEhC,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAa;;;;;;sDAG9C,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,UAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,UAAU,gBAAgB,CAAC;sDAE1B,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUhD,oCACC,8OAAC;gBAAI,WAAU;gBAAgB,SAAS;0BACtC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;oBACjC,UAAU;oBACV,KAAK;;sCAEL,8OAAC;4BAAO,WAAU;4BAAc,SAAS;4BAAmB,cAAW;sCAAc;;;;;;wBAKpF,gCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAM,iBAAiB,eACnB,qFACA;gCAEJ,QAAO;gCACP,KAAI;gCACJ,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC,iOAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;wBAK3B,iBAAiB,8BAChB;;8CACE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wIAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,OAAO;oDAAE,OAAO;oDAAQ,QAAQ;oDAAS,WAAW;oDAAS,cAAc;gDAAM;;;;;;;;;;;sDAKrF,8OAAC;4CACC,WAAW,CAAC,iCAAiC,EAAE,iBAAiB,2BAA2B,IAAI;4CAC/F,KAAK,iBAAiB,eAAe,kBAAkB;sDAEvD,cAAA,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAW,CAAC,gBAAgB,EAAE,iBAAiB,gBAAgB,GAAG,CAAC,EAAE,cAAc,iBAAiB,eAAe,WAAW,IAAI;0DACnI;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAe;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAe;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAiB;;;;;;;sEAE3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAyB;;;;;;;sEAEnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAoB;;;;;;;sEAE9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAoB;;;;;;;;;;;;;;;;;;;sDAKlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;;sEACC,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;sDAIR,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAsB;;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAoB;;;;;;;sEAE9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAyB;;;;;;;;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAA0B;;;;;;;sEAEpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAkB;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAkB;;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAuB;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAiB;;;;;;;sEAE3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ3C,iBAAiB,+BAChB;;8CACE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wIAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,OAAO;oDAAE,OAAO;oDAAQ,QAAQ;oDAAS,WAAW;oDAAS,cAAc;gDAAM;;;;;;;;;;;sDAKrF,8OAAC;4CACC,WAAW,CAAC,iCAAiC,EAAE,iBAAiB,2BAA2B,IAAI;4CAC/F,KAAK,iBAAiB,gBAAgB,kBAAkB;sDAExD,cAAA,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAW,CAAC,gBAAgB,EAAE,iBAAiB,gBAAgB,GAAG,CAAC,EAAE,cAAc,iBAAiB,gBAAgB,WAAW,IAAI;0DACpI;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAe;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAe;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAiB;;;;;;;sEAE3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAyB;;;;;;;sEAEnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAO;;;;;;gEAAc;;;;;;;;;;;;;;;;;;;sDAK5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;;sEACC,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B", "debugId": null}}]}