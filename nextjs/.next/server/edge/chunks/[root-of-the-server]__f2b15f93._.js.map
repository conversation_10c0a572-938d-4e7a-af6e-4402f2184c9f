{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\n// Define protected routes that require authentication\nconst isProtectedRoute = createRouteMatcher([\n    '/dashboard(.*)',\n    '/admin(.*)',\n    '/profile(.*)',\n    '/submit-art(.*)',\n    '/artist-submissions(.*)',\n    '/my-submissions(.*)'\n])\n\nexport default clerkMiddleware(async (auth, req) => {\n    // Protect all defined routes using auth.protect() for automatic redirect handling\n    if (isProtectedRoute(req)) {\n        await auth.protect()\n    }\n})\n\nexport const config = {\n    matcher: [\n        // Skip Next.js internals and all static files, unless found in search params\n        '/((?!_next|[^?]*\\\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',\n        // Always run for API routes\n        '/(api|trpc)(.*)',\n    ],\n}"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AAGA,sDAAsD;AACtD,MAAM,mBAAmB,IAAA,wMAAkB,EAAC;IACxC;IACA;IACA;IACA;IACA;IACA;CACH;uCAEc,IAAA,wMAAe,EAAC,OAAO,MAAM;IACxC,kFAAkF;IAClF,IAAI,iBAAiB,MAAM;QACvB,MAAM,KAAK,OAAO;IACtB;AACJ;AAEO,MAAM,SAAS;IAClB,SAAS;QACL,6EAA6E;QAC7E;QACA,4BAA4B;QAC5B;KACH;AACL"}}]}