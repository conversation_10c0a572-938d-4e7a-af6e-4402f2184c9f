// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../app/admin/page.tsx
{
  const handler = {} as typeof import("../../app/admin/page.js")
  handler satisfies AppPageConfig<"/admin">
}

// Validate ../../app/artists/page.tsx
{
  const handler = {} as typeof import("../../app/artists/page.js")
  handler satisfies AppPageConfig<"/artists">
}

// Validate ../../app/contact/page.tsx
{
  const handler = {} as typeof import("../../app/contact/page.js")
  handler satisfies AppPageConfig<"/contact">
}

// Validate ../../app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../app/events/dallas-tx-2025-09-18-19/page.tsx
{
  const handler = {} as typeof import("../../app/events/dallas-tx-2025-09-18-19/page.js")
  handler satisfies AppPageConfig<"/events/dallas-tx-2025-09-18-19">
}

// Validate ../../app/events/page.tsx
{
  const handler = {} as typeof import("../../app/events/page.js")
  handler satisfies AppPageConfig<"/events">
}

// Validate ../../app/faq/page.tsx
{
  const handler = {} as typeof import("../../app/faq/page.js")
  handler satisfies AppPageConfig<"/faq">
}

// Validate ../../app/gallery/page.tsx
{
  const handler = {} as typeof import("../../app/gallery/page.js")
  handler satisfies AppPageConfig<"/gallery">
}

// Validate ../../app/my-submissions/page.tsx
{
  const handler = {} as typeof import("../../app/my-submissions/page.js")
  handler satisfies AppPageConfig<"/my-submissions">
}

// Validate ../../app/page.tsx
{
  const handler = {} as typeof import("../../app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../app/profile/page.tsx
{
  const handler = {} as typeof import("../../app/profile/page.js")
  handler satisfies AppPageConfig<"/profile">
}

// Validate ../../app/shows/page.tsx
{
  const handler = {} as typeof import("../../app/shows/page.js")
  handler satisfies AppPageConfig<"/shows">
}

// Validate ../../app/sign-in/[[...sign-in]]/page.tsx
{
  const handler = {} as typeof import("../../app/sign-in/[[...sign-in]]/page.js")
  handler satisfies AppPageConfig<"/sign-in/[[...sign-in]]">
}

// Validate ../../app/sign-up/[[...sign-up]]/page.tsx
{
  const handler = {} as typeof import("../../app/sign-up/[[...sign-up]]/page.js")
  handler satisfies AppPageConfig<"/sign-up/[[...sign-up]]">
}

// Validate ../../app/submit-art/page.tsx
{
  const handler = {} as typeof import("../../app/submit-art/page.js")
  handler satisfies AppPageConfig<"/submit-art">
}







// Validate ../../app/layout.tsx
{
  const handler = {} as typeof import("../../app/layout.js")
  handler satisfies LayoutConfig<"/">
}
