// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/admin" | "/artists" | "/contact" | "/dashboard" | "/events" | "/events/dallas-tx-2025-09-18-19" | "/faq" | "/gallery" | "/my-submissions" | "/profile" | "/shows" | "/sign-in/[[...sign-in]]" | "/sign-up/[[...sign-up]]" | "/submit-art"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes


interface ParamMap {
  "/": {}
  "/admin": {}
  "/artists": {}
  "/contact": {}
  "/dashboard": {}
  "/events": {}
  "/events/dallas-tx-2025-09-18-19": {}
  "/faq": {}
  "/gallery": {}
  "/my-submissions": {}
  "/profile": {}
  "/shows": {}
  "/sign-in/[[...sign-in]]": { "sign-in"?: string[]; }
  "/sign-up/[[...sign-up]]": { "sign-up"?: string[]; }
  "/submit-art": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }
}
