{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/ui/WelcomeBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useUser } from '@clerk/nextjs';\n\nexport default function WelcomeBanner() {\n  const { user, isSignedIn } = useUser();\n  const [showBanner, setShowBanner] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasShownBanner, setHasShownBanner] = useState(false);\n\n  useEffect(() => {\n    // Only show banner if user just signed in and we haven't shown it yet\n    if (isSignedIn && user?.firstName && !hasShownBanner) {\n      setHasShownBanner(true);\n      setIsVisible(true);\n\n      // Small delay to ensure smooth transition\n      const showTimer = setTimeout(() => {\n        setShowBanner(true);\n      }, 100);\n\n      // Start hiding after 4 seconds\n      const hideTimer = setTimeout(() => {\n        setShowBanner(false);\n      }, 4100);\n\n      // Remove from DOM after fade out completes\n      const removeTimer = setTimeout(() => {\n        setIsVisible(false);\n      }, 4600);\n\n      return () => {\n        clearTimeout(showTimer);\n        clearTimeout(hideTimer);\n        clearTimeout(removeTimer);\n      };\n    }\n  }, [isSignedIn, user?.firstName, hasShownBanner]);\n\n  // Reset banner state when user signs out\n  useEffect(() => {\n    if (!isSignedIn) {\n      setHasShownBanner(false);\n      setShowBanner(false);\n      setIsVisible(false);\n    }\n  }, [isSignedIn]);\n\n  if (!isSignedIn || !user?.firstName || !isVisible) {\n    return null;\n  }\n\n  return (\n    <div\n      className={`welcome-banner ${showBanner ? 'show' : 'hide'}`}\n      role=\"alert\"\n      aria-live=\"polite\"\n    >\n      Welcome, {user.firstName}! 🎨\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAA,0KAAO;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAC;IAErD,IAAA,0KAAS;mCAAC;YACR,sEAAsE;YACtE,IAAI,eAAc,iBAAA,2BAAA,KAAM,SAAS,KAAI,CAAC,gBAAgB;gBACpD,kBAAkB;gBAClB,aAAa;gBAEb,0CAA0C;gBAC1C,MAAM,YAAY;yDAAW;wBAC3B,cAAc;oBAChB;wDAAG;gBAEH,+BAA+B;gBAC/B,MAAM,YAAY;yDAAW;wBAC3B,cAAc;oBAChB;wDAAG;gBAEH,2CAA2C;gBAC3C,MAAM,cAAc;2DAAW;wBAC7B,aAAa;oBACf;0DAAG;gBAEH;+CAAO;wBACL,aAAa;wBACb,aAAa;wBACb,aAAa;oBACf;;YACF;QACF;kCAAG;QAAC;QAAY,iBAAA,2BAAA,KAAM,SAAS;QAAE;KAAe;IAEhD,yCAAyC;IACzC,IAAA,0KAAS;mCAAC;YACR,IAAI,CAAC,YAAY;gBACf,kBAAkB;gBAClB,cAAc;gBACd,aAAa;YACf;QACF;kCAAG;QAAC;KAAW;IAEf,IAAI,CAAC,cAAc,EAAC,iBAAA,2BAAA,KAAM,SAAS,KAAI,CAAC,WAAW;QACjD,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,kBAA8C,OAA7B,aAAa,SAAS;QACnD,MAAK;QACL,aAAU;;YACX;YACW,KAAK,SAAS;YAAC;;;;;;;AAG/B;GAzDwB;;QACO,0KAAO;;;KADd", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useUser, UserButton, SignInButton, SignUpButton } from '@clerk/nextjs';\nimport WelcomeBanner from '@/components/ui/WelcomeBanner';\n\nexport default function Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isClosing, setIsClosing] = useState(false);\n  const [isHydrated, setIsHydrated] = useState(false);\n  const mobileMenuRef = useRef<HTMLDivElement>(null);\n  const menuButtonRef = useRef<HTMLButtonElement>(null);\n  const pathname = usePathname();\n  const router = useRouter();\n  const { isSignedIn } = useUser();\n\n  // Handle hydration\n  useEffect(() => {\n    setIsHydrated(true);\n  }, []);\n\n  const toggleMobileMenu = () => {\n    if (isMobileMenuOpen) {\n      closeMobileMenu();\n    } else {\n      setIsMobileMenuOpen(true);\n      setIsClosing(false);\n    }\n  };\n\n  const closeMobileMenu = () => {\n    setIsClosing(true);\n    setTimeout(() => {\n      setIsMobileMenuOpen(false);\n      setIsClosing(false);\n    }, 400); // Match animation duration\n  };\n\n  const handleMobileNavClick = (href: string, event: React.MouseEvent) => {\n    event.preventDefault();\n\n    // Start closing animation\n    setIsClosing(true);\n\n    // Navigate after animation completes\n    setTimeout(() => {\n      setIsMobileMenuOpen(false);\n      setIsClosing(false);\n      router.push(href);\n    }, 600); // Allow time for staggered exit animation\n  };\n\n  const isActive = (href: string) => {\n    // Prevent hydration mismatch by only checking pathname after hydration\n    if (!isHydrated) return false;\n\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  // Focus management for mobile menu\n  useEffect(() => {\n    if (isMobileMenuOpen && !isClosing) {\n      // Focus the first link in the mobile menu when opened\n      const firstLink = mobileMenuRef.current?.querySelector('a');\n      firstLink?.focus();\n    } else if (!isMobileMenuOpen) {\n      // Return focus to menu button when closed\n      menuButtonRef.current?.focus();\n    }\n  }, [isMobileMenuOpen, isClosing]);\n\n  // Handle escape key to close mobile menu\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        closeMobileMenu();\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when menu is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = '';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = '';\n    };\n  }, [isMobileMenuOpen]);\n\n  return (\n    <>\n      {/* Welcome Banner */}\n      <WelcomeBanner />\n\n      {/* Navigation */}\n      <nav className=\"nav\" role=\"navigation\" aria-label=\"Main navigation\">\n        <div className=\"nav-container\">\n          <Link href=\"/\" className=\"nav-logo\" aria-label=\"Chocolate & Art Show home\">\n            <Image\n              src=\"/images/brand/choco-logo.png\"\n              alt=\"Chocolate & Art Show logo\"\n              width={48}\n              height={48}\n            />\n            <span>Chocolate & Art Show</span>\n          </Link>\n\n          {/* Navigation center section - menu + auth closer together */}\n          <div className=\"nav-center\">\n            <ul className=\"nav-menu\" role=\"menubar\">\n              <li role=\"none\">\n                <Link href=\"/\" role=\"menuitem\" className={isActive('/') ? 'active' : ''}>Home</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/artists\" role=\"menuitem\" className={isActive('/artists') ? 'active' : ''}>Artists</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/shows\" role=\"menuitem\" className={isActive('/shows') ? 'active' : ''}>Shows</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/gallery\" role=\"menuitem\" className={isActive('/gallery') ? 'active' : ''}>Gallery</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/faq\" role=\"menuitem\" className={isActive('/faq') ? 'active' : ''}>FAQ</Link>\n              </li>\n              <li role=\"none\">\n                <Link href=\"/contact\" role=\"menuitem\" className={isActive('/contact') ? 'active' : ''}>Contact</Link>\n              </li>\n            </ul>\n\n            {/* Authentication Section */}\n            <div className=\"auth-section\">\n              {isSignedIn ? (\n                <div className=\"auth-user-section\">\n                  <Link href=\"/dashboard\" className=\"auth-dashboard-link\">\n                    Dashboard\n                  </Link>\n                  <UserButton\n                    appearance={{\n                      elements: {\n                        avatarBox: 'w-8 h-8 border-2 border-pink-500 rounded-full hover:border-pink-400 transition-colors',\n                        userButtonPopoverCard: 'bg-gray-900 border border-pink-500',\n                        userButtonPopoverActionButton: 'text-white hover:bg-pink-500',\n                        userButtonPopoverActionButtonText: 'text-white',\n                        userButtonPopoverFooter: 'hidden'\n                      }\n                    }}\n                  />\n                </div>\n              ) : (\n                <div className=\"auth-buttons\">\n                  <SignInButton mode=\"modal\">\n                    <button className=\"auth-btn auth-btn-signin\">\n                      Sign In\n                    </button>\n                  </SignInButton>\n                  <SignUpButton mode=\"modal\">\n                    <button className=\"auth-btn auth-btn-signup\">\n                      Sign Up\n                    </button>\n                  </SignUpButton>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            ref={menuButtonRef}\n            className={`mobile-menu-btn${isMobileMenuOpen ? ' active' : ''}`}\n            aria-label=\"Toggle mobile menu\"\n            aria-expanded={isMobileMenuOpen}\n            onClick={toggleMobileMenu}\n          >\n            <span></span>\n            <span></span>\n            <span></span>\n          </button>\n        </div>\n      </nav>\n\n      {/* Mobile Menu Overlay */}\n      <div\n        className={`mobile-menu-overlay${isMobileMenuOpen ? ' active' : ''}${isClosing ? ' closing' : ''}`}\n        id=\"mobile-menu-overlay\"\n        role=\"dialog\"\n        aria-modal=\"true\"\n        aria-labelledby=\"mobile-menu-title\"\n        aria-hidden={!isMobileMenuOpen}\n        onClick={(e) => {\n          if (e.target === e.currentTarget) {\n            closeMobileMenu();\n          }\n        }}\n      >\n        <div ref={mobileMenuRef} className={`mobile-menu${isClosing ? ' closing' : ''}`}>\n          <h2 id=\"mobile-menu-title\" className=\"sr-only\">Mobile Navigation Menu</h2>\n          <nav role=\"navigation\" aria-label=\"Mobile navigation\">\n\n            {/* Mobile Authentication Section - Moved to Top */}\n            {isSignedIn ? (\n              <div\n                className=\"mobile-auth-top\"\n                style={{ '--delay': '0' } as React.CSSProperties}\n              >\n                <UserButton\n                  appearance={{\n                    elements: {\n                      avatarBox: 'w-8 h-8 border-2 border-pink-500 rounded-full',\n                      userButtonPopoverCard: 'bg-gray-900 border border-pink-500',\n                      userButtonPopoverActionButton: 'text-white hover:bg-pink-500',\n                      userButtonPopoverActionButtonText: 'text-white'\n                    }\n                  }}\n                />\n                <Link\n                  href=\"/dashboard\"\n                  onClick={(e) => handleMobileNavClick('/dashboard', e)}\n                  className={`mobile-dashboard-link ${isActive('/dashboard') ? 'active' : ''}`}\n                >\n                  Dashboard\n                </Link>\n              </div>\n            ) : (\n              <div\n                className=\"mobile-auth-top mobile-auth-buttons\"\n                style={{ '--delay': '0' } as React.CSSProperties}\n              >\n                <SignInButton mode=\"modal\">\n                  <button className=\"mobile-auth-btn mobile-auth-signin\">\n                    Sign In\n                  </button>\n                </SignInButton>\n                <SignUpButton mode=\"modal\">\n                  <button className=\"mobile-auth-btn mobile-auth-signup\">\n                    Sign Up\n                  </button>\n                </SignUpButton>\n              </div>\n            )}\n\n            {/* Navigation Items */}\n            <Link\n              href=\"/\"\n              onClick={(e) => handleMobileNavClick('/', e)}\n              className={`mobile-nav-item ${isActive('/') ? 'active' : ''}`}\n              style={{ '--delay': '1' } as React.CSSProperties}\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/artists\"\n              onClick={(e) => handleMobileNavClick('/artists', e)}\n              className={`mobile-nav-item ${isActive('/artists') ? 'active' : ''}`}\n              style={{ '--delay': '2' } as React.CSSProperties}\n            >\n              Artists\n            </Link>\n            <Link\n              href=\"/shows\"\n              onClick={(e) => handleMobileNavClick('/shows', e)}\n              className={`mobile-nav-item ${isActive('/shows') ? 'active' : ''}`}\n              style={{ '--delay': '3' } as React.CSSProperties}\n            >\n              Shows\n            </Link>\n            <Link\n              href=\"/gallery\"\n              onClick={(e) => handleMobileNavClick('/gallery', e)}\n              className={`mobile-nav-item ${isActive('/gallery') ? 'active' : ''}`}\n              style={{ '--delay': '4' } as React.CSSProperties}\n            >\n              Gallery\n            </Link>\n            <Link\n              href=\"/faq\"\n              onClick={(e) => handleMobileNavClick('/faq', e)}\n              className={`mobile-nav-item ${isActive('/faq') ? 'active' : ''}`}\n              style={{ '--delay': '5' } as React.CSSProperties}\n            >\n              FAQ\n            </Link>\n            <Link\n              href=\"/contact\"\n              onClick={(e) => handleMobileNavClick('/contact', e)}\n              className={`mobile-nav-item ${isActive('/contact') ? 'active' : ''}`}\n              style={{ '--delay': '6' } as React.CSSProperties}\n            >\n              Contact\n            </Link>\n          </nav>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAC;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,gBAAgB,IAAA,uKAAM,EAAiB;IAC7C,MAAM,gBAAgB,IAAA,uKAAM,EAAoB;IAChD,MAAM,WAAW,IAAA,oJAAW;IAC5B,MAAM,SAAS,IAAA,kJAAS;IACxB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,0KAAO;IAE9B,mBAAmB;IACnB,IAAA,0KAAS;4BAAC;YACR,cAAc;QAChB;2BAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,kBAAkB;YACpB;QACF,OAAO;YACL,oBAAoB;YACpB,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,WAAW;YACT,oBAAoB;YACpB,aAAa;QACf,GAAG,MAAM,2BAA2B;IACtC;IAEA,MAAM,uBAAuB,CAAC,MAAc;QAC1C,MAAM,cAAc;QAEpB,0BAA0B;QAC1B,aAAa;QAEb,qCAAqC;QACrC,WAAW;YACT,oBAAoB;YACpB,aAAa;YACb,OAAO,IAAI,CAAC;QACd,GAAG,MAAM,0CAA0C;IACrD;IAEA,MAAM,WAAW,CAAC;QAChB,uEAAuE;QACvE,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,mCAAmC;IACnC,IAAA,0KAAS;4BAAC;YACR,IAAI,oBAAoB,CAAC,WAAW;oBAEhB;gBADlB,sDAAsD;gBACtD,MAAM,aAAY,yBAAA,cAAc,OAAO,cAArB,6CAAA,uBAAuB,aAAa,CAAC;gBACvD,sBAAA,gCAAA,UAAW,KAAK;YAClB,OAAO,IAAI,CAAC,kBAAkB;oBAC5B,0CAA0C;gBAC1C;iBAAA,yBAAA,cAAc,OAAO,cAArB,6CAAA,uBAAuB,KAAK;YAC9B;QACF;2BAAG;QAAC;QAAkB;KAAU;IAEhC,yCAAyC;IACzC,IAAA,0KAAS;4BAAC;YACR,MAAM;iDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,kBAAkB;wBAC9C;oBACF;gBACF;;YAEA,IAAI,kBAAkB;gBACpB,SAAS,gBAAgB,CAAC,WAAW;gBACrC,wCAAwC;gBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;KAAiB;IAErB,qBACE;;0BAEE,6LAAC,gJAAa;;;;;0BAGd,6LAAC;gBAAI,WAAU;gBAAM,MAAK;gBAAa,cAAW;0BAChD,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0KAAI;4BAAC,MAAK;4BAAI,WAAU;4BAAW,cAAW;;8CAC7C,6LAAC,2IAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;8CAEV,6LAAC;8CAAK;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;oCAAW,MAAK;;sDAC5B,6LAAC;4CAAG,MAAK;sDACP,cAAA,6LAAC,0KAAI;gDAAC,MAAK;gDAAI,MAAK;gDAAW,WAAW,SAAS,OAAO,WAAW;0DAAI;;;;;;;;;;;sDAE3E,6LAAC;4CAAG,MAAK;sDACP,cAAA,6LAAC,0KAAI;gDAAC,MAAK;gDAAW,MAAK;gDAAW,WAAW,SAAS,cAAc,WAAW;0DAAI;;;;;;;;;;;sDAEzF,6LAAC;4CAAG,MAAK;sDACP,cAAA,6LAAC,0KAAI;gDAAC,MAAK;gDAAS,MAAK;gDAAW,WAAW,SAAS,YAAY,WAAW;0DAAI;;;;;;;;;;;sDAErF,6LAAC;4CAAG,MAAK;sDACP,cAAA,6LAAC,0KAAI;gDAAC,MAAK;gDAAW,MAAK;gDAAW,WAAW,SAAS,cAAc,WAAW;0DAAI;;;;;;;;;;;sDAEzF,6LAAC;4CAAG,MAAK;sDACP,cAAA,6LAAC,0KAAI;gDAAC,MAAK;gDAAO,MAAK;gDAAW,WAAW,SAAS,UAAU,WAAW;0DAAI;;;;;;;;;;;sDAEjF,6LAAC;4CAAG,MAAK;sDACP,cAAA,6LAAC,0KAAI;gDAAC,MAAK;gDAAW,MAAK;gDAAW,WAAW,SAAS,cAAc,WAAW;0DAAI;;;;;;;;;;;;;;;;;8CAK3F,6LAAC;oCAAI,WAAU;8CACZ,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,0KAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAsB;;;;;;0DAGxD,6LAAC,wLAAU;gDACT,YAAY;oDACV,UAAU;wDACR,WAAW;wDACX,uBAAuB;wDACvB,+BAA+B;wDAC/B,mCAAmC;wDACnC,yBAAyB;oDAC3B;gDACF;;;;;;;;;;;6DAIJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8LAAY;gDAAC,MAAK;0DACjB,cAAA,6LAAC;oDAAO,WAAU;8DAA2B;;;;;;;;;;;0DAI/C,6LAAC,8LAAY;gDAAC,MAAK;0DACjB,cAAA,6LAAC;oDAAO,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUvD,6LAAC;4BACC,KAAK;4BACL,WAAW,AAAC,kBAAmD,OAAlC,mBAAmB,YAAY;4BAC5D,cAAW;4BACX,iBAAe;4BACf,SAAS;;8CAET,6LAAC;;;;;8CACD,6LAAC;;;;;8CACD,6LAAC;;;;;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBACC,WAAW,AAAC,sBAAyD,OAApC,mBAAmB,YAAY,IAAiC,OAA5B,YAAY,aAAa;gBAC9F,IAAG;gBACH,MAAK;gBACL,cAAW;gBACX,mBAAgB;gBAChB,eAAa,CAAC;gBACd,SAAS,CAAC;oBACR,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;wBAChC;oBACF;gBACF;0BAEA,cAAA,6LAAC;oBAAI,KAAK;oBAAe,WAAW,AAAC,cAAyC,OAA5B,YAAY,aAAa;;sCACzE,6LAAC;4BAAG,IAAG;4BAAoB,WAAU;sCAAU;;;;;;sCAC/C,6LAAC;4BAAI,MAAK;4BAAa,cAAW;;gCAG/B,2BACC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW;oCAAI;;sDAExB,6LAAC,wLAAU;4CACT,YAAY;gDACV,UAAU;oDACR,WAAW;oDACX,uBAAuB;oDACvB,+BAA+B;oDAC/B,mCAAmC;gDACrC;4CACF;;;;;;sDAEF,6LAAC,0KAAI;4CACH,MAAK;4CACL,SAAS,CAAC,IAAM,qBAAqB,cAAc;4CACnD,WAAW,AAAC,yBAA+D,OAAvC,SAAS,gBAAgB,WAAW;sDACzE;;;;;;;;;;;yDAKH,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW;oCAAI;;sDAExB,6LAAC,8LAAY;4CAAC,MAAK;sDACjB,cAAA,6LAAC;gDAAO,WAAU;0DAAqC;;;;;;;;;;;sDAIzD,6LAAC,8LAAY;4CAAC,MAAK;sDACjB,cAAA,6LAAC;gDAAO,WAAU;0DAAqC;;;;;;;;;;;;;;;;;8CAQ7D,6LAAC,0KAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,KAAK;oCAC1C,WAAW,AAAC,mBAAgD,OAA9B,SAAS,OAAO,WAAW;oCACzD,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,YAAY;oCACjD,WAAW,AAAC,mBAAuD,OAArC,SAAS,cAAc,WAAW;oCAChE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,UAAU;oCAC/C,WAAW,AAAC,mBAAqD,OAAnC,SAAS,YAAY,WAAW;oCAC9D,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,YAAY;oCACjD,WAAW,AAAC,mBAAuD,OAArC,SAAS,cAAc,WAAW;oCAChE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,QAAQ;oCAC7C,WAAW,AAAC,mBAAmD,OAAjC,SAAS,UAAU,WAAW;oCAC5D,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,SAAS,CAAC,IAAM,qBAAqB,YAAY;oCACjD,WAAW,AAAC,mBAAuD,OAArC,SAAS,cAAc,WAAW;oCAChE,OAAO;wCAAE,WAAW;oCAAI;8CACzB;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAxSwB;;QAML,oJAAW;QACb,kJAAS;QACD,0KAAO;;;KARR", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/elgatoai/choco-instructions-sept3/nextjs/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useEffect } from 'react';\nimport { SparklesIcon } from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  useEffect(() => {\n    // Easter Egg functionality for social icons\n    let hoverSequence: number[] = [];\n    const correctSequence = [0, 1, 2, 3, 4, 5, 5, 4, 3, 2, 1, 0]; // Left to right, then right to left\n    let isEasterEggActive = false;\n\n    const socialIcons = document.querySelectorAll('.footer-social a');\n\n    const resetSequence = () => {\n      hoverSequence = [];\n    };\n\n    const createSmokeParticles = (element: Element) => {\n      const rect = element.getBoundingClientRect();\n      const centerX = rect.left + rect.width / 2;\n      const centerY = rect.top + rect.height / 2;\n\n      for (let i = 0; i < 8; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'smoke-particle';\n        particle.style.position = 'fixed';\n        particle.style.left = centerX + (Math.random() - 0.5) * 20 + 'px';\n        particle.style.top = centerY + (Math.random() - 0.5) * 20 + 'px';\n        particle.style.zIndex = '9999';\n        document.body.appendChild(particle);\n\n        setTimeout(() => {\n          particle.remove();\n        }, 2000);\n      }\n    };\n\n    const triggerEasterEgg = () => {\n      if (isEasterEggActive) return;\n      isEasterEggActive = true;\n\n      // Phase 1: Glow effect\n      socialIcons.forEach((icon, index) => {\n        setTimeout(() => {\n          icon.classList.add('easter-egg-glow');\n        }, index * 100);\n      });\n\n      // Phase 2: Explode and scatter\n      setTimeout(() => {\n        socialIcons.forEach((icon) => {\n          icon.classList.remove('easter-egg-glow');\n          icon.classList.add('easter-egg-explode');\n\n          setTimeout(() => {\n            createSmokeParticles(icon);\n            icon.classList.remove('easter-egg-explode');\n            icon.classList.add('easter-egg-scatter');\n\n            // Random scatter directions\n            const randomX = (Math.random() - 0.5) * 400;\n            const randomY = (Math.random() - 0.5) * 400;\n            (icon as HTMLElement).style.transform = `translate(${randomX}px, ${randomY}px) scale(0.5) rotate(${Math.random() * 360}deg)`;\n          }, 400);\n        });\n      }, 1000);\n\n      // Phase 3: Return to original positions\n      setTimeout(() => {\n        socialIcons.forEach((icon, index) => {\n          setTimeout(() => {\n            icon.classList.remove('easter-egg-scatter');\n            icon.classList.add('easter-egg-return');\n            (icon as HTMLElement).style.transform = '';\n\n            setTimeout(() => {\n              icon.classList.remove('easter-egg-return');\n              isEasterEggActive = false;\n            }, 1000);\n          }, index * 150);\n        });\n      }, 3000);\n    };\n\n    socialIcons.forEach((icon, index) => {\n      icon.addEventListener('mouseenter', () => {\n        if (isEasterEggActive) return;\n\n        hoverSequence.push(index);\n\n        // Check if sequence matches the correct pattern\n        const isCorrectSoFar = hoverSequence.every((val, i) => val === correctSequence[i]);\n\n        if (!isCorrectSoFar) {\n          resetSequence();\n          hoverSequence.push(index);\n        }\n\n        // Check if complete sequence is achieved\n        if (hoverSequence.length === correctSequence.length &&\n          hoverSequence.every((val, i) => val === correctSequence[i])) {\n          triggerEasterEgg();\n          resetSequence();\n        }\n\n        // Reset if sequence gets too long without matching\n        if (hoverSequence.length > correctSequence.length) {\n          resetSequence();\n        }\n      });\n    });\n\n    // Reset sequence after period of inactivity\n    let resetTimer: NodeJS.Timeout;\n    const resetAfterDelay = () => {\n      clearTimeout(resetTimer);\n      resetTimer = setTimeout(resetSequence, 3000);\n    };\n\n    socialIcons.forEach(icon => {\n      icon.addEventListener('mouseenter', resetAfterDelay);\n    });\n\n    // Initialize confetti functionality\n    const confettiButton = document.getElementById('footer-confetti-trigger');\n    const canvas = document.getElementById('confetti-canvas') as HTMLCanvasElement;\n\n    if (confettiButton && canvas) {\n      confettiButton.addEventListener('click', () => {\n        // Confetti animation will be implemented later\n        console.log('Confetti triggered!');\n      });\n    }\n\n    return () => {\n      clearTimeout(resetTimer);\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Footer */}\n      <footer className=\"site-footer\" id=\"site-footer\" role=\"contentinfo\">\n        <div className=\"footer-content\">\n          {/* Brand Section */}\n          <div className=\"footer-brand\">\n            <Link className=\"brand-ft\" href=\"/\" aria-label=\"Chocolate & Art Show home\">\n              <Image\n                src=\"/images/brand/choco-logo.png\"\n                width={32}\n                height={32}\n                alt=\"Chocolate & Art Show\"\n              />\n              <span>Chocolate & Art Show</span>\n            </Link>\n            <p className=\"footer-tagline\">Where art meets chocolate, and creativity flows.</p>\n          </div>\n\n          {/* Quick Links Section */}\n          <div className=\"footer-section\">\n            <h3>Quick Links</h3>\n            <nav className=\"footer-nav\" aria-label=\"Footer navigation\">\n              <Link href=\"/events/dallas-tx-2025-09-18-19\">Buy Tickets</Link>\n              <Link href=\"/artists\">Meet Artists</Link>\n              <Link href=\"/shows\">Event Schedule</Link>\n              <Link href=\"/#gallery\">Photo Gallery</Link>\n            </nav>\n          </div>\n\n          {/* Get Involved Section */}\n          <div className=\"footer-section\">\n            <h3>Get Involved</h3>\n            <nav className=\"footer-nav\" aria-label=\"Get involved navigation\">\n              <Link\n                href=\"mailto:<EMAIL>?subject=Artist%20Submission%20—%20Dallas/September&body=Links%20+%20portfolio%20+%20dimensions\"\n              >\n                Apply as Artist\n              </Link>\n              <Link\n                href=\"mailto:<EMAIL>?subject=Vendor%20Application%20—%20Dallas/September&body=Line%20sheet%20+%20power%20needs\"\n              >\n                Apply as Vendor\n              </Link>\n              <Link href=\"/contact\">Contact Us</Link>\n            </nav>\n          </div>\n\n          {/* Upcoming Show Section */}\n          <div className=\"footer-section upcoming-show-card\">\n            <h3>Upcoming Show:</h3>\n            <div className=\"show-details\">\n              <div className=\"show-city\">Dallas, TX</div>\n              <ul className=\"show-info\">\n                <li>September 18-19, 2025</li>\n                <li>Lofty Spaces</li>\n                <li>21+ Event</li>\n                <li>Doors: 7:00 PM</li>\n              </ul>\n            </div>\n            {/* <div className=\"show-expand\">\n              <span className=\"expand-icon\">+</span>\n              <span className=\"expand-text\">see more</span>\n            </div> */}\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"footer-bottom\">\n          <div className=\"footer-bottom-content\">\n            <div className=\"footer-legal\">\n              <small>© 2025 Chocolate & Art Show. All rights reserved.</small>\n              <small>An immersive experience celebrating art, music, and artisan chocolate.</small>\n            </div>\n\n            {/* Social Icons */}\n            <nav className=\"footer-social\" aria-label=\"Social links\">\n              <a href=\"https://www.facebook.com/ChocolateAndArtShowDallas\" target=\"_blank\" rel=\"noopener\"\n                aria-label=\"Facebook\">\n                <Image src=\"/images/brand/ico/facebook-white.svg\" alt=\"Facebook\" width={24} height={24} />\n              </a>\n              <a href=\"https://www.instagram.com/chocolateandartshow/\" target=\"_blank\" rel=\"noopener\" aria-label=\"Instagram\">\n                <Image src=\"/images/brand/ico/instagram-white.svg\" alt=\"Instagram\" width={24} height={24} />\n              </a>\n              <a href=\"https://www.youtube.com/@ChocolateAndArtShow\" target=\"_blank\" rel=\"noopener\" aria-label=\"YouTube\">\n                <Image src=\"/images/brand/ico/youtube-white.svg\" alt=\"YouTube\" width={24} height={24} />\n              </a>\n              <a href=\"https://vimeo.com/chocolateandartshow\" target=\"_blank\" rel=\"noopener\" aria-label=\"Vimeo\">\n                <Image src=\"/images/brand/ico/vimeo-white.svg\" alt=\"Vimeo\" width={24} height={24} />\n              </a>\n              <a href=\"https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089\" target=\"_blank\"\n                rel=\"noopener\" aria-label=\"Eventbrite\">\n                <Image src=\"/images/brand/ico/eventbrite-white.svg\" alt=\"Eventbrite\" width={24} height={24} />\n              </a>\n              <a href=\"mailto:<EMAIL>\" aria-label=\"Email\">\n                <Image src=\"/images/brand/ico/mail-white.svg\" alt=\"Email\" width={24} height={24} />\n              </a>\n            </nav>\n\n            {/* Celebrate button */}\n            <div className=\"footer-celebrate\">\n              <button className=\"btn-modern\" id=\"footer-confetti-trigger\" aria-label=\"Celebrate Dallas\">\n                <span className=\"btn-modern__inner\">\n                  <SparklesIcon className=\"celebrate-icon\" />\n                  Celebrate\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </footer>\n\n      <canvas id=\"confetti-canvas\" className=\"confetti-canvas\" aria-hidden=\"true\"></canvas>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,IAAA,0KAAS;4BAAC;YACR,4CAA4C;YAC5C,IAAI,gBAA0B,EAAE;YAChC,MAAM,kBAAkB;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,EAAE,oCAAoC;YAClG,IAAI,oBAAoB;YAExB,MAAM,cAAc,SAAS,gBAAgB,CAAC;YAE9C,MAAM;kDAAgB;oBACpB,gBAAgB,EAAE;gBACpB;;YAEA,MAAM;yDAAuB,CAAC;oBAC5B,MAAM,OAAO,QAAQ,qBAAqB;oBAC1C,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;oBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;oBAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBAC1B,MAAM,WAAW,SAAS,aAAa,CAAC;wBACxC,SAAS,SAAS,GAAG;wBACrB,SAAS,KAAK,CAAC,QAAQ,GAAG;wBAC1B,SAAS,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK;wBAC7D,SAAS,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK;wBAC5D,SAAS,KAAK,CAAC,MAAM,GAAG;wBACxB,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B;qEAAW;gCACT,SAAS,MAAM;4BACjB;oEAAG;oBACL;gBACF;;YAEA,MAAM;qDAAmB;oBACvB,IAAI,mBAAmB;oBACvB,oBAAoB;oBAEpB,uBAAuB;oBACvB,YAAY,OAAO;6DAAC,CAAC,MAAM;4BACzB;qEAAW;oCACT,KAAK,SAAS,CAAC,GAAG,CAAC;gCACrB;oEAAG,QAAQ;wBACb;;oBAEA,+BAA+B;oBAC/B;6DAAW;4BACT,YAAY,OAAO;qEAAC,CAAC;oCACnB,KAAK,SAAS,CAAC,MAAM,CAAC;oCACtB,KAAK,SAAS,CAAC,GAAG,CAAC;oCAEnB;6EAAW;4CACT,qBAAqB;4CACrB,KAAK,SAAS,CAAC,MAAM,CAAC;4CACtB,KAAK,SAAS,CAAC,GAAG,CAAC;4CAEnB,4BAA4B;4CAC5B,MAAM,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4CACxC,MAAM,UAAU,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4CACvC,KAAqB,KAAK,CAAC,SAAS,GAAG,AAAC,aAA0B,OAAd,SAAQ,QAAsC,OAAhC,SAAQ,0BAA4C,OAApB,KAAK,MAAM,KAAK,KAAI;wCACzH;4EAAG;gCACL;;wBACF;4DAAG;oBAEH,wCAAwC;oBACxC;6DAAW;4BACT,YAAY,OAAO;qEAAC,CAAC,MAAM;oCACzB;6EAAW;4CACT,KAAK,SAAS,CAAC,MAAM,CAAC;4CACtB,KAAK,SAAS,CAAC,GAAG,CAAC;4CAClB,KAAqB,KAAK,CAAC,SAAS,GAAG;4CAExC;qFAAW;oDACT,KAAK,SAAS,CAAC,MAAM,CAAC;oDACtB,oBAAoB;gDACtB;oFAAG;wCACL;4EAAG,QAAQ;gCACb;;wBACF;4DAAG;gBACL;;YAEA,YAAY,OAAO;oCAAC,CAAC,MAAM;oBACzB,KAAK,gBAAgB,CAAC;4CAAc;4BAClC,IAAI,mBAAmB;4BAEvB,cAAc,IAAI,CAAC;4BAEnB,gDAAgD;4BAChD,MAAM,iBAAiB,cAAc,KAAK;mEAAC,CAAC,KAAK,IAAM,QAAQ,eAAe,CAAC,EAAE;;4BAEjF,IAAI,CAAC,gBAAgB;gCACnB;gCACA,cAAc,IAAI,CAAC;4BACrB;4BAEA,yCAAyC;4BACzC,IAAI,cAAc,MAAM,KAAK,gBAAgB,MAAM,IACjD,cAAc,KAAK;oDAAC,CAAC,KAAK,IAAM,QAAQ,eAAe,CAAC,EAAE;oDAAG;gCAC7D;gCACA;4BACF;4BAEA,mDAAmD;4BACnD,IAAI,cAAc,MAAM,GAAG,gBAAgB,MAAM,EAAE;gCACjD;4BACF;wBACF;;gBACF;;YAEA,4CAA4C;YAC5C,IAAI;YACJ,MAAM;oDAAkB;oBACtB,aAAa;oBACb,aAAa,WAAW,eAAe;gBACzC;;YAEA,YAAY,OAAO;oCAAC,CAAA;oBAClB,KAAK,gBAAgB,CAAC,cAAc;gBACtC;;YAEA,oCAAoC;YACpC,MAAM,iBAAiB,SAAS,cAAc,CAAC;YAC/C,MAAM,SAAS,SAAS,cAAc,CAAC;YAEvC,IAAI,kBAAkB,QAAQ;gBAC5B,eAAe,gBAAgB,CAAC;wCAAS;wBACvC,+CAA+C;wBAC/C,QAAQ,GAAG,CAAC;oBACd;;YACF;YAEA;oCAAO;oBACL,aAAa;gBACf;;QACF;2BAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC;gBAAO,WAAU;gBAAc,IAAG;gBAAc,MAAK;;kCACpD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCAAC,WAAU;wCAAW,MAAK;wCAAI,cAAW;;0DAC7C,6LAAC,2IAAK;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,KAAI;;;;;;0DAEN,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAI,WAAU;wCAAa,cAAW;;0DACrC,6LAAC,0KAAI;gDAAC,MAAK;0DAAkC;;;;;;0DAC7C,6LAAC,0KAAI;gDAAC,MAAK;0DAAW;;;;;;0DACtB,6LAAC,0KAAI;gDAAC,MAAK;0DAAS;;;;;;0DACpB,6LAAC,0KAAI;gDAAC,MAAK;0DAAY;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAI,WAAU;wCAAa,cAAW;;0DACrC,6LAAC,0KAAI;gDACH,MAAK;0DACN;;;;;;0DAGD,6LAAC,0KAAI;gDACH,MAAK;0DACN;;;;;;0DAGD,6LAAC,0KAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM;;;;;;sDACP,6LAAC;sDAAM;;;;;;;;;;;;8CAIT,6LAAC;oCAAI,WAAU;oCAAgB,cAAW;;sDACxC,6LAAC;4CAAE,MAAK;4CAAqD,QAAO;4CAAS,KAAI;4CAC/E,cAAW;sDACX,cAAA,6LAAC,2IAAK;gDAAC,KAAI;gDAAuC,KAAI;gDAAW,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEtF,6LAAC;4CAAE,MAAK;4CAAiD,QAAO;4CAAS,KAAI;4CAAW,cAAW;sDACjG,cAAA,6LAAC,2IAAK;gDAAC,KAAI;gDAAwC,KAAI;gDAAY,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAExF,6LAAC;4CAAE,MAAK;4CAA+C,QAAO;4CAAS,KAAI;4CAAW,cAAW;sDAC/F,cAAA,6LAAC,2IAAK;gDAAC,KAAI;gDAAsC,KAAI;gDAAU,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEpF,6LAAC;4CAAE,MAAK;4CAAwC,QAAO;4CAAS,KAAI;4CAAW,cAAW;sDACxF,cAAA,6LAAC,2IAAK;gDAAC,KAAI;gDAAoC,KAAI;gDAAQ,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEhF,6LAAC;4CAAE,MAAK;4CAAmF,QAAO;4CAChG,KAAI;4CAAW,cAAW;sDAC1B,cAAA,6LAAC,2IAAK;gDAAC,KAAI;gDAAyC,KAAI;gDAAa,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAE1F,6LAAC;4CAAE,MAAK;4CAAsC,cAAW;sDACvD,cAAA,6LAAC,2IAAK;gDAAC,KAAI;gDAAmC,KAAI;gDAAQ,OAAO;gDAAI,QAAQ;;;;;;;;;;;;;;;;;8CAKjF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;wCAAa,IAAG;wCAA0B,cAAW;kDACrE,cAAA,6LAAC;4CAAK,WAAU;;8DACd,6LAAC,0OAAY;oDAAC,WAAU;;;;;;gDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvD,6LAAC;gBAAO,IAAG;gBAAkB,WAAU;gBAAkB,eAAY;;;;;;;;AAG3E;GA1PwB;KAAA", "debugId": null}}]}