import { currentUser } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'

export default async function DashboardPage() {
  const user = await currentUser()

  if (!user) {
    redirect('/sign-in')
  }

  return (
    <main id="main-content" className="min-h-screen">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <section className="hero-section">
            <div className="hero-content">
              <h1 className="hero-title">My Dashboard</h1>
              <p className="hero-subtitle">
                Welcome back, {user.firstName || user.emailAddresses[0].emailAddress}
              </p>
            </div>
          </section>

          {/* User Stats */}
          <section className="cities-grid">
            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">My Submissions</h2>
                <span className="city-card__status">3</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Artist applications you&apos;ve submitted
                </div>
              </div>
              <div className="city-card__actions">
                <Link href="/my-submissions" className="btn btn-primary">
                  View Submissions
                </Link>
              </div>
            </article>

            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">Profile</h2>
                <span className="city-card__status bg-success">Complete</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Manage your account and preferences
                </div>
              </div>
              <div className="city-card__actions">
                <Link href="/profile" className="btn btn-primary">
                  Edit Profile
                </Link>
              </div>
            </article>

            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">Upcoming Shows</h2>
                <span className="city-card__status bg-info">2</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Shows you&apos;re registered for or interested in
                </div>
              </div>
              <div className="city-card__actions">
                <Link href="/shows" className="btn btn-primary">
                  View Shows
                </Link>
              </div>
            </article>
          </section>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* Submit Art */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Submit Art</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Apply to showcase your artwork at upcoming shows
              </p>
              <Link href="/submit-art" className="btn btn-primary w-full">
                Submit Application
              </Link>
            </div>

            {/* Browse Gallery */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Gallery</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Explore artwork from past and upcoming shows
              </p>
              <Link href="/gallery" className="btn btn-primary w-full">
                Browse Gallery
              </Link>
            </div>

            {/* Contact */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Get Help</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Have questions? Contact our team for support
              </p>
              <Link href="/contact" className="btn btn-primary w-full">
                Contact Us
              </Link>
            </div>
          </div>

          {/* Recent Activity */}
          <section className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-bold text-white mb-6">Recent Activity</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                <div>
                  <h3 className="text-white font-semibold">Application Submitted</h3>
                  <p className="text-gray-400 text-sm">Dallas 2025 - Visual Art Application</p>
                </div>
                <span className="text-sm text-gray-500">2 days ago</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                <div>
                  <h3 className="text-white font-semibold">Profile Updated</h3>
                  <p className="text-gray-400 text-sm">Added portfolio images</p>
                </div>
                <span className="text-sm text-gray-500">1 week ago</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                <div>
                  <h3 className="text-white font-semibold">Account Created</h3>
                  <p className="text-gray-400 text-sm">Welcome to Chocolate & Art Show!</p>
                </div>
                <span className="text-sm text-gray-500">2 weeks ago</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
  )
}