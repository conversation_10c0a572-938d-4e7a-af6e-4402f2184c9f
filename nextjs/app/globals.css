/**
 * Global Styles for Chocolate & Art Show
 * Tailwind CSS + Custom Design System
 */

@import "tailwindcss";

/* Tailwind CSS v4 Theme Configuration */
@theme {
    /* Colors */
    --color-pink: #ff2ebd;
    --color-pink-light: #ff7ad6;
    --color-black: #000000;
    --color-white: #ffffff;

    /* Background Colors */
    --color-bg: #000000;
    --color-bg-secondary: #0a0a0a;
    --color-bg-tertiary: #1a1a1a;

    /* Card Colors */
    --color-card-bg: #1a1a1a;
    --color-card-bg-hover: #2a2a2a;
    --color-card-border: #333333;
    --color-card-border-hover: #ff2ebd;

    /* Text Colors */
    --color-fg: #eef2ff;
    --color-fg-secondary: #a7adb7;
    --color-fg-muted: #8ea3b3;

    /* Semantic Colors */
    --color-success: #4ade80;
    --color-warning: #fbbf24;
    --color-error: #ef4444;
    --color-info: #3b82f6;

    /* Font Families */
    --font-family-monoton: var(--font-monoton), cursive;

    /* Shadows */
    --shadow-neon: 0 0 5px #ff2ebd, 0 0 10px #ff2ebd, 0 0 15px #ff2ebd;
    --shadow-glow: 0 0 20px rgba(255, 46, 189, 0.3);
    --shadow-pink-glow: 0 0 20px rgba(255, 46, 189, 0.2);
}

/* Core CSS Variables - Preserved for compatibility */
:root {
    --pink: #ff2ebd;
    --pink-2: #ff7ad6;
    --white: #ffffff;
    --bg: #000;
    --card-bg: #1a1a1a;
    --card-bd: #333;
    --fg: #eef2ff;
    --name: #fff;
}

@layer base {
    * {
        box-sizing: border-box;
    }

    html,
    body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    body {
        @apply bg-black text-white font-sans;
    }
}

/* Component Classes using Tailwind v4 */
.container {
    @apply mx-auto px-4;
    max-width: 1200px;
}

/* Hero Section */
.hero-section {
    @apply py-16 md:py-24 text-center;
}

.hero-content {
    @apply max-w-4xl mx-auto;
}

.hero-title {
    @apply text-4xl md:text-6xl lg:text-7xl text-pink mb-6 leading-tight;
    font-family: var(--font-family-monoton);
    text-shadow: 0 0 20px rgba(255, 46, 189, 0.5);
}

.hero-subtitle {
    @apply text-lg md:text-xl text-fg-secondary mb-8 leading-relaxed;
}

/* Navigation */
.nav {
    @apply fixed top-0 left-0 right-0 z-[1030] h-[70px] bg-black/70 backdrop-blur-[20px] border-b border-white/10;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.nav-container {
    @apply container flex items-center justify-between h-full;
}

/* City Cards (Dashboard Cards) */
.cities-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-12;
}

.city-card {
    @apply bg-card-bg border-2 border-card-border rounded-lg p-6 transition-all duration-300 hover:border-pink;
    box-shadow: 0 0 20px rgba(255, 46, 189, 0.2);
}

.city-card:hover {
    box-shadow: var(--shadow-pink-glow);
}

.city-card__header {
    @apply flex items-center justify-between mb-4;
}

.city-card__title {
    @apply text-xl font-bold text-white;
}

.city-card__status {
    @apply px-3 py-1 bg-pink text-white text-sm font-medium rounded-full;
}

.city-card__details {
    @apply space-y-3 mb-6;
}

.city-card__detail {
    @apply text-fg-secondary leading-relaxed;
}

.city-card__actions {
    @apply flex gap-3 flex-wrap;
}

/* Buttons */
.btn {
    @apply inline-flex items-center justify-center px-6 py-3 font-medium rounded-lg transition-all duration-300 cursor-pointer border;
}

.btn-primary {
    @apply bg-pink text-white border-pink hover:bg-pink/90;
}

.btn-primary:hover {
    box-shadow: var(--shadow-glow);
}

.btn-secondary {
    @apply bg-transparent text-pink border-pink hover:bg-pink hover:text-white;
}

/* User Info Section */
.user-info-section {
    @apply mt-12;
}

/* Authentication Styles */
.auth-section {
    @apply flex items-center gap-4;
}

.auth-btn {
    @apply px-4 py-2 rounded-md font-medium text-sm transition-all duration-300 cursor-pointer border;
}

.auth-btn-signup {
    @apply bg-pink text-white border-pink hover:bg-pink/90;
}

.auth-btn-signin {
    @apply bg-transparent text-pink border-pink hover:bg-pink hover:text-white;
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: 70px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
    max-width: 1400px;
    margin: 0 auto;
    height: 100%;
}

/* Navigation center section - menu + auth closer together */
.nav-center {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--white);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: -0.02em;
}

.nav-logo img {
    width: 48px;
    height: 48px;
    border-radius: 4px;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2.5rem;
    align-items: center;
}

.nav-menu a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    letter-spacing: -0.01em;
    padding: 0.5rem 0;
    transition: all 0.2s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--white);
    box-shadow: 0 4px 15px rgba(255, 46, 189, 0.4);
}

.nav-menu a:hover::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--pink);
    border-radius: 1px;
    box-shadow: 0 0 8px rgba(255, 46, 189, 0.6);
}

.nav-menu a.active {
    color: var(--pink);
}

.nav-menu a.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--pink);
    border-radius: 1px;
    box-shadow: 0 0 8px rgba(255, 46, 189, 0.8);
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.mobile-menu-btn span {
    display: block;
    width: 24px;
    height: 2px;
    background: var(--white);
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 1px;
}

.mobile-menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay.active {
    display: flex;
    opacity: 1;
}

.mobile-menu-overlay.closing {
    opacity: 0;
}

.mobile-menu {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    height: 100%;
    padding: 2rem;
    padding-right: 3rem;
}

.mobile-menu nav {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0;
}

.mobile-nav-item {
    color: var(--white);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0.5rem 0;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: right;
    min-width: 160px;
    position: relative;
    transform: translateX(100px);
    opacity: 0;
    animation: slideInFromRight 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-delay: calc(var(--delay) * 0.1s);
}

.mobile-menu.closing .mobile-nav-item {
    animation: slideOutToRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    animation-delay: calc(var(--delay) * 0.05s);
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }

    60% {
        transform: translateX(-10px);
        opacity: 1;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }

    100% {
        transform: translateX(120px);
        opacity: 0;
    }
}

/* Authentication Styles */
.auth-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.auth-user-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.auth-welcome {
    color: var(--white);
    font-size: 0.9rem;
    font-weight: 500;
}

.auth-dashboard-link {
    color: var(--pink);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--pink);
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Welcome Banner */
.welcome-banner {
    position: fixed;
    top: 70px;
    /* Position below the nav menu */
    left: 0;
    right: 0;
    background: linear-gradient(135deg, var(--pink), var(--pink-2));
    color: var(--white);
    text-align: center;
    padding: 1rem;
    font-weight: 600;
    font-size: 1rem;
    z-index: 999;
    transform: translateY(-100%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(255, 46, 189, 0.3);
    pointer-events: none;
    opacity: 0;
}

.welcome-banner.show {
    transform: translateY(0);
    opacity: 1;
}

.welcome-banner.hide {
    transform: translateY(-100%);
    opacity: 0;
}

/* Mobile welcome banner positioning */
@media (max-width: 768px) {
    .welcome-banner {
        top: 60px;
        /* Position below mobile nav menu */
    }
}

.auth-dashboard-link:hover {
    background: var(--pink);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(255, 46, 189, 0.4);
}

.auth-buttons {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.auth-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid;
}

.auth-btn-signin {
    background: transparent;
    color: var(--white);
    border-color: rgba(255, 255, 255, 0.3);
}

.auth-btn-signin:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--white);
}

.auth-btn-signup {
    background: var(--pink);
    color: var(--white);
    border-color: var(--pink);
}

.auth-btn-signup:hover {
    background: var(--pink-2);
    border-color: var(--pink-2);
    box-shadow: 0 4px 15px rgba(255, 46, 189, 0.4);
}

/* Mobile Authentication Styles */
.mobile-auth-user {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem !important;
}

.mobile-auth-welcome {
    color: var(--white);
    font-weight: 500;
}

.mobile-auth-buttons {
    display: flex !important;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem 2rem !important;
}

.mobile-auth-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid;
    width: 100%;
}

.mobile-auth-signin {
    background: transparent;
    color: var(--white);
    border-color: rgba(255, 255, 255, 0.3);
}

.mobile-auth-signin:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--white);
}

.mobile-auth-signup {
    background: var(--pink);
    color: var(--white);
    border-color: var(--pink);
}

.mobile-auth-signup:hover {
    background: var(--pink-2);
    border-color: var(--pink-2);
    box-shadow: 0 4px 15px rgba(255, 46, 189, 0.4);
}

/* Mobile Authentication Top Section */
.mobile-auth-top {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
    padding: 0.8rem 2rem;
    margin: 0.8rem 0;
    margin-right: 0;
    border-bottom: 1px solid rgba(255, 46, 189, 0.2);
    transform: translateX(100px);
    opacity: 0;
    animation: slideInFromRight 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-delay: calc(var(--delay) * 0.1s);
    text-align: right;
    width: 100%;
    box-sizing: border-box;
}

.mobile-dashboard-link {
    color: var(--pink);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--pink);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mobile-dashboard-link:hover,
.mobile-dashboard-link.active {
    background: var(--pink);
    color: var(--white);
}

.mobile-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--pink);
    transform: scale(1.05) translateX(-5px);
    box-shadow: 0 4px 20px rgba(255, 46, 189, 0.3);
}

.mobile-nav-item:focus {
    outline: 2px solid var(--pink);
    outline-offset: 2px;
}

.mobile-nav-item.active {
    background: rgba(255, 46, 189, 0.2);
    color: var(--pink);
    border: 1px solid rgba(255, 46, 189, 0.5);
}

.mobile-nav-item:active {
    transform: scale(0.98);
}

/* Navigation Responsive */
/* Medium screens - improve navigation spacing and sizing */
@media (max-width: 1024px) and (min-width: 769px) {
    .nav-menu {
        gap: 1rem;
    }

    .nav-menu a {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
    }

    .auth-welcome {
        font-size: 0.8rem;
    }

    .auth-dashboard-link {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 768px) {
    .nav {
        height: 60px;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-logo {
        font-size: 1rem;
        gap: 0.5rem;
    }

    .nav-logo img {
        width: 40px;
        height: 40px;
    }

    .nav-menu {
        display: none;
    }

    .auth-section {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background: var(--bg);
    padding-top: 70px;
}

.overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
    z-index: 2;
}

.hero-content {
    position: relative;
    text-align: center;
    color: var(--white);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    padding: 1rem;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    border: 0;
    padding: 0;
    clip: rect(0 0 0 0);
    overflow: hidden;
}

/* Skip link that becomes visible when focused */
.sr-only-focusable:focus {
    position: absolute;
    top: 0;
    left: 0;
    width: auto;
    height: auto;
    margin: 0;
    padding: 0.5rem 1rem;
    clip: auto;
    overflow: visible;
    background: var(--pink);
    color: var(--white);
    font-weight: 700;
    text-decoration: none;
    z-index: 10000;
    border-radius: 0 0 4px 0;
}

/* Neon Sign */
.neon-sign {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(0.4rem, 2.2vw, 1.25rem);
    text-align: center;
    margin-bottom: 2rem;
}

.neon-line {
    --stroke: rgba(255, 255, 255, 0.85);
    font-family: 'Monoton', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-weight: 400;
    line-height: 0.88;
    text-transform: uppercase;
    white-space: nowrap;
    color: var(--white);
    text-rendering: optimizeLegibility;
    font-kerning: normal;
    text-shadow:
        0 0 .02em var(--white),
        0 0 .08em var(--white),
        0 0 .18em var(--pink),
        0 0 .36em var(--pink),
        0 0 .72em var(--pink),
        0 0 1.2em var(--pink-2);
    -webkit-text-stroke: .01em var(--stroke);
    position: relative;
    isolation: isolate;
    z-index: 1002;
    animation: flickerLoop 3.6s linear infinite, glow 6s ease-in-out infinite;
}

.line-1 {
    font-size: clamp(2.8rem, 10.6vw, 10rem);
    letter-spacing: clamp(-.05em, -0.40vw, -.10em);
}

.line-2 {
    font-size: clamp(2.6rem, 9.8vw, 9rem);
    letter-spacing: clamp(-.05em, -0.34vw, -.10em);
    word-spacing: clamp(0.02em, 0.2vw, 0.12em);
}

.line-3 {
    font-size: clamp(2.4rem, 8.8vw, 8rem);
    letter-spacing: clamp(-.08em, -0.50vw, -.14em);
}

.neon-line::before,
.neon-line::after {
    content: attr(data-text);
    position: absolute;
    inset: 0;
    z-index: 1001;
    color: var(--pink);
    filter: blur(.12em);
    opacity: .55;
}

.neon-line::after {
    filter: blur(.45em);
    opacity: .35;
}

/* Neon animations */
@keyframes flickerLoop {

    0%,
    100% {
        opacity: 1;
    }

    2% {
        opacity: 0.8;
    }

    4% {
        opacity: 1;
    }

    8% {
        opacity: 0.9;
    }

    10% {
        opacity: 1;
    }

    12% {
        opacity: 0.7;
    }

    14% {
        opacity: 1;
    }

    16% {
        opacity: 0.95;
    }

    18% {
        opacity: 1;
    }

    22% {
        opacity: 0.85;
    }

    24% {
        opacity: 1;
    }
}

@keyframes glow {

    0%,
    100% {
        filter: brightness(1);
    }

    50% {
        filter: brightness(1.1);
    }
}

/* Hero CTA */
.hero-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    text-align: center;
}

.cta-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 800;
    margin: 0;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.ticket-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.btn-textured {
    display: inline-block;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--pink), var(--pink-2));
    color: var(--white);
    text-decoration: none;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 2px solid transparent;
    border-radius: 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-textured:hover {
    background: linear-gradient(135deg, var(--pink-2), var(--pink));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
}

.btn-textured:active {
    transform: translateY(0);
}

/* Secondary CTA styling */
.cta-secondary {
    margin-top: 1rem;
    font-size: 1.1rem;
    color: var(--fg);
}

.cta-secondary a {
    color: var(--pink-2);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
}

.cta-secondary a:hover {
    border-bottom-color: var(--pink-2);
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .ticket-buttons {
        flex-direction: row;
        gap: 2rem;
    }
}

/* Container and Layout - removed duplicate, using the one above */

.main-content {
    padding-top: 70px;
    min-height: 100vh;
}

/* Page Headers */
.page-header {
    padding: 4rem 0 2rem;
    text-align: center;
}

.page-header h1 {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 800;
    margin-bottom: 1rem;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.page-header p {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: var(--fg);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Card Components */
.bwhc-card,
.card {
    background: linear-gradient(135deg, var(--card-bg), #2a2a2a);
    border: 2px solid var(--card-bd);
    border-radius: 0;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bwhc-card:hover,
.card:hover {
    border-color: var(--pink);
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(255, 46, 189, 0.2);
}

.bwhc-card h3,
.card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.bwhc-card p,
.card p {
    color: var(--fg);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.bwhc-card ul,
.card ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.bwhc-card li,
.card li {
    color: var(--fg);
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.bwhc-card li::before,
.card li::before {
    content: '•';
    color: var(--pink);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Application Grid */
.application-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin: 3rem 0;
}

@media (min-width: 768px) {
    .application-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Section Styling */
.artists-section {
    padding: 3rem 0;
}

.artists-section h2 {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 800;
    margin-bottom: 2rem;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.artists-section p {
    font-size: 1.1rem;
    color: var(--fg);
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Button Styling */
.btn,
.btn-primary {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--pink), var(--pink-2));
    color: var(--white);
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn:hover,
.btn-primary:hover {
    background: linear-gradient(135deg, var(--pink-2), var(--pink));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 46, 189, 0.3);
    color: var(--white);
    text-decoration: none;
}

/* Intermission Banner */
.intermission {
    background: linear-gradient(135deg, var(--pink), var(--pink-2));
    padding: 2rem 0;
    text-align: center;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.intermission::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.intermission h2 {
    font-size: clamp(2rem, 8vw, 4rem);
    font-weight: 900;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.2em;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* RGB Wall Styling */
.rgb-wall {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    padding: 3rem 0;
    text-align: center;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.rgb-wall::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.rgb-hero {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    font-family: 'Monoton', cursive;
    font-size: clamp(1.5rem, 6vw, 3rem);
    font-weight: 900;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.rgb-word {
    position: relative;
    display: inline-block;
    animation: glow 2s ease-in-out infinite alternate;
}

.rgb-plus,
.rgb-eq {
    font-size: clamp(1.2rem, 5vw, 2.5rem);
    color: var(--white);
    font-weight: 700;
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
    }

    to {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.5);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* CTA Bar - "Join the Show" Section */
.cta-bar {
    background: var(--bg);
    padding: 2rem 0;
    margin: 2rem 0;
}

.cta-bar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.cta-bar-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 800;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 1.5rem;
}

.cta-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .cta-buttons {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

.cta-btn {
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    padding: 1.5rem 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
    color: inherit;
    font-family: inherit;
}

.cta-btn:hover {
    border-color: var(--pink);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
}

.cta-btn__content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cta-btn__title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.cta-btn__subtitle {
    font-size: 0.9rem;
    color: var(--fg);
    margin: 0;
    line-height: 1.4;
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 1rem 0;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border: 2px solid var(--card-bd);
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-item:hover {
    border-color: var(--pink);
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Gallery Lightbox */
.gallery-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.gallery-lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-lightbox-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border: 2px solid var(--pink);
}

.gallery-close,
.gallery-prev,
.gallery-next {
    position: absolute;
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    color: var(--white);
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1001;
}

.gallery-close {
    top: -60px;
    right: 0;
}

.gallery-prev {
    left: -60px;
    top: 50%;
    transform: translateY(-50%);
}

.gallery-next {
    right: -60px;
    top: 50%;
    transform: translateY(-50%);
}

.gallery-close:hover,
.gallery-prev:hover,
.gallery-next:hover {
    border-color: var(--pink);
    background: var(--pink);
    transform: translateY(-50%) scale(1.1);
}

.gallery-close:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .gallery-close {
        top: -50px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }

    .gallery-prev,
    .gallery-next {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }

    .gallery-prev {
        left: 10px;
    }

    .gallery-next {
        right: 10px;
    }
}

/* Footer */
.site-footer {
    background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
    /* border-top: 2px solid var(--card-bd); */
    padding: 2rem 0 1rem;
    margin-top: 2rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: start;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.brand-ft {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--white);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
}

.brand-ft:hover {
    color: var(--pink-2);
}

.footer-tagline {
    color: var(--fg);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

.footer-section h3 {
    color: var(--white);
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.footer-section p {
    color: var(--fg);
    line-height: 1.5;
    margin-bottom: 0.4rem;
    font-size: 0.85rem;
}

.footer-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-nav a {
    color: var(--fg);
    text-decoration: none;
    font-size: 0.85rem;
    line-height: 1.5;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: var(--pink-2);
}

.footer-bottom {
    padding-top: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 2rem;
    padding-right: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-legal {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.footer-legal small {
    color: var(--fg);
    font-size: 0.8rem;
    line-height: 1.4;
}

.footer-social {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--card-bg);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    border-radius: 4px;
}

.footer-social a:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(255, 46, 189, 0.4);
    animation: socialHover 2s ease-in-out infinite;
}

@keyframes socialHover {

    0%,
    100% {
        transform: translateY(-4px);
    }

    50% {
        transform: translateY(-6px);
    }
}

.footer-social img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
    transition: filter 0.3s ease;
}

.footer-social a:hover img {
    filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 46, 189, 0.8));
}

/* Easter Egg Animations */
.footer-social a.easter-egg-glow {
    animation: easterEggGlow 1s ease-in-out;
}

.footer-social a.easter-egg-explode {
    animation: easterEggExplode 0.8s ease-out forwards;
}

.footer-social a.easter-egg-scatter {
    animation: easterEggScatter 1.5s ease-out forwards;
}

.footer-social a.easter-egg-return {
    animation: easterEggReturn 1s ease-in-out forwards;
}

@keyframes easterEggGlow {
    0% {
        box-shadow: 0 0 10px rgba(255, 46, 189, 0.5);
    }

    50% {
        box-shadow: 0 0 20px rgba(255, 46, 189, 0.8), 0 0 30px rgba(255, 100, 50, 0.6);
        transform: scale(1.1) translateY(-2px);
    }

    100% {
        box-shadow: 0 0 15px rgba(255, 100, 50, 0.7);
        transform: scale(1.05);
    }
}

@keyframes easterEggExplode {
    0% {
        transform: scale(1.05);
        filter: brightness(1.5);
    }

    50% {
        transform: scale(1.3);
        filter: brightness(2) saturate(2);
        box-shadow: 0 0 40px rgba(255, 46, 189, 1), 0 0 60px rgba(255, 100, 50, 0.8);
    }

    100% {
        transform: scale(0.1);
        opacity: 0;
        filter: brightness(3) saturate(3);
    }
}

@keyframes easterEggScatter {
    0% {
        transform: scale(0.1);
        opacity: 0;
    }

    10% {
        opacity: 1;
        transform: scale(0.8);
    }

    100% {
        opacity: 0;
        transform: scale(0.5);
    }
}

@keyframes easterEggReturn {
    0% {
        opacity: 0;
        transform: scale(0.5) translateY(-20px);
    }

    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Smoke effect */
.smoke-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    animation: smokeRise 2s ease-out forwards;
}

@keyframes smokeRise {
    0% {
        opacity: 0.7;
        transform: scale(1) translateY(0);
    }

    100% {
        opacity: 0;
        transform: scale(3) translateY(-50px);
    }
}

.footer-celebrate {
    display: flex;
    align-items: center;
}

.btn-modern {
    background: linear-gradient(135deg, var(--pink), var(--pink-2));
    border: none;
    padding: 0.75rem 1.5rem;
    color: var(--white);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 46, 189, 0.4);
}

.btn-modern__inner {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Large screens - optimal spacing */
@media (min-width: 1025px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

/* Medium screens - ensure 4 columns fit on same row */
@media (min-width: 769px) and (max-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.8rem;
        padding: 0 1rem;
    }

    .footer-section h3 {
        font-size: 1rem;
        margin-bottom: 0.6rem;
    }

    .footer-section p {
        font-size: 0.8rem;
        line-height: 1.4;
        margin-bottom: 0.3rem;
    }

    .footer-nav a {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .footer-tagline {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .brand-ft {
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: left;
        padding: 0 1.5rem;
    }

    /* Brand section with better spacing */
    .footer-brand {
        text-align: center;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid var(--card-bd);
        margin-bottom: 0.5rem;
    }

    /* Other sections use centered alignment for better balance */
    .footer-section {
        text-align: center;
        padding: 1rem 0;
    }

    .footer-section h3 {
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    /* Upcoming show card mobile treatment */
    .upcoming-show-card {
        padding: 1.5rem;
        margin: 0 auto;
        max-width: 300px;
    }

    .show-details {
        text-align: center;
    }

    .show-city {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    /* Remove pink bullets on mobile for cleaner look */
    .show-info li::before {
        display: none;
    }

    /* Adjust show info spacing without bullets */
    .show-info li {
        padding-left: 0;
        margin-bottom: 0.3rem;
        font-size: 0.85rem;
    }

    /* Navigation links with better spacing */
    .footer-nav {
        gap: 0.5rem;
        align-items: center;
        justify-content: center;
    }

    .footer-nav a {
        font-size: 0.9rem;
        line-height: 1.4;
        padding: 0.25rem 0;
    }

    /* Bottom section with better balance */
    .footer-bottom {
        padding-top: 1.5rem;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1.25rem;
    }

    .footer-legal {
        order: 3;
        max-width: 300px;
        margin: 0 auto;
    }

    .footer-legal small {
        font-size: 0.75rem;
        line-height: 1.3;
    }

    .footer-social {
        order: 1;
        justify-content: center;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .footer-social a {
        width: 36px;
        height: 36px;
    }

    .footer-social img {
        width: 18px;
        height: 18px;
    }

    .footer-celebrate {
        order: 2;
        justify-content: center;
    }

    .btn-modern {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Extra small mobile screens */
@media (max-width: 480px) {
    .footer-content {
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .footer-brand {
        padding-bottom: 1rem;
    }

    .footer-section {
        padding: 0.75rem 0;
    }

    .upcoming-show-card {
        padding: 1rem;
        max-width: 280px;
    }

    .footer-nav {
        gap: 0.4rem;
    }

    .footer-nav a {
        font-size: 0.85rem;
    }

    .footer-bottom {
        padding-top: 1rem;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .footer-bottom-content {
        gap: 1rem;
    }

    .footer-social {
        gap: 0.5rem;
    }

    .footer-social a {
        width: 32px;
        height: 32px;
    }

    .footer-social img {
        width: 16px;
        height: 16px;
    }

    .btn-modern {
        padding: 0.6rem 1.25rem;
        font-size: 0.85rem;
    }

    .footer-legal small {
        font-size: 0.7rem;
    }
}

/* Events Page - City Cards */
.cities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.city-card {
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.city-card:hover {
    border-color: var(--pink);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
}

.city-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.city-card__title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.city-card__status {
    background: var(--pink);
    color: var(--white);
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 2px;
}

.city-card__details {
    margin-bottom: 2rem;
}

.city-card__detail {
    color: var(--fg);
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.city-card__detail strong {
    color: var(--white);
    margin-right: 0.5rem;
}

.city-card__actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.city-card .btn {
    flex: 1;
    min-width: 120px;
    text-align: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
    font-family: inherit;
}

.city-card .btn-primary {
    background: linear-gradient(135deg, var(--pink), var(--pink-2));
    color: var(--white);
}

.city-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 46, 189, 0.4);
}

.city-card .btn-secondary {
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    color: var(--white);
}

.city-card .btn-secondary:hover {
    border-color: var(--pink);
    background: var(--pink);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .cities-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .city-card {
        padding: 1.5rem;
    }

    .city-card__header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .city-card__actions {
        flex-direction: column;
    }

    .city-card .btn {
        flex: none;
        width: 100%;
    }
}

/* Coming Soon Section */
.coming-soon-section {
    margin: 4rem 0 2rem 0;
    padding: 3rem 0;
    border-top: 1px solid var(--card-bd);
}

.coming-soon-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 1rem;
    text-align: center;
}

.coming-soon-subtitle {
    color: var(--fg);
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.coming-soon-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.coming-soon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--card-bg);
    border: 1px solid var(--card-bd);
    transition: all 0.3s ease;
}

.coming-soon-item:hover {
    border-color: var(--pink);
    transform: translateX(4px);
}

.coming-soon-city {
    color: var(--white);
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.btn-notify {
    background: var(--card-bg);
    border: 2px solid var(--pink);
    color: var(--pink);
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
}

.btn-notify:hover {
    background: var(--pink);
    color: var(--white);
    transform: translateY(-2px);
}

/* Notification Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-content {
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    position: relative;
    animation: modalSlideIn 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 12px;
}

.modal-content--large {
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

/* Modal actions positioning */
.modal-actions--top {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0;
    position: relative;
    z-index: 10;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--fg);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    line-height: 1;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--pink);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modal-subtitle {
    color: var(--fg);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.modal-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    color: var(--white);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.9rem;
}

.form-input {
    padding: 0.75rem;
    background: var(--bg);
    border: 2px solid var(--card-bd);
    color: var(--white);
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-input:focus {
    outline: none;
    border-color: var(--pink);
}

.form-input::placeholder {
    color: var(--fg);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.form-actions .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: inherit;
}

.modal-message {
    text-align: center;
    padding: 2rem;
    font-size: 1.1rem;
    color: var(--white);
    background: var(--bg);
    border: 1px solid var(--card-bd);
}

@media (max-width: 768px) {
    .coming-soon-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .modal-content {
        margin: 1rem;
        padding: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* Gallery Styles */
.gallery-albums {
    padding: 2rem 0;
}

.albums-grid {
    display: grid;
    gap: 3rem;
}

.album-card {
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.album-card:hover {
    border-color: rgba(255, 46, 189, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.album-header {
    margin-bottom: 2rem;
}

.album-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.album-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.album-date {
    color: var(--pink);
    font-weight: 600;
}

.album-count {
    color: rgba(255, 255, 255, 0.6);
}

.album-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 0.95rem;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    max-width: 100%;
}

.photo-item {
    position: relative;
    aspect-ratio: 1;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.photo-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(255, 46, 189, 0.3);
}

.photo-item:hover .photo-image {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.photo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.more-photos {
    position: relative;
}

.more-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}

.more-text {
    color: var(--white);
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Gallery Responsive */
@media (max-width: 768px) {
    .photo-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
    }

    .album-card {
        padding: 1.5rem;
    }

    .album-title {
        font-size: 1.5rem;
    }

    .album-meta {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Photo Viewer Styles */
.photo-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.photo-viewer-container {
    position: relative;
    width: 90vw;
    height: 90vh;
    max-width: 1200px;
    max-height: 800px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-viewer-close {
    position: absolute;
    top: -60px;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--white);
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
}

.photo-viewer-close:hover {
    background: rgba(255, 46, 189, 0.3);
    transform: scale(1.1);
}

.photo-viewer-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--white);
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
}

.photo-viewer-nav:hover {
    background: rgba(255, 46, 189, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.photo-viewer-prev {
    left: -80px;
}

.photo-viewer-next {
    right: -80px;
}

.photo-viewer-counter {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 10001;
}

.photo-viewer-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10001;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--pink);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.photo-viewer-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.photo-viewer-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    border-radius: 12px;
    animation: imageSlideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes imageSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Photo Viewer Responsive */
@media (max-width: 768px) {
    .photo-viewer-container {
        width: 95vw;
        height: 85vh;
    }

    .photo-viewer-close {
        top: -50px;
        width: 40px;
        height: 40px;
    }

    .photo-viewer-nav {
        width: 48px;
        height: 48px;
    }

    .photo-viewer-prev {
        left: -60px;
    }

    .photo-viewer-next {
        right: -60px;
    }

    .photo-viewer-counter {
        bottom: -50px;
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Event Details Modal */
.modal-header {
    margin-bottom: 2rem;
}

.modal-image {
    margin-top: 1rem;
}

.modal-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.detail-section h4 {
    color: var(--pink);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.detail-grid {
    display: grid;
    gap: 0.75rem;
}

.detail-item {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid var(--pink);
}

.detail-item strong {
    color: var(--pink);
    margin-right: 0.5rem;
}

.detail-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.detail-section li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-section li:last-child {
    border-bottom: none;
}

.modal-actions {
    margin-top: 2rem;
    text-align: center;
}

/* FAQ Sections */
.faq-sections {
    display: grid;
    gap: 3rem;
    margin: 3rem auto;
    max-width: 1200px;
    padding: 0 1rem;
}

.faq-section {
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.faq-section:hover {
    border-color: rgba(255, 46, 189, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.faq-section h2 {
    color: var(--pink);
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 700;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.faq-section-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--pink);
}

/* Section Icons */
.section-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--pink);
    margin-right: 0.75rem;
}

/* Contact and other section headers with icons */
.contact-grid h2,
.transportation h2,
.important-notes h2 {
    display: flex;
    align-items: center;
    color: var(--pink);
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 700;
}

/* Celebrate icon in footer */
.celebrate-icon {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

/* Sticky Ticket Icon Styles */
.sticky-ticket-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1001;
    animation: ticketSlideIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.sticky-ticket-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--pink);
    border-radius: 50%;
    box-shadow: 0 4px 20px rgba(255, 46, 189, 0.4);
    transition: all 0.3s ease;
    text-decoration: none;
}

.sticky-ticket-link:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 46, 189, 0.6);
}

.sticky-ticket-svg {
    width: 24px;
    height: 24px;
    color: white;
    stroke-width: 2;
}

/* Modal content shifting animations */
.modal-actions--shifted {
    transform: translateX(-60px);
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn--hidden {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* Individual detail item shifting for sticky ticket */
.detail-item {
    transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        padding-right 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-content--large.has-sticky-ticket .detail-item {
    width: calc(100% - 80px);
    padding-right: 20px;
}

/* Text content shifting - only paragraphs and lists, not in detail-items */
.detail-section>p,
.detail-section>ul,
.detail-section>h4 {
    transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        padding-right 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-content--large.has-sticky-ticket .detail-section>p,
.modal-content--large.has-sticky-ticket .detail-section>ul,
.modal-content--large.has-sticky-ticket .detail-section>h4 {
    width: calc(100% - 80px);
    padding-right: 20px;
}

/* Keyframe animations */
@keyframes ticketSlideIn {
    0% {
        transform: translateX(100px) scale(0.5);
        opacity: 0;
    }

    50% {
        transform: translateX(-10px) scale(1.1);
        opacity: 0.8;
    }

    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

/* Button wiggle animation when returning to normal position */
@keyframes buttonWiggle {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

.btn-primary.wiggle {
    animation: buttonWiggle 0.5s ease-in-out;
}

.faq-grid {
    display: grid;
    gap: 1rem;
}

.faq-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: rgba(255, 46, 189, 0.3);
}

.faq-question {
    width: 100%;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: none;
    color: var(--white);
    font-size: 1.1rem;
    font-weight: 600;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.faq-question:hover {
    background: rgba(255, 46, 189, 0.1);
    color: var(--pink);
}

.faq-question.active {
    background: rgba(255, 46, 189, 0.15);
    color: var(--pink);
}

.faq-question::after {
    content: '+';
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    font-weight: 300;
    transition: transform 0.3s ease;
}

.faq-question.active::after {
    transform: translateY(-50%) rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background: var(--card-bg);
}

.faq-answer.active {
    max-height: 500px;
    padding: 1.5rem;
}

.faq-answer p {
    margin: 0 0 1rem 0;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
}

.faq-answer p:last-child {
    margin-bottom: 0;
}

.faq-answer strong {
    color: var(--pink);
}

/* FAQ CTA Section */
.faq-cta {
    text-align: center;
    padding: 3rem 2rem;
    margin: 3rem 0;
    background: var(--card-bg);
    border: 2px solid var(--card-bd);
    border-radius: 8px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.faq-cta h2 {
    color: var(--pink);
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 700;
}

.faq-cta p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
}

.faq-cta .btn {
    margin: 0 1rem 1rem 0;
    min-width: 150px;
}

.faq-cta .btn:last-child {
    margin-right: 0;
}

@media (max-width: 768px) {
    .faq-sections {
        margin: 2rem 0;
        gap: 2rem;
        padding: 0 1rem;
    }

    .faq-section {
        padding: 1.5rem;
        margin: 0;
    }

    .faq-section h2 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .faq-question {
        padding: 1.25rem;
        font-size: 1rem;
        text-align: left;
    }

    .faq-answer.active {
        padding: 1.25rem;
    }

    .faq-cta {
        margin: 2rem 0;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .faq-cta h2 {
        font-size: 1.75rem;
    }

    .faq-cta .btn {
        display: block;
        margin: 0 0 1rem 0;
        width: 100%;
        max-width: 300px;
    }
}

/* Upcoming Show Card in Footer */
.upcoming-show-card {
    background: var(--card-bg);
    border: 1px solid var(--card-bd);
    border-radius: 8px;
    padding: 1.5rem;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    /* Align the card heading with other footer section headings */
    padding-top: 1rem;
    margin-top: -17px;
    /* Offset to align heading with other sections */
}

.upcoming-show-card:hover {
    border-color: rgba(255, 46, 189, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.upcoming-show-card h3 {
    color: var(--pink);
    margin-bottom: 1rem;
    margin-top: 0;
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.show-city {
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
    color: var(--white);
}

.show-info {
    list-style: none;
    padding: 0;
    margin: 0;
}

.show-info li {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.25rem;
    position: relative;
    padding-left: 1rem;
}

.show-info li::before {
    content: '•';
    color: var(--pink);
    position: absolute;
    left: 0;
    top: 0;
}

.show-expand {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
    width: 20px;
    height: 20px;
    transition: width 0.4s ease;
}

.expand-icon {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--white);
    transition: transform 0.3s ease;
    flex-shrink: 0;
    line-height: 1;
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: -3.44px;
}

.expand-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--white);
    margin-left: 0.5rem;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease 0.1s;
    white-space: nowrap;
    line-height: 1;
    height: 20px;
    display: flex;
    align-items: center;
}

.upcoming-show-card:hover .show-expand {
    width: 100px;
}

.upcoming-show-card:hover .expand-icon {
    transform: rotate(-90deg) translateX(-5px);
}

.upcoming-show-card:hover .expand-text {
    opacity: 1;
    transform: translateX(0);
    animation: pinkShine 2s ease-in-out infinite;
}

@keyframes pinkShine {

    0%,
    100% {
        color: var(--white);
    }

    50% {
        color: var(--pink);
        text-shadow: 0 0 8px rgba(255, 46, 189, 0.6);
    }
}

/* Contact Page */
.contact-grid {
    display: grid;
    gap: 3rem;
    margin: 3rem 0;
}

.venue-info .venue-card {
    max-width: 600px;
    margin: 0 auto;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-method {
    min-height: 150px;
}

.contact-method .city-card__header {
    text-align: center;
    margin-bottom: 1rem;
}

.contact-method .city-card__actions {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
}

@media (max-width: 768px) {
    .contact-methods {
        grid-template-columns: 1fr;
        max-width: 400px;
    }

    .venue-info .venue-card {
        max-width: 100%;
    }
}