import type { Metada<PERSON> } from "next";
import { ClerkProvider } from "@clerk/nextjs";
import { monoton } from "@/lib/fonts";
import { Layout } from "@/components/layout";
import { ConvexClientProvider } from "@/components/providers/ConvexProvider";
import StructuredData from "@/components/StructuredData";
import "./globals.css";

export const metadata: Metadata = {
  title: "Chocolate & Art Show — Dallas | September 18-19, 2025",
  description: "Immersive art, live music, body painting, and artisan chocolate. Two nights in Dallas at Lofty Spaces. 21+ event featuring local artists and DJs.",
  keywords: ["art show", "Dallas events", "chocolate tasting", "body painting", "live music", "21+ event", "local artists", "immersive experience", "gallery night", "cultural event"],
  authors: [{ name: "Chocolate & Art Show" }],
  openGraph: {
    title: "Chocolate & Art Show — Dallas | September 18-19, 2025",
    description: "Immersive art, live music, body painting, and artisan chocolate. Two nights in Dallas at Lofty Spaces. 21+ event featuring local artists and DJs.",
    type: "website",
    url: "https://chocolateandartshow.com/",
    images: [
      {
        url: "https://chocolateandartshow.com/images/gallery/hero-image.jpg",
        width: 1200,
        height: 630,
        alt: "Chocolate & Art Show Dallas - Immersive art experience",
      },
    ],
    siteName: "Chocolate & Art Show",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    site: "@chocolateandartshow",
    creator: "@chocolateandartshow",
    title: "Chocolate & Art Show — Dallas | September 18-19, 2025",
    description: "Immersive art, live music, body painting, and artisan chocolate. Two nights in Dallas at Lofty Spaces. 21+ event featuring local artists and DJs.",
    images: ["https://chocolateandartshow.com/images/gallery/hero-image.jpg"],
  },
  icons: {
    icon: "/images/brand/choco-logo.png",
    apple: "/images/brand/choco-logo.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" className={monoton.variable}>
        <head>
          <StructuredData />
        </head>
        <body>
          <ConvexClientProvider>
            <Layout>
              {children}
            </Layout>
          </ConvexClientProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
