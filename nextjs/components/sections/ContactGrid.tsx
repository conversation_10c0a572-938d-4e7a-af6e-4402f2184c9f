'use client';

// import { EnvelopeIcon } from '@heroicons/react/24/outline';

export default function ContactGrid() {
  const openCompose = (email: string, subject: string, body: string) => {
    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
  };

  return (
    <section className="contact-info">
      {/* <h2>
        <EnvelopeIcon className="section-icon" />
        Get in Touch
      </h2> */}
      <div className="contact-methods">
        <div className="contact-method city-card">
          <div className="city-card__header">
            <h3 className="city-card__title">GENERAL INQUIRIES</h3>
          </div>
          <div className="city-card__actions">
            <button
              onClick={() => openCompose('<EMAIL>', 'General Inquiry', 'Hello, I have a question about the Chocolate & Art Show...')}
              className="btn btn-primary"
            >
              <EMAIL>
            </button>
          </div>
        </div>
        <div className="contact-method city-card">
          <div className="city-card__header">
            <h3 className="city-card__title">ARTIST SUBMISSIONS</h3>
          </div>
          <div className="city-card__actions">
            <button
              onClick={() => openCompose('<EMAIL>', 'Artist Submission — Dallas/September', 'Links + portfolio + dimensions')}
              className="btn btn-primary"
            >
              SUBMIT YOUR WORK
            </button>
          </div>
        </div>
        <div className="contact-method city-card">
          <div className="city-card__header">
            <h3 className="city-card__title">VENDOR APPLICATIONS</h3>
          </div>
          <div className="city-card__actions">
            <button
              onClick={() => openCompose('<EMAIL>', 'Vendor Application — Dallas/September', 'Line sheet + power needs')}
              className="btn btn-primary"
            >
              APPLY TO SELL
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
