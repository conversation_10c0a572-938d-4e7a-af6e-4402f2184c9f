'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useEffect } from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';

export default function Footer() {
  useEffect(() => {
    // Easter Egg functionality for social icons
    let hoverSequence: number[] = [];
    const correctSequence = [0, 1, 2, 3, 4, 5, 5, 4, 3, 2, 1, 0]; // Left to right, then right to left
    let isEasterEggActive = false;

    const socialIcons = document.querySelectorAll('.footer-social a');

    const resetSequence = () => {
      hoverSequence = [];
    };

    const createSmokeParticles = (element: Element) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      for (let i = 0; i < 8; i++) {
        const particle = document.createElement('div');
        particle.className = 'smoke-particle';
        particle.style.position = 'fixed';
        particle.style.left = centerX + (Math.random() - 0.5) * 20 + 'px';
        particle.style.top = centerY + (Math.random() - 0.5) * 20 + 'px';
        particle.style.zIndex = '9999';
        document.body.appendChild(particle);

        setTimeout(() => {
          particle.remove();
        }, 2000);
      }
    };

    const triggerEasterEgg = () => {
      if (isEasterEggActive) return;
      isEasterEggActive = true;

      // Phase 1: Glow effect
      socialIcons.forEach((icon, index) => {
        setTimeout(() => {
          icon.classList.add('easter-egg-glow');
        }, index * 100);
      });

      // Phase 2: Explode and scatter
      setTimeout(() => {
        socialIcons.forEach((icon) => {
          icon.classList.remove('easter-egg-glow');
          icon.classList.add('easter-egg-explode');

          setTimeout(() => {
            createSmokeParticles(icon);
            icon.classList.remove('easter-egg-explode');
            icon.classList.add('easter-egg-scatter');

            // Random scatter directions
            const randomX = (Math.random() - 0.5) * 400;
            const randomY = (Math.random() - 0.5) * 400;
            (icon as HTMLElement).style.transform = `translate(${randomX}px, ${randomY}px) scale(0.5) rotate(${Math.random() * 360}deg)`;
          }, 400);
        });
      }, 1000);

      // Phase 3: Return to original positions
      setTimeout(() => {
        socialIcons.forEach((icon, index) => {
          setTimeout(() => {
            icon.classList.remove('easter-egg-scatter');
            icon.classList.add('easter-egg-return');
            (icon as HTMLElement).style.transform = '';

            setTimeout(() => {
              icon.classList.remove('easter-egg-return');
              isEasterEggActive = false;
            }, 1000);
          }, index * 150);
        });
      }, 3000);
    };

    socialIcons.forEach((icon, index) => {
      icon.addEventListener('mouseenter', () => {
        if (isEasterEggActive) return;

        hoverSequence.push(index);

        // Check if sequence matches the correct pattern
        const isCorrectSoFar = hoverSequence.every((val, i) => val === correctSequence[i]);

        if (!isCorrectSoFar) {
          resetSequence();
          hoverSequence.push(index);
        }

        // Check if complete sequence is achieved
        if (hoverSequence.length === correctSequence.length &&
          hoverSequence.every((val, i) => val === correctSequence[i])) {
          triggerEasterEgg();
          resetSequence();
        }

        // Reset if sequence gets too long without matching
        if (hoverSequence.length > correctSequence.length) {
          resetSequence();
        }
      });
    });

    // Reset sequence after period of inactivity
    let resetTimer: NodeJS.Timeout;
    const resetAfterDelay = () => {
      clearTimeout(resetTimer);
      resetTimer = setTimeout(resetSequence, 3000);
    };

    socialIcons.forEach(icon => {
      icon.addEventListener('mouseenter', resetAfterDelay);
    });

    // Initialize confetti functionality
    const confettiButton = document.getElementById('footer-confetti-trigger');
    const canvas = document.getElementById('confetti-canvas') as HTMLCanvasElement;

    if (confettiButton && canvas) {
      confettiButton.addEventListener('click', () => {
        // Confetti animation will be implemented later
        console.log('Confetti triggered!');
      });
    }

    return () => {
      clearTimeout(resetTimer);
    };
  }, []);

  return (
    <>
      {/* Footer */}
      <footer className="site-footer" id="site-footer" role="contentinfo">
        <div className="footer-content">
          {/* Brand Section */}
          <div className="footer-brand">
            <Link className="brand-ft" href="/" aria-label="Chocolate & Art Show home">
              <Image
                src="/images/brand/choco-logo.png"
                width={32}
                height={32}
                alt="Chocolate & Art Show"
              />
              <span>Chocolate & Art Show</span>
            </Link>
            <p className="footer-tagline">Where art meets chocolate, and creativity flows.</p>
          </div>

          {/* Quick Links Section */}
          <div className="footer-section">
            <h3>Quick Links</h3>
            <nav className="footer-nav" aria-label="Footer navigation">
              <Link href="/events/dallas-tx-2025-09-18-19">Buy Tickets</Link>
              <Link href="/artists">Meet Artists</Link>
              <Link href="/shows">Event Schedule</Link>
              <Link href="/#gallery">Photo Gallery</Link>
            </nav>
          </div>

          {/* Get Involved Section */}
          <div className="footer-section">
            <h3>Get Involved</h3>
            <nav className="footer-nav" aria-label="Get involved navigation">
              <Link
                href="mailto:<EMAIL>?subject=Artist%20Submission%20—%20Dallas/September&body=Links%20+%20portfolio%20+%20dimensions"
              >
                Apply as Artist
              </Link>
              <Link
                href="mailto:<EMAIL>?subject=Vendor%20Application%20—%20Dallas/September&body=Line%20sheet%20+%20power%20needs"
              >
                Apply as Vendor
              </Link>
              <Link href="/contact">Contact Us</Link>
            </nav>
          </div>

          {/* Upcoming Show Section */}
          <div className="footer-section upcoming-show-card">
            <h3>Upcoming Show:</h3>
            <div className="show-details">
              <div className="show-city">Dallas, TX</div>
              <ul className="show-info">
                <li>September 18-19, 2025</li>
                <li>Lofty Spaces</li>
                <li>21+ Event</li>
                <li>Doors: 7:00 PM</li>
              </ul>
            </div>
            {/* <div className="show-expand">
              <span className="expand-icon">+</span>
              <span className="expand-text">see more</span>
            </div> */}
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="footer-legal">
              <small>© 2025 Chocolate & Art Show. All rights reserved.</small>
              <small>An immersive experience celebrating art, music, and artisan chocolate.</small>
            </div>

            {/* Social Icons */}
            <nav className="footer-social" aria-label="Social links">
              <a href="https://www.facebook.com/ChocolateAndArtShowDallas" target="_blank" rel="noopener"
                aria-label="Facebook">
                <Image src="/images/brand/ico/facebook-white.svg" alt="Facebook" width={24} height={24} />
              </a>
              <a href="https://www.instagram.com/chocolateandartshow/" target="_blank" rel="noopener" aria-label="Instagram">
                <Image src="/images/brand/ico/instagram-white.svg" alt="Instagram" width={24} height={24} />
              </a>
              <a href="https://www.youtube.com/@ChocolateAndArtShow" target="_blank" rel="noopener" aria-label="YouTube">
                <Image src="/images/brand/ico/youtube-white.svg" alt="YouTube" width={24} height={24} />
              </a>
              <a href="https://vimeo.com/chocolateandartshow" target="_blank" rel="noopener" aria-label="Vimeo">
                <Image src="/images/brand/ico/vimeo-white.svg" alt="Vimeo" width={24} height={24} />
              </a>
              <a href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089" target="_blank"
                rel="noopener" aria-label="Eventbrite">
                <Image src="/images/brand/ico/eventbrite-white.svg" alt="Eventbrite" width={24} height={24} />
              </a>
              <a href="mailto:<EMAIL>" aria-label="Email">
                <Image src="/images/brand/ico/mail-white.svg" alt="Email" width={24} height={24} />
              </a>
            </nav>

            {/* Celebrate button */}
            <div className="footer-celebrate">
              <button className="btn-modern" id="footer-confetti-trigger" aria-label="Celebrate Dallas">
                <span className="btn-modern__inner">
                  <SparklesIcon className="celebrate-icon" />
                  Celebrate
                </span>
              </button>
            </div>
          </div>
        </div>
      </footer>

      <canvas id="confetti-canvas" className="confetti-canvas" aria-hidden="true"></canvas>
    </>
  );
}
