{"version": 3, "sources": ["../../../src/browser/simple_client.ts"], "sourcesContent": ["import { validateDeploymentUrl } from \"../common/index.js\";\nimport {\n  BaseConvexClient,\n  BaseConvexClientOptions,\n  MutationOptions,\n  QueryToken,\n  UserIdentityAttributes,\n} from \"./index.js\";\nimport {\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n} from \"../server/index.js\";\nimport { getFunctionName } from \"../server/api.js\";\nimport { AuthTokenFetcher } from \"./sync/authentication_manager.js\";\nimport { ConnectionState } from \"./sync/client.js\";\n\n// In Node.js builds this points to a bundled WebSocket implementation. If no\n// WebSocket implementation is manually specified or globally available,\n// this one is used.\nlet defaultWebSocketConstructor: typeof WebSocket | undefined;\n\n/** internal */\nexport function setDefaultWebSocketConstructor(ws: typeof WebSocket) {\n  defaultWebSocketConstructor = ws;\n}\n\nexport type ConvexClientOptions = BaseConvexClientOptions & {\n  /**\n   * `disabled` makes onUpdate callback registration a no-op and actions,\n   * mutations and one-shot queries throw. Setting disabled to true may be\n   * useful for server-side rendering, where subscriptions don't make sense.\n   */\n  disabled?: boolean;\n  /**\n   * Whether to prompt users in browsers about queued or in-flight mutations.\n   * This only works in environments where `window.onbeforeunload` is available.\n   *\n   * Defaults to true when `window` is defined, otherwise false.\n   */\n  unsavedChangesWarning?: boolean;\n};\n\n/**\n * Stops callbacks from running.\n *\n * @public\n */\nexport type Unsubscribe<T> = {\n  /** Stop calling callback when query results changes. If this is the last listener on this query, stop received updates. */\n  (): void;\n  /** Stop calling callback when query results changes. If this is the last listener on this query, stop received updates. */\n  unsubscribe(): void;\n  /** Get the last known value, possibly with local optimistic updates applied. */\n  getCurrentValue(): T | undefined;\n  /** @internal */\n  getQueryLogs(): string[] | undefined;\n};\n\n/**\n * Subscribes to Convex query functions and executes mutations and actions over a WebSocket.\n *\n * Optimistic updates for mutations are not provided for this client.\n * Third party clients may choose to wrap {@link browser.BaseConvexClient} for additional control.\n *\n * ```ts\n * const client = new ConvexClient(\"https://happy-otter-123.convex.cloud\");\n * const unsubscribe = client.onUpdate(api.messages.list, {}, (messages) => {\n *   console.log(messages[0].body);\n * });\n * ```\n *\n * @public\n */\nexport class ConvexClient {\n  private listeners: Set<QueryInfo>;\n  private _client: BaseConvexClient | undefined;\n  // A synthetic server event to run callbacks the first time\n  private callNewListenersWithCurrentValuesTimer:\n    | ReturnType<typeof setTimeout>\n    | undefined;\n  private _closed: boolean;\n  private _disabled: boolean;\n  /**\n   * Once closed no registered callbacks will fire again.\n   */\n  get closed(): boolean {\n    return this._closed;\n  }\n  get client(): BaseConvexClient {\n    if (this._client) return this._client;\n    throw new Error(\"ConvexClient is disabled\");\n  }\n  get disabled(): boolean {\n    return this._disabled;\n  }\n\n  /**\n   * Construct a client and immediately initiate a WebSocket connection to the passed address.\n   *\n   * @public\n   */\n  constructor(address: string, options: ConvexClientOptions = {}) {\n    if (options.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    const { disabled, ...baseOptions } = options;\n    this._closed = false;\n    this._disabled = !!disabled;\n    if (\n      defaultWebSocketConstructor &&\n      !(\"webSocketConstructor\" in baseOptions) &&\n      typeof WebSocket === \"undefined\"\n    ) {\n      baseOptions.webSocketConstructor = defaultWebSocketConstructor;\n    }\n    if (\n      typeof window === \"undefined\" &&\n      !(\"unsavedChangesWarning\" in baseOptions)\n    ) {\n      baseOptions.unsavedChangesWarning = false;\n    }\n    if (!this.disabled) {\n      this._client = new BaseConvexClient(\n        address,\n        (updatedQueries) => this._transition(updatedQueries),\n        baseOptions,\n      );\n    }\n    this.listeners = new Set();\n  }\n\n  /**\n   * Call a callback whenever a new result for a query is received. The callback\n   * will run soon after being registered if a result for the query is already\n   * in memory.\n   *\n   * The return value is an {@link Unsubscribe} object which is both a function\n   * an an object with properties. Both of the patterns below work with this object:\n   *\n   *```ts\n   * // call the return value as a function\n   * const unsubscribe = client.onUpdate(api.messages.list, {}, (messages) => {\n   *   console.log(messages);\n   * });\n   * unsubscribe();\n   *\n   * // unpack the return value into its properties\n   * const {\n   *   getCurrentValue,\n   *   unsubscribe,\n   * } = client.onUpdate(api.messages.list, {}, (messages) => {\n   *   console.log(messages);\n   * });\n   *```\n   *\n   * @param query - A {@link server.FunctionReference} for the public query to run.\n   * @param args - The arguments to run the query with.\n   * @param callback - Function to call when the query result updates.\n   * @param onError - Function to call when the query result updates with an error.\n   * If not provided, errors will be thrown instead of calling the callback.\n   *\n   * @return an {@link Unsubscribe} function to stop calling the onUpdate function.\n   */\n  onUpdate<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    args: FunctionArgs<Query>,\n    callback: (result: FunctionReturnType<Query>) => unknown,\n    onError?: (e: Error) => unknown,\n  ): Unsubscribe<Query[\"_returnType\"]> {\n    if (this.disabled) {\n      const disabledUnsubscribe = (() => {}) as Unsubscribe<\n        Query[\"_returnType\"]\n      >;\n      const unsubscribeProps: RemoveCallSignature<\n        Unsubscribe<Query[\"_returnType\"]>\n      > = {\n        unsubscribe: disabledUnsubscribe,\n        getCurrentValue: () => undefined,\n        getQueryLogs: () => undefined,\n      };\n      Object.assign(disabledUnsubscribe, unsubscribeProps);\n      return disabledUnsubscribe;\n    }\n\n    // BaseConvexClient takes care of deduplicating queries subscriptions...\n    const { queryToken, unsubscribe } = this.client.subscribe(\n      getFunctionName(query),\n      args,\n    );\n\n    // ...but we still need to bookkeep callbacks to actually call them.\n    const queryInfo: QueryInfo = {\n      queryToken,\n      callback,\n      onError,\n      unsubscribe,\n      hasEverRun: false,\n      query,\n      args,\n    };\n    this.listeners.add(queryInfo);\n\n    // If the callback is registered for a query with a result immediately available\n    // schedule a fake transition to call the callback soon instead of waiting for\n    // a new server update (which could take seconds or days).\n    if (\n      this.queryResultReady(queryToken) &&\n      this.callNewListenersWithCurrentValuesTimer === undefined\n    ) {\n      this.callNewListenersWithCurrentValuesTimer = setTimeout(\n        () => this.callNewListenersWithCurrentValues(),\n        0,\n      );\n    }\n\n    const unsubscribeProps: RemoveCallSignature<\n      Unsubscribe<Query[\"_returnType\"]>\n    > = {\n      unsubscribe: () => {\n        if (this.closed) {\n          // all unsubscribes already ran\n          return;\n        }\n        this.listeners.delete(queryInfo);\n        unsubscribe();\n      },\n      getCurrentValue: () => this.client.localQueryResultByToken(queryToken),\n      getQueryLogs: () => this.client.localQueryLogs(queryToken),\n    };\n    const ret = unsubscribeProps.unsubscribe as Unsubscribe<\n      Query[\"_returnType\"]\n    >;\n    Object.assign(ret, unsubscribeProps);\n    return ret;\n  }\n\n  // Run all callbacks that have never been run before if they have a query\n  // result available now.\n  private callNewListenersWithCurrentValues() {\n    this.callNewListenersWithCurrentValuesTimer = undefined;\n    this._transition([], true);\n  }\n\n  private queryResultReady(queryToken: QueryToken): boolean {\n    return this.client.hasLocalQueryResultByToken(queryToken);\n  }\n\n  async close() {\n    if (this.disabled) return;\n    // prevent pending updates\n    this.listeners.clear();\n    this._closed = true;\n    return this.client.close();\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT (typically an OpenID Connect Identity Token)\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange?: (isAuthenticated: boolean) => void,\n  ) {\n    if (this.disabled) return;\n    this.client.setAuth(\n      fetchToken,\n      onChange ??\n        (() => {\n          // Do nothing\n        }),\n    );\n  }\n\n  /**\n   * @internal\n   */\n  setAdminAuth(token: string, identity?: UserIdentityAttributes) {\n    if (this.closed) {\n      throw new Error(\"ConvexClient has already been closed.\");\n    }\n    if (this.disabled) return;\n    this.client.setAdminAuth(token, identity);\n  }\n\n  /**\n   * @internal\n   */\n  _transition(updatedQueries: QueryToken[], callNewListeners = false) {\n    // Deduping subscriptions happens in the BaseConvexClient, so not much to do here.\n\n    // Call all callbacks in the order they were registered\n    for (const queryInfo of this.listeners) {\n      const { callback, queryToken, onError, hasEverRun } = queryInfo;\n      if (\n        updatedQueries.includes(queryToken) ||\n        (callNewListeners &&\n          !hasEverRun &&\n          this.client.hasLocalQueryResultByToken(queryToken))\n      ) {\n        queryInfo.hasEverRun = true;\n        let newValue;\n        try {\n          newValue = this.client.localQueryResultByToken(queryToken);\n        } catch (error) {\n          if (!(error instanceof Error)) throw error;\n          if (onError) {\n            onError(\n              error,\n              \"Second argument to onUpdate onError is reserved for later use\",\n            );\n          } else {\n            // Make some noise without unsubscribing or failing to call other callbacks.\n            void Promise.reject(error);\n          }\n          continue;\n        }\n        callback(\n          newValue,\n          \"Second argument to onUpdate callback is reserved for later use\",\n        );\n      }\n    }\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param mutation - A {@link server.FunctionReference} for the public mutation\n   * to run.\n   * @param args - An arguments object for the mutation.\n   * @param options - A {@link MutationOptions} options object for the mutation.\n   * @returns A promise of the mutation's result.\n   */\n  async mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    args: FunctionArgs<Mutation>,\n    options?: MutationOptions,\n  ): Promise<Awaited<FunctionReturnType<Mutation>>> {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    return await this.client.mutation(getFunctionName(mutation), args, options);\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param action - A {@link server.FunctionReference} for the public action\n   * to run.\n   * @param args - An arguments object for the action.\n   * @returns A promise of the action's result.\n   */\n  async action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    args: FunctionArgs<Action>,\n  ): Promise<Awaited<FunctionReturnType<Action>>> {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    return await this.client.action(getFunctionName(action), args);\n  }\n\n  /**\n   * Fetch a query result once.\n   *\n   * @param query - A {@link server.FunctionReference} for the public query\n   * to run.\n   * @param args - An arguments object for the query.\n   * @returns A promise of the query's result.\n   */\n  async query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    args: Query[\"_args\"],\n  ): Promise<Awaited<Query[\"_returnType\"]>> {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    const value = this.client.localQueryResult(getFunctionName(query), args) as\n      | Awaited<Query[\"_returnType\"]>\n      | undefined;\n    if (value !== undefined) return Promise.resolve(value);\n\n    return new Promise((resolve, reject) => {\n      const { unsubscribe } = this.onUpdate(\n        query,\n        args,\n        (value) => {\n          unsubscribe();\n          resolve(value);\n        },\n        (e: Error) => {\n          unsubscribe();\n          reject(e);\n        },\n      );\n    });\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    return this.client.connectionState();\n  }\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    if (this.disabled) return () => {};\n    return this.client.subscribeToConnectionState(cb);\n  }\n}\n\n// internal information tracked about each registered callback\ntype QueryInfo = {\n  callback: (result: any, meta: unknown) => unknown;\n  onError: ((e: Error, meta: unknown) => unknown) | undefined;\n  unsubscribe: () => void;\n  queryToken: QueryToken;\n  hasEverRun: boolean;\n  // query and args are just here for debugging, the queryToken is authoritative\n  query: FunctionReference<\"query\">;\n  args: any;\n};\n\ntype RemoveCallSignature<T> = Omit<T, never>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAsC;AACtC,mBAMO;AAMP,iBAAgC;AAOhC,IAAI;AAGG,SAAS,+BAA+B,IAAsB;AACnE,gCAA8B;AAChC;AAiDO,MAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BxB,YAAY,SAAiB,UAA+B,CAAC,GAAG;AA3BhE,wBAAQ;AACR,wBAAQ;AAER;AAAA,wBAAQ;AAGR,wBAAQ;AACR,wBAAQ;AAqBN,QAAI,QAAQ,iCAAiC,MAAM;AACjD,+CAAsB,OAAO;AAAA,IAC/B;AACA,UAAM,EAAE,UAAU,GAAG,YAAY,IAAI;AACrC,SAAK,UAAU;AACf,SAAK,YAAY,CAAC,CAAC;AACnB,QACE,+BACA,EAAE,0BAA0B,gBAC5B,OAAO,cAAc,aACrB;AACA,kBAAY,uBAAuB;AAAA,IACrC;AACA,QACE,OAAO,WAAW,eAClB,EAAE,2BAA2B,cAC7B;AACA,kBAAY,wBAAwB;AAAA,IACtC;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,UAAU,IAAI;AAAA,QACjB;AAAA,QACA,CAAC,mBAAmB,KAAK,YAAY,cAAc;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EA5CA,IAAI,SAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAA2B;AAC7B,QAAI,KAAK,QAAS,QAAO,KAAK;AAC9B,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC5C;AAAA,EACA,IAAI,WAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqEA,SACE,OACA,MACA,UACA,SACmC;AACnC,QAAI,KAAK,UAAU;AACjB,YAAM,sBAAuB,MAAM;AAAA,MAAC;AAGpC,YAAMA,oBAEF;AAAA,QACF,aAAa;AAAA,QACb,iBAAiB,MAAM;AAAA,QACvB,cAAc,MAAM;AAAA,MACtB;AACA,aAAO,OAAO,qBAAqBA,iBAAgB;AACnD,aAAO;AAAA,IACT;AAGA,UAAM,EAAE,YAAY,YAAY,IAAI,KAAK,OAAO;AAAA,UAC9C,4BAAgB,KAAK;AAAA,MACrB;AAAA,IACF;AAGA,UAAM,YAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,IACF;AACA,SAAK,UAAU,IAAI,SAAS;AAK5B,QACE,KAAK,iBAAiB,UAAU,KAChC,KAAK,2CAA2C,QAChD;AACA,WAAK,yCAAyC;AAAA,QAC5C,MAAM,KAAK,kCAAkC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAEA,UAAM,mBAEF;AAAA,MACF,aAAa,MAAM;AACjB,YAAI,KAAK,QAAQ;AAEf;AAAA,QACF;AACA,aAAK,UAAU,OAAO,SAAS;AAC/B,oBAAY;AAAA,MACd;AAAA,MACA,iBAAiB,MAAM,KAAK,OAAO,wBAAwB,UAAU;AAAA,MACrE,cAAc,MAAM,KAAK,OAAO,eAAe,UAAU;AAAA,IAC3D;AACA,UAAM,MAAM,iBAAiB;AAG7B,WAAO,OAAO,KAAK,gBAAgB;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAIQ,oCAAoC;AAC1C,SAAK,yCAAyC;AAC9C,SAAK,YAAY,CAAC,GAAG,IAAI;AAAA,EAC3B;AAAA,EAEQ,iBAAiB,YAAiC;AACxD,WAAO,KAAK,OAAO,2BAA2B,UAAU;AAAA,EAC1D;AAAA,EAEA,MAAM,QAAQ;AACZ,QAAI,KAAK,SAAU;AAEnB,SAAK,UAAU,MAAM;AACrB,SAAK,UAAU;AACf,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QACE,YACA,UACA;AACA,QAAI,KAAK,SAAU;AACnB,SAAK,OAAO;AAAA,MACV;AAAA,MACA,aACG,MAAM;AAAA,MAEP;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAe,UAAmC;AAC7D,QAAI,KAAK,QAAQ;AACf,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AACA,QAAI,KAAK,SAAU;AACnB,SAAK,OAAO,aAAa,OAAO,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,gBAA8B,mBAAmB,OAAO;AAIlE,eAAW,aAAa,KAAK,WAAW;AACtC,YAAM,EAAE,UAAU,YAAY,SAAS,WAAW,IAAI;AACtD,UACE,eAAe,SAAS,UAAU,KACjC,oBACC,CAAC,cACD,KAAK,OAAO,2BAA2B,UAAU,GACnD;AACA,kBAAU,aAAa;AACvB,YAAI;AACJ,YAAI;AACF,qBAAW,KAAK,OAAO,wBAAwB,UAAU;AAAA,QAC3D,SAAS,OAAO;AACd,cAAI,EAAE,iBAAiB,OAAQ,OAAM;AACrC,cAAI,SAAS;AACX;AAAA,cACE;AAAA,cACA;AAAA,YACF;AAAA,UACF,OAAO;AAEL,iBAAK,QAAQ,OAAO,KAAK;AAAA,UAC3B;AACA;AAAA,QACF;AACA;AAAA,UACE;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SACJ,UACA,MACA,SACgD;AAChD,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,0BAA0B;AAC7D,WAAO,MAAM,KAAK,OAAO,aAAS,4BAAgB,QAAQ,GAAG,MAAM,OAAO;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OACJ,QACA,MAC8C;AAC9C,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,0BAA0B;AAC7D,WAAO,MAAM,KAAK,OAAO,WAAO,4BAAgB,MAAM,GAAG,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MACJ,OACA,MACwC;AACxC,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,0BAA0B;AAC7D,UAAM,QAAQ,KAAK,OAAO,qBAAiB,4BAAgB,KAAK,GAAG,IAAI;AAGvE,QAAI,UAAU,OAAW,QAAO,QAAQ,QAAQ,KAAK;AAErD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,EAAE,YAAY,IAAI,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,CAACC,WAAU;AACT,sBAAY;AACZ,kBAAQA,MAAK;AAAA,QACf;AAAA,QACA,CAAC,MAAa;AACZ,sBAAY;AACZ,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAmC;AACjC,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,0BAA0B;AAC7D,WAAO,KAAK,OAAO,gBAAgB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,2BACE,IACY;AACZ,QAAI,KAAK,SAAU,QAAO,MAAM;AAAA,IAAC;AACjC,WAAO,KAAK,OAAO,2BAA2B,EAAE;AAAA,EAClD;AACF;", "names": ["unsubscribeProps", "value"]}