{"version": 3, "sources": ["../../../../src/browser/sync/optimistic_updates_impl.ts"], "sourcesContent": ["import {\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n} from \"../../server/api.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { Value } from \"../../values/index.js\";\nimport { createHybridErrorStacktrace, forwardData } from \"../logging.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport { OptimisticLocalStore } from \"./optimistic_updates.js\";\nimport { RequestId } from \"./protocol.js\";\nimport {\n  canonicalizeUdfPath,\n  QueryToken,\n  serializePathAndArgs,\n} from \"./udf_path_utils.js\";\nimport { ConvexError } from \"../../values/errors.js\";\n\n/**\n * An optimistic update function that has been curried over its arguments.\n */\ntype WrappedOptimisticUpdate = (locaQueryStore: OptimisticLocalStore) => void;\n\n/**\n * The implementation of `OptimisticLocalStore`.\n *\n * This class provides the interface for optimistic updates to modify query results.\n */\nclass OptimisticLocalStoreImpl implements OptimisticLocalStore {\n  // A references of the query results in OptimisticQueryResults\n  private readonly queryResults: QueryResultsMap;\n\n  // All of the queries modified by this class\n  readonly modifiedQueries: QueryToken[];\n\n  constructor(queryResults: QueryResultsMap) {\n    this.queryResults = queryResults;\n    this.modifiedQueries = [];\n  }\n\n  getQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): undefined | FunctionReturnType<Query> {\n    const queryArgs = parseArgs(args[0]);\n    const name = getFunctionName(query);\n    const queryResult = this.queryResults.get(\n      serializePathAndArgs(name, queryArgs),\n    );\n    if (queryResult === undefined) {\n      return undefined;\n    }\n    return OptimisticLocalStoreImpl.queryValue(queryResult.result);\n  }\n\n  getAllQueries<Query extends FunctionReference<\"query\">>(\n    query: Query,\n  ): {\n    args: FunctionArgs<Query>;\n    value: undefined | FunctionReturnType<Query>;\n  }[] {\n    const queriesWithName: {\n      args: FunctionArgs<Query>;\n      value: undefined | FunctionReturnType<Query>;\n    }[] = [];\n    const name = getFunctionName(query);\n    for (const queryResult of this.queryResults.values()) {\n      if (queryResult.udfPath === canonicalizeUdfPath(name)) {\n        queriesWithName.push({\n          args: queryResult.args as FunctionArgs<Query>,\n          value: OptimisticLocalStoreImpl.queryValue(queryResult.result),\n        });\n      }\n    }\n    return queriesWithName;\n  }\n\n  setQuery<QueryReference extends FunctionReference<\"query\">>(\n    queryReference: QueryReference,\n    args: FunctionArgs<QueryReference>,\n    value: undefined | FunctionReturnType<QueryReference>,\n  ): void {\n    const queryArgs = parseArgs(args);\n    const name = getFunctionName(queryReference);\n    const queryToken = serializePathAndArgs(name, queryArgs);\n\n    let result: FunctionResult | undefined;\n    if (value === undefined) {\n      result = undefined;\n    } else {\n      result = {\n        success: true,\n        value,\n        // It's an optimistic update, so there are no function logs to show.\n        logLines: [],\n      };\n    }\n    const query: Query = {\n      udfPath: name,\n      args: queryArgs,\n      result,\n    };\n    this.queryResults.set(queryToken, query);\n    this.modifiedQueries.push(queryToken);\n  }\n\n  private static queryValue(\n    result: FunctionResult | undefined,\n  ): Value | undefined {\n    if (result === undefined) {\n      return undefined;\n    } else if (result.success) {\n      return result.value;\n    } else {\n      // If the query is an error state, just return `undefined` as though\n      // it's loading. Optimistic updates should already handle `undefined` well\n      // and there isn't a need to break the whole update because it tried\n      // to load a single query that errored.\n      return undefined;\n    }\n  }\n}\n\ntype OptimisticUpdateAndId = {\n  update: WrappedOptimisticUpdate;\n  mutationId: RequestId;\n};\n\ntype Query = {\n  // undefined means the query was set to be loading (undefined) in an optimistic update.\n  // Note that we can also have queries not present in the QueryResultMap\n  // at all because they are still loading from the server and have no optimistic update\n  // setting an optimistic value in advance.\n  result: FunctionResult | undefined;\n  udfPath: string;\n  args: Record<string, Value>;\n};\nexport type QueryResultsMap = Map<QueryToken, Query>;\n\ntype ChangedQueries = QueryToken[];\n\n/**\n * A view of all of our query results with optimistic updates applied on top.\n */\nexport class OptimisticQueryResults {\n  private queryResults: QueryResultsMap;\n  private optimisticUpdates: OptimisticUpdateAndId[];\n\n  constructor() {\n    this.queryResults = new Map();\n    this.optimisticUpdates = [];\n  }\n\n  /**\n   * Apply all optimistic updates on top of server query results\n   */\n  ingestQueryResultsFromServer(\n    serverQueryResults: QueryResultsMap,\n    optimisticUpdatesToDrop: Set<RequestId>,\n  ): ChangedQueries {\n    this.optimisticUpdates = this.optimisticUpdates.filter((updateAndId) => {\n      return !optimisticUpdatesToDrop.has(updateAndId.mutationId);\n    });\n\n    const oldQueryResults = this.queryResults;\n    this.queryResults = new Map(serverQueryResults);\n    const localStore = new OptimisticLocalStoreImpl(this.queryResults);\n    for (const updateAndId of this.optimisticUpdates) {\n      updateAndId.update(localStore);\n    }\n\n    // To find the changed queries, just do a shallow comparison\n    // TODO(CX-733): Change this so we avoid unnecessary rerenders\n    const changedQueries: ChangedQueries = [];\n    for (const [queryToken, query] of this.queryResults) {\n      const oldQuery = oldQueryResults.get(queryToken);\n      if (oldQuery === undefined || oldQuery.result !== query.result) {\n        changedQueries.push(queryToken);\n      }\n    }\n\n    return changedQueries;\n  }\n\n  applyOptimisticUpdate(\n    update: WrappedOptimisticUpdate,\n    mutationId: RequestId,\n  ): ChangedQueries {\n    // Apply the update to our store\n    this.optimisticUpdates.push({\n      update,\n      mutationId,\n    });\n    const localStore = new OptimisticLocalStoreImpl(this.queryResults);\n    update(localStore);\n\n    // Notify about any query results that changed\n    // TODO(CX-733): Change this so we avoid unnecessary rerenders\n    return localStore.modifiedQueries;\n  }\n\n  /**\n   * @internal\n   */\n  rawQueryResult(queryToken: QueryToken): Query | undefined {\n    return this.queryResults.get(queryToken);\n  }\n\n  queryResult(queryToken: QueryToken): Value | undefined {\n    const query = this.queryResults.get(queryToken);\n    if (query === undefined) {\n      return undefined;\n    }\n    const result = query.result;\n    if (result === undefined) {\n      return undefined;\n    } else if (result.success) {\n      return result.value;\n    } else {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(\n            createHybridErrorStacktrace(\"query\", query.udfPath, result),\n          ),\n        );\n      }\n      throw new Error(\n        createHybridErrorStacktrace(\"query\", query.udfPath, result),\n      );\n    }\n  }\n\n  hasQueryResult(queryToken: QueryToken): boolean {\n    return this.queryResults.get(queryToken) !== undefined;\n  }\n\n  /**\n   * @internal\n   */\n  queryLogs(queryToken: QueryToken): string[] | undefined {\n    const query = this.queryResults.get(queryToken);\n    return query?.result?.logLines;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAMO;AACP,oBAA0B;AAE1B,qBAAyD;AAIzD,4BAIO;AACP,oBAA4B;AAY5B,MAAM,yBAAyD;AAAA,EAO7D,YAAY,cAA+B;AAL3C;AAAA,wBAAiB;AAGjB;AAAA,wBAAS;AAGP,SAAK,eAAe;AACpB,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EAEA,SACE,UACG,MACoC;AACvC,UAAM,gBAAY,yBAAU,KAAK,CAAC,CAAC;AACnC,UAAM,WAAO,4BAAgB,KAAK;AAClC,UAAM,cAAc,KAAK,aAAa;AAAA,UACpC,4CAAqB,MAAM,SAAS;AAAA,IACtC;AACA,QAAI,gBAAgB,QAAW;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,yBAAyB,WAAW,YAAY,MAAM;AAAA,EAC/D;AAAA,EAEA,cACE,OAIE;AACF,UAAM,kBAGA,CAAC;AACP,UAAM,WAAO,4BAAgB,KAAK;AAClC,eAAW,eAAe,KAAK,aAAa,OAAO,GAAG;AACpD,UAAI,YAAY,gBAAY,2CAAoB,IAAI,GAAG;AACrD,wBAAgB,KAAK;AAAA,UACnB,MAAM,YAAY;AAAA,UAClB,OAAO,yBAAyB,WAAW,YAAY,MAAM;AAAA,QAC/D,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SACE,gBACA,MACA,OACM;AACN,UAAM,gBAAY,yBAAU,IAAI;AAChC,UAAM,WAAO,4BAAgB,cAAc;AAC3C,UAAM,iBAAa,4CAAqB,MAAM,SAAS;AAEvD,QAAI;AACJ,QAAI,UAAU,QAAW;AACvB,eAAS;AAAA,IACX,OAAO;AACL,eAAS;AAAA,QACP,SAAS;AAAA,QACT;AAAA;AAAA,QAEA,UAAU,CAAC;AAAA,MACb;AAAA,IACF;AACA,UAAM,QAAe;AAAA,MACnB,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACF;AACA,SAAK,aAAa,IAAI,YAAY,KAAK;AACvC,SAAK,gBAAgB,KAAK,UAAU;AAAA,EACtC;AAAA,EAEA,OAAe,WACb,QACmB;AACnB,QAAI,WAAW,QAAW;AACxB,aAAO;AAAA,IACT,WAAW,OAAO,SAAS;AACzB,aAAO,OAAO;AAAA,IAChB,OAAO;AAKL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAuBO,MAAM,uBAAuB;AAAA,EAIlC,cAAc;AAHd,wBAAQ;AACR,wBAAQ;AAGN,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,oBAAoB,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,6BACE,oBACA,yBACgB;AAChB,SAAK,oBAAoB,KAAK,kBAAkB,OAAO,CAAC,gBAAgB;AACtE,aAAO,CAAC,wBAAwB,IAAI,YAAY,UAAU;AAAA,IAC5D,CAAC;AAED,UAAM,kBAAkB,KAAK;AAC7B,SAAK,eAAe,IAAI,IAAI,kBAAkB;AAC9C,UAAM,aAAa,IAAI,yBAAyB,KAAK,YAAY;AACjE,eAAW,eAAe,KAAK,mBAAmB;AAChD,kBAAY,OAAO,UAAU;AAAA,IAC/B;AAIA,UAAM,iBAAiC,CAAC;AACxC,eAAW,CAAC,YAAY,KAAK,KAAK,KAAK,cAAc;AACnD,YAAM,WAAW,gBAAgB,IAAI,UAAU;AAC/C,UAAI,aAAa,UAAa,SAAS,WAAW,MAAM,QAAQ;AAC9D,uBAAe,KAAK,UAAU;AAAA,MAChC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,sBACE,QACA,YACgB;AAEhB,SAAK,kBAAkB,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,aAAa,IAAI,yBAAyB,KAAK,YAAY;AACjE,WAAO,UAAU;AAIjB,WAAO,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,YAA2C;AACxD,WAAO,KAAK,aAAa,IAAI,UAAU;AAAA,EACzC;AAAA,EAEA,YAAY,YAA2C;AACrD,UAAM,QAAQ,KAAK,aAAa,IAAI,UAAU;AAC9C,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,MAAM;AACrB,QAAI,WAAW,QAAW;AACxB,aAAO;AAAA,IACT,WAAW,OAAO,SAAS;AACzB,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,UAAI,OAAO,cAAc,QAAW;AAClC,kBAAM;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,gBACF,4CAA4B,SAAS,MAAM,SAAS,MAAM;AAAA,UAC5D;AAAA,QACF;AAAA,MACF;AACA,YAAM,IAAI;AAAA,YACR,4CAA4B,SAAS,MAAM,SAAS,MAAM;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAAA,EAEA,eAAe,YAAiC;AAC9C,WAAO,KAAK,aAAa,IAAI,UAAU,MAAM;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,YAA8C;AACtD,UAAM,QAAQ,KAAK,aAAa,IAAI,UAAU;AAC9C,WAAO,OAAO,QAAQ;AAAA,EACxB;AACF;", "names": []}