{"version": 3, "sources": ["../../../src/browser/http_client.ts"], "sourcesContent": ["import {\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n} from \"../server/api.js\";\nimport { parseArgs, validateDeploymentUrl } from \"../common/index.js\";\nimport { version } from \"../index.js\";\nimport {\n  ConvexError,\n  JSONValue,\n  convexToJson,\n  jsonToConvex,\n} from \"../values/index.js\";\nimport {\n  instantiateDefaultLogger,\n  instantiateNoopLogger,\n  logForFunction,\n  Logger,\n} from \"./logging.js\";\nimport {\n  ArgsAndOptions,\n  FunctionArgs,\n  UserIdentityAttributes,\n} from \"../server/index.js\";\n\nexport const STATUS_CODE_OK = 200;\nexport const STATUS_CODE_BAD_REQUEST = 400;\n// Special custom 5xx HTTP status code to mean that the UDF returned an error.\n//\n// Must match the constant of the same name in the backend.\nexport const STATUS_CODE_UDF_FAILED = 560;\n\n// Allow fetch to be shimmed in for Node.js < 18\nlet specifiedFetch: typeof globalThis.fetch | undefined = undefined;\nexport function setFetch(f: typeof globalThis.fetch) {\n  specifiedFetch = f;\n}\n\nexport type HttpMutationOptions = {\n  /**\n   * Skip the default queue of mutations and run this immediately.\n   *\n   * This allows the same HttpConvexClient to be used to request multiple\n   * mutations in parallel, something not possible with WebSocket-based clients.\n   */\n  skipQueue: boolean;\n};\n\n/**\n * A Convex client that runs queries and mutations over HTTP.\n *\n * This client is stateful (it has user credentials and queues mutations)\n * so take care to avoid sharing it between requests in a server.\n *\n * This is appropriate for server-side code (like Netlify Lambdas) or non-reactive\n * webapps.\n *\n * @public\n */\nexport class ConvexHttpClient {\n  private readonly address: string;\n  private auth?: string;\n  private adminAuth?: string;\n  private encodedTsPromise?: Promise<string>;\n  private debug: boolean;\n  private fetchOptions?: FetchOptions;\n  private logger: Logger;\n  private mutationQueue: Array<{\n    mutation: FunctionReference<\"mutation\">;\n    args: FunctionArgs<any>;\n    resolve: (value: any) => void;\n    reject: (error: any) => void;\n  }> = [];\n  private isProcessingQueue: boolean = false;\n\n  /**\n   * Create a new {@link ConvexHttpClient}.\n   *\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param options - An object of options.\n   * - `skipConvexDeploymentUrlCheck` - Skip validating that the Convex deployment URL looks like\n   * `https://happy-animal-123.convex.cloud` or localhost. This can be useful if running a self-hosted\n   * Convex backend that uses a different URL.\n   * - `logger` - A logger or a boolean. If not provided, logs to the console.\n   * You can construct your own logger to customize logging to log elsewhere\n   * or not log at all, or use `false` as a shorthand for a no-op logger.\n   * A logger is an object with 4 methods: log(), warn(), error(), and logVerbose().\n   * These methods can receive multiple arguments of any types, like console.log().\n   * - `auth` - A JWT containing identity claims accessible in Convex functions.\n   * This identity may expire so it may be necessary to call `setAuth()` later,\n   * but for short-lived clients it's convenient to specify this value here.\n   */\n  constructor(\n    address: string,\n    options?: {\n      skipConvexDeploymentUrlCheck?: boolean;\n      logger?: Logger | boolean;\n      auth?: string;\n    },\n  ) {\n    if (typeof options === \"boolean\") {\n      throw new Error(\n        \"skipConvexDeploymentUrlCheck as the second argument is no longer supported. Please pass an options object, `{ skipConvexDeploymentUrlCheck: true }`.\",\n      );\n    }\n    const opts = options ?? {};\n    if (opts.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    this.logger =\n      options?.logger === false\n        ? instantiateNoopLogger({ verbose: false })\n        : options?.logger !== true && options?.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: false });\n    this.address = address;\n    this.debug = true;\n    if (options?.auth) {\n      this.setAuth(options.auth);\n    }\n  }\n\n  /**\n   * Obtain the {@link ConvexHttpClient}'s URL to its backend.\n   * @deprecated Use url, which returns the url without /api at the end.\n   *\n   * @returns The URL to the Convex backend, including the client's API version.\n   */\n  backendUrl(): string {\n    return `${this.address}/api`;\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   *\n   * Should be called whenever the token changes (i.e. due to expiration and refresh).\n   *\n   * @param value - JWT-encoded OpenID Connect identity token.\n   */\n  setAuth(value: string) {\n    this.clearAuth();\n    this.auth = value;\n  }\n\n  /**\n   * Set admin auth token to allow calling internal queries, mutations, and actions\n   * and acting as an identity.\n   *\n   * @internal\n   */\n  setAdminAuth(token: string, actingAsIdentity?: UserIdentityAttributes) {\n    this.clearAuth();\n    if (actingAsIdentity !== undefined) {\n      // Encode the identity to a base64 string\n      const bytes = new TextEncoder().encode(JSON.stringify(actingAsIdentity));\n      const actingAsIdentityEncoded = btoa(String.fromCodePoint(...bytes));\n      this.adminAuth = `${token}:${actingAsIdentityEncoded}`;\n    } else {\n      this.adminAuth = token;\n    }\n  }\n\n  /**\n   * Clear the current authentication token if set.\n   */\n  clearAuth() {\n    this.auth = undefined;\n    this.adminAuth = undefined;\n  }\n\n  /**\n   * Sets whether the result log lines should be printed on the console or not.\n   *\n   * @internal\n   */\n  setDebug(debug: boolean) {\n    this.debug = debug;\n  }\n\n  /**\n   * Used to customize the fetch behavior in some runtimes.\n   *\n   * @internal\n   */\n  setFetchOptions(fetchOptions: FetchOptions) {\n    this.fetchOptions = fetchOptions;\n  }\n\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Execute a Convex query function at the same timestamp as every other\n   * consistent query execution run by this HTTP client.\n   *\n   * This doesn't make sense for long-lived ConvexHttpClients as Convex\n   * backends can read a limited amount into the past: beyond 30 seconds\n   * in the past may not be available.\n   *\n   * Create a new client to use a consistent time.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   *\n   * @deprecated This API is experimental: it may change or disappear.\n   */\n  async consistentQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const queryArgs = parseArgs(args[0]);\n\n    const timestampPromise = this.getTimestamp();\n    return await this.queryInner(query, queryArgs, { timestampPromise });\n  }\n\n  private async getTimestamp() {\n    if (this.encodedTsPromise) {\n      return this.encodedTsPromise;\n    }\n    return (this.encodedTsPromise = this.getTimestampInner());\n  }\n\n  private async getTimestampInner() {\n    const localFetch = specifiedFetch || fetch;\n\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    const response = await localFetch(`${this.address}/api/query_ts`, {\n      ...this.fetchOptions,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok) {\n      throw new Error(await response.text());\n    }\n    const { ts } = (await response.json()) as { ts: string };\n    return ts;\n  }\n\n  /**\n   * Execute a Convex query function.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   */\n  async query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const queryArgs = parseArgs(args[0]);\n    return await this.queryInner(query, queryArgs, {});\n  }\n\n  private async queryInner<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    queryArgs: FunctionArgs<Query>,\n    options: { timestampPromise?: Promise<string> },\n  ): Promise<FunctionReturnType<Query>> {\n    const name = getFunctionName(query);\n    const args = [convexToJson(queryArgs)];\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n\n    const timestamp = options.timestampPromise\n      ? await options.timestampPromise\n      : undefined;\n\n    const body = JSON.stringify({\n      path: name,\n      format: \"convex_encoded_json\",\n      args,\n      ...(timestamp ? { ts: timestamp } : {}),\n    });\n    const endpoint = timestamp\n      ? `${this.address}/api/query_at_ts`\n      : `${this.address}/api/query`;\n\n    const response = await localFetch(endpoint, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"query\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n\n  private async mutationInner<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    mutationArgs: FunctionArgs<Mutation>,\n  ): Promise<FunctionReturnType<Mutation>> {\n    const name = getFunctionName(mutation);\n    const body = JSON.stringify({\n      path: name,\n      format: \"convex_encoded_json\",\n      args: [convexToJson(mutationArgs)],\n    });\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n    const response = await localFetch(`${this.address}/api/mutation`, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"mutation\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n\n  private async processMutationQueue() {\n    if (this.isProcessingQueue) {\n      return;\n    }\n\n    this.isProcessingQueue = true;\n    while (this.mutationQueue.length > 0) {\n      const { mutation, args, resolve, reject } = this.mutationQueue.shift()!;\n      try {\n        const result = await this.mutationInner(mutation, args);\n        resolve(result);\n      } catch (error) {\n        reject(error);\n      }\n    }\n    this.isProcessingQueue = false;\n  }\n\n  private enqueueMutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    args: FunctionArgs<Mutation>,\n  ): Promise<FunctionReturnType<Mutation>> {\n    return new Promise((resolve, reject) => {\n      this.mutationQueue.push({ mutation, args, resolve, reject });\n      void this.processMutationQueue();\n    });\n  }\n\n  /**\n   * Execute a Convex mutation function. Mutations are queued by default.\n   *\n   * @param name - The name of the mutation.\n   * @param args - The arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - An optional object containing\n   * @returns A promise of the mutation's result.\n   */\n  async mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    ...args: ArgsAndOptions<Mutation, HttpMutationOptions>\n  ): Promise<FunctionReturnType<Mutation>> {\n    const [fnArgs, options] = args;\n    const mutationArgs = parseArgs(fnArgs);\n    const queued = !options?.skipQueue;\n\n    if (queued) {\n      return await this.enqueueMutation(mutation, mutationArgs);\n    } else {\n      return await this.mutationInner(mutation, mutationArgs);\n    }\n  }\n\n  /**\n   * Execute a Convex action function. Actions are not queued.\n   *\n   * @param name - The name of the action.\n   * @param args - The arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  async action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    ...args: OptionalRestArgs<Action>\n  ): Promise<FunctionReturnType<Action>> {\n    const actionArgs = parseArgs(args[0]);\n    const name = getFunctionName(action);\n    const body = JSON.stringify({\n      path: name,\n      format: \"convex_encoded_json\",\n      args: [convexToJson(actionArgs)],\n    });\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n    const response = await localFetch(`${this.address}/api/action`, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"action\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n\n  /**\n   * Execute a Convex function of an unknown type. These function calls are not queued.\n   *\n   * @param name - The name of the function.\n   * @param args - The arguments object for the function. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the function's result.\n   *\n   * @internal\n   */\n  async function<\n    AnyFunction extends FunctionReference<\"query\" | \"mutation\" | \"action\">,\n  >(\n    anyFunction: AnyFunction | string,\n    componentPath?: string,\n    ...args: OptionalRestArgs<AnyFunction>\n  ): Promise<FunctionReturnType<AnyFunction>> {\n    const functionArgs = parseArgs(args[0]);\n    const name =\n      typeof anyFunction === \"string\"\n        ? anyFunction\n        : getFunctionName(anyFunction);\n    const body = JSON.stringify({\n      componentPath: componentPath,\n      path: name,\n      format: \"convex_encoded_json\",\n      args: convexToJson(functionArgs),\n    });\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n    const response = await localFetch(`${this.address}/api/function`, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"any\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n}\n\nfunction forwardErrorData(errorData: JSONValue, error: ConvexError<string>) {\n  (error as ConvexError<any>).data = jsonToConvex(errorData);\n  return error;\n}\n\n/**\n * @internal\n */\ntype FetchOptions = { cache: \"force-cache\" | \"no-store\" };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAKO;AACP,oBAAiD;AACjD,eAAwB;AACxB,oBAKO;AACP,qBAKO;AAOA,MAAM,iBAAiB;AACvB,MAAM,0BAA0B;AAIhC,MAAM,yBAAyB;AAGtC,IAAI,iBAAsD;AACnD,SAAS,SAAS,GAA4B;AACnD,mBAAiB;AACnB;AAuBO,MAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkC5B,YACE,SACA,SAKA;AAxCF,wBAAiB;AACjB,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ,iBAKH,CAAC;AACN,wBAAQ,qBAA6B;AA4BnC,QAAI,OAAO,YAAY,WAAW;AAChC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,UAAM,OAAO,WAAW,CAAC;AACzB,QAAI,KAAK,iCAAiC,MAAM;AAC9C,+CAAsB,OAAO;AAAA,IAC/B;AACA,SAAK,SACH,SAAS,WAAW,YAChB,sCAAsB,EAAE,SAAS,MAAM,CAAC,IACxC,SAAS,WAAW,QAAQ,SAAS,SACnC,QAAQ,aACR,yCAAyB,EAAE,SAAS,MAAM,CAAC;AACnD,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI,SAAS,MAAM;AACjB,WAAK,QAAQ,QAAQ,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAqB;AACnB,WAAO,GAAG,KAAK,OAAO;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,OAAe;AACrB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,OAAe,kBAA2C;AACrE,SAAK,UAAU;AACf,QAAI,qBAAqB,QAAW;AAElC,YAAM,QAAQ,IAAI,YAAY,EAAE,OAAO,KAAK,UAAU,gBAAgB,CAAC;AACvE,YAAM,0BAA0B,KAAK,OAAO,cAAc,GAAG,KAAK,CAAC;AACnE,WAAK,YAAY,GAAG,KAAK,IAAI,uBAAuB;AAAA,IACtD,OAAO;AACL,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAgB;AACvB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,cAA4B;AAC1C,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,gBACJ,UACG,MACiC;AACpC,UAAM,gBAAY,yBAAU,KAAK,CAAC,CAAC;AAEnC,UAAM,mBAAmB,KAAK,aAAa;AAC3C,WAAO,MAAM,KAAK,WAAW,OAAO,WAAW,EAAE,iBAAiB,CAAC;AAAA,EACrE;AAAA,EAEA,MAAc,eAAe;AAC3B,QAAI,KAAK,kBAAkB;AACzB,aAAO,KAAK;AAAA,IACd;AACA,WAAQ,KAAK,mBAAmB,KAAK,kBAAkB;AAAA,EACzD;AAAA,EAEA,MAAc,oBAAoB;AAChC,UAAM,aAAa,kBAAkB;AAErC,UAAM,UAAkC;AAAA,MACtC,gBAAgB;AAAA,MAChB,iBAAiB,OAAO,gBAAO;AAAA,IACjC;AACA,UAAM,WAAW,MAAM,WAAW,GAAG,KAAK,OAAO,iBAAiB;AAAA,MAChE,GAAG,KAAK;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IACvC;AACA,UAAM,EAAE,GAAG,IAAK,MAAM,SAAS,KAAK;AACpC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MACJ,UACG,MACiC;AACpC,UAAM,gBAAY,yBAAU,KAAK,CAAC,CAAC;AACnC,WAAO,MAAM,KAAK,WAAW,OAAO,WAAW,CAAC,CAAC;AAAA,EACnD;AAAA,EAEA,MAAc,WACZ,OACA,WACA,SACoC;AACpC,UAAM,WAAO,4BAAgB,KAAK;AAClC,UAAM,OAAO,KAAC,4BAAa,SAAS,CAAC;AACrC,UAAM,UAAkC;AAAA,MACtC,gBAAgB;AAAA,MAChB,iBAAiB,OAAO,gBAAO;AAAA,IACjC;AACA,QAAI,KAAK,WAAW;AAClB,cAAQ,eAAe,IAAI,UAAU,KAAK,SAAS;AAAA,IACrD,WAAW,KAAK,MAAM;AACpB,cAAQ,eAAe,IAAI,UAAU,KAAK,IAAI;AAAA,IAChD;AACA,UAAM,aAAa,kBAAkB;AAErC,UAAM,YAAY,QAAQ,mBACtB,MAAM,QAAQ,mBACd;AAEJ,UAAM,OAAO,KAAK,UAAU;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR;AAAA,MACA,GAAI,YAAY,EAAE,IAAI,UAAU,IAAI,CAAC;AAAA,IACvC,CAAC;AACD,UAAM,WAAW,YACb,GAAG,KAAK,OAAO,qBACf,GAAG,KAAK,OAAO;AAEnB,UAAM,WAAW,MAAM,WAAW,UAAU;AAAA,MAC1C,GAAG,KAAK;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,CAAC,SAAS,MAAM,SAAS,WAAW,wBAAwB;AAC9D,YAAM,IAAI,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IACvC;AACA,UAAM,WAAW,MAAM,SAAS,KAAK;AAErC,QAAI,KAAK,OAAO;AACd,iBAAW,QAAQ,SAAS,YAAY,CAAC,GAAG;AAC1C,2CAAe,KAAK,QAAQ,QAAQ,SAAS,MAAM,IAAI;AAAA,MACzD;AAAA,IACF;AACA,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AACH,mBAAO,4BAAa,SAAS,KAAK;AAAA,MACpC,KAAK;AACH,YAAI,SAAS,cAAc,QAAW;AACpC,gBAAM;AAAA,YACJ,SAAS;AAAA,YACT,IAAI,0BAAY,SAAS,YAAY;AAAA,UACvC;AAAA,QACF;AACA,cAAM,IAAI,MAAM,SAAS,YAAY;AAAA,MACvC;AACE,cAAM,IAAI,MAAM,qBAAqB,KAAK,UAAU,QAAQ,CAAC,EAAE;AAAA,IACnE;AAAA,EACF;AAAA,EAEA,MAAc,cACZ,UACA,cACuC;AACvC,UAAM,WAAO,4BAAgB,QAAQ;AACrC,UAAM,OAAO,KAAK,UAAU;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM,KAAC,4BAAa,YAAY,CAAC;AAAA,IACnC,CAAC;AACD,UAAM,UAAkC;AAAA,MACtC,gBAAgB;AAAA,MAChB,iBAAiB,OAAO,gBAAO;AAAA,IACjC;AACA,QAAI,KAAK,WAAW;AAClB,cAAQ,eAAe,IAAI,UAAU,KAAK,SAAS;AAAA,IACrD,WAAW,KAAK,MAAM;AACpB,cAAQ,eAAe,IAAI,UAAU,KAAK,IAAI;AAAA,IAChD;AACA,UAAM,aAAa,kBAAkB;AACrC,UAAM,WAAW,MAAM,WAAW,GAAG,KAAK,OAAO,iBAAiB;AAAA,MAChE,GAAG,KAAK;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,CAAC,SAAS,MAAM,SAAS,WAAW,wBAAwB;AAC9D,YAAM,IAAI,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IACvC;AACA,UAAM,WAAW,MAAM,SAAS,KAAK;AACrC,QAAI,KAAK,OAAO;AACd,iBAAW,QAAQ,SAAS,YAAY,CAAC,GAAG;AAC1C,2CAAe,KAAK,QAAQ,QAAQ,YAAY,MAAM,IAAI;AAAA,MAC5D;AAAA,IACF;AACA,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AACH,mBAAO,4BAAa,SAAS,KAAK;AAAA,MACpC,KAAK;AACH,YAAI,SAAS,cAAc,QAAW;AACpC,gBAAM;AAAA,YACJ,SAAS;AAAA,YACT,IAAI,0BAAY,SAAS,YAAY;AAAA,UACvC;AAAA,QACF;AACA,cAAM,IAAI,MAAM,SAAS,YAAY;AAAA,MACvC;AACE,cAAM,IAAI,MAAM,qBAAqB,KAAK,UAAU,QAAQ,CAAC,EAAE;AAAA,IACnE;AAAA,EACF;AAAA,EAEA,MAAc,uBAAuB;AACnC,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AAEA,SAAK,oBAAoB;AACzB,WAAO,KAAK,cAAc,SAAS,GAAG;AACpC,YAAM,EAAE,UAAU,MAAM,SAAS,OAAO,IAAI,KAAK,cAAc,MAAM;AACrE,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,cAAc,UAAU,IAAI;AACtD,gBAAQ,MAAM;AAAA,MAChB,SAAS,OAAO;AACd,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EAEQ,gBACN,UACA,MACuC;AACvC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,cAAc,KAAK,EAAE,UAAU,MAAM,SAAS,OAAO,CAAC;AAC3D,WAAK,KAAK,qBAAqB;AAAA,IACjC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SACJ,aACG,MACoC;AACvC,UAAM,CAAC,QAAQ,OAAO,IAAI;AAC1B,UAAM,mBAAe,yBAAU,MAAM;AACrC,UAAM,SAAS,CAAC,SAAS;AAEzB,QAAI,QAAQ;AACV,aAAO,MAAM,KAAK,gBAAgB,UAAU,YAAY;AAAA,IAC1D,OAAO;AACL,aAAO,MAAM,KAAK,cAAc,UAAU,YAAY;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OACJ,WACG,MACkC;AACrC,UAAM,iBAAa,yBAAU,KAAK,CAAC,CAAC;AACpC,UAAM,WAAO,4BAAgB,MAAM;AACnC,UAAM,OAAO,KAAK,UAAU;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM,KAAC,4BAAa,UAAU,CAAC;AAAA,IACjC,CAAC;AACD,UAAM,UAAkC;AAAA,MACtC,gBAAgB;AAAA,MAChB,iBAAiB,OAAO,gBAAO;AAAA,IACjC;AACA,QAAI,KAAK,WAAW;AAClB,cAAQ,eAAe,IAAI,UAAU,KAAK,SAAS;AAAA,IACrD,WAAW,KAAK,MAAM;AACpB,cAAQ,eAAe,IAAI,UAAU,KAAK,IAAI;AAAA,IAChD;AACA,UAAM,aAAa,kBAAkB;AACrC,UAAM,WAAW,MAAM,WAAW,GAAG,KAAK,OAAO,eAAe;AAAA,MAC9D,GAAG,KAAK;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,CAAC,SAAS,MAAM,SAAS,WAAW,wBAAwB;AAC9D,YAAM,IAAI,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IACvC;AACA,UAAM,WAAW,MAAM,SAAS,KAAK;AACrC,QAAI,KAAK,OAAO;AACd,iBAAW,QAAQ,SAAS,YAAY,CAAC,GAAG;AAC1C,2CAAe,KAAK,QAAQ,QAAQ,UAAU,MAAM,IAAI;AAAA,MAC1D;AAAA,IACF;AACA,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AACH,mBAAO,4BAAa,SAAS,KAAK;AAAA,MACpC,KAAK;AACH,YAAI,SAAS,cAAc,QAAW;AACpC,gBAAM;AAAA,YACJ,SAAS;AAAA,YACT,IAAI,0BAAY,SAAS,YAAY;AAAA,UACvC;AAAA,QACF;AACA,cAAM,IAAI,MAAM,SAAS,YAAY;AAAA,MACvC;AACE,cAAM,IAAI,MAAM,qBAAqB,KAAK,UAAU,QAAQ,CAAC,EAAE;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAGJ,aACA,kBACG,MACuC;AAC1C,UAAM,mBAAe,yBAAU,KAAK,CAAC,CAAC;AACtC,UAAM,OACJ,OAAO,gBAAgB,WACnB,kBACA,4BAAgB,WAAW;AACjC,UAAM,OAAO,KAAK,UAAU;AAAA,MAC1B;AAAA,MACA,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAM,4BAAa,YAAY;AAAA,IACjC,CAAC;AACD,UAAM,UAAkC;AAAA,MACtC,gBAAgB;AAAA,MAChB,iBAAiB,OAAO,gBAAO;AAAA,IACjC;AACA,QAAI,KAAK,WAAW;AAClB,cAAQ,eAAe,IAAI,UAAU,KAAK,SAAS;AAAA,IACrD,WAAW,KAAK,MAAM;AACpB,cAAQ,eAAe,IAAI,UAAU,KAAK,IAAI;AAAA,IAChD;AACA,UAAM,aAAa,kBAAkB;AACrC,UAAM,WAAW,MAAM,WAAW,GAAG,KAAK,OAAO,iBAAiB;AAAA,MAChE,GAAG,KAAK;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,CAAC,SAAS,MAAM,SAAS,WAAW,wBAAwB;AAC9D,YAAM,IAAI,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IACvC;AACA,UAAM,WAAW,MAAM,SAAS,KAAK;AACrC,QAAI,KAAK,OAAO;AACd,iBAAW,QAAQ,SAAS,YAAY,CAAC,GAAG;AAC1C,2CAAe,KAAK,QAAQ,QAAQ,OAAO,MAAM,IAAI;AAAA,MACvD;AAAA,IACF;AACA,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AACH,mBAAO,4BAAa,SAAS,KAAK;AAAA,MACpC,KAAK;AACH,YAAI,SAAS,cAAc,QAAW;AACpC,gBAAM;AAAA,YACJ,SAAS;AAAA,YACT,IAAI,0BAAY,SAAS,YAAY;AAAA,UACvC;AAAA,QACF;AACA,cAAM,IAAI,MAAM,SAAS,YAAY;AAAA,MACvC;AACE,cAAM,IAAI,MAAM,qBAAqB,KAAK,UAAU,QAAQ,CAAC,EAAE;AAAA,IACnE;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,WAAsB,OAA4B;AAC1E,EAAC,MAA2B,WAAO,4BAAa,SAAS;AACzD,SAAO;AACT;", "names": []}