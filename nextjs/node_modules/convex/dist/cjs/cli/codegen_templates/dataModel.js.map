{"version": 3, "sources": ["../../../../src/cli/codegen_templates/dataModel.ts"], "sourcesContent": ["import { Context } from \"../../bundler/context.js\";\nimport { SystemIndexes } from \"../../server/system_fields.js\";\nimport {\n  ComponentDirectory,\n  toComponentDefinitionPath,\n} from \"../lib/components/definition/directoryStructure.js\";\nimport {\n  AnalyzedSchema,\n  TableDefinition,\n} from \"../lib/deployApi/componentDefinition.js\";\nimport { StartPushResponse } from \"../lib/deployApi/startPush.js\";\nimport { ConvexValidator } from \"../lib/deployApi/validator.js\";\nimport { header } from \"./common.js\";\nimport { validatorToType } from \"./validator_helpers.js\";\n\nexport function noSchemaDataModelDTS() {\n  return `\n  ${header(\"Generated data model types.\")}\n  import { AnyDataModel } from \"convex/server\";\n  import type { GenericId } from \"convex/values\";\n\n  /**\n   * No \\`schema.ts\\` file found!\n   *\n   * This generated code has permissive types like \\`Doc = any\\` because\n   * Convex doesn't know your schema. If you'd like more type safety, see\n   * https://docs.convex.dev/using/schemas for instructions on how to add a\n   * schema file.\n   *\n   * After you change a schema, rerun codegen with \\`npx convex dev\\`.\n   */\n\n  /**\n   * The names of all of your Convex tables.\n   */\n  export type TableNames = string;\n\n  /**\n   * The type of a document stored in Convex.\n   */\n  export type Doc = any;\n\n  /**\n   * An identifier for a document in Convex.\n   *\n   * Convex documents are uniquely identified by their \\`Id\\`, which is accessible\n   * on the \\`_id\\` field. To learn more, see [Document IDs](https://docs.convex.dev/using/document-ids).\n   *\n   * Documents can be loaded using \\`db.get(id)\\` in query and mutation functions.\n   *\n   * IDs are just strings at runtime, but this type can be used to distinguish them from other\n   * strings when type checking.\n   */\n  export type Id<TableName extends TableNames = TableNames> = GenericId<TableName>;\n\n  /**\n   * A type describing your Convex data model.\n   *\n   * This type includes information about what tables you have, the type of\n   * documents stored in those tables, and the indexes defined on them.\n   *\n   * This type is used to parameterize methods like \\`queryGeneric\\` and\n   * \\`mutationGeneric\\` to make them type-safe.\n   */\n  export type DataModel = AnyDataModel;`;\n}\n\nexport function dynamicDataModelDTS() {\n  return `\n  ${header(\"Generated data model types.\")}\n  import type { DataModelFromSchemaDefinition, DocumentByName, TableNamesInDataModel, SystemTableNames } from \"convex/server\";\n  import type { GenericId } from \"convex/values\";\n  import schema from \"../schema.js\";\n\n  /**\n   * The names of all of your Convex tables.\n   */\n  export type TableNames = TableNamesInDataModel<DataModel>;\n\n  /**\n   * The type of a document stored in Convex.\n   *\n   * @typeParam TableName - A string literal type of the table name (like \"users\").\n   */\n  export type Doc<TableName extends TableNames> = DocumentByName<DataModel, TableName>;\n\n  /**\n   * An identifier for a document in Convex.\n   *\n   * Convex documents are uniquely identified by their \\`Id\\`, which is accessible\n   * on the \\`_id\\` field. To learn more, see [Document IDs](https://docs.convex.dev/using/document-ids).\n   *\n   * Documents can be loaded using \\`db.get(id)\\` in query and mutation functions.\n   *\n   * IDs are just strings at runtime, but this type can be used to distinguish them from other\n   * strings when type checking.\n   *\n   * @typeParam TableName - A string literal type of the table name (like \"users\").\n   */\n  export type Id<TableName extends TableNames | SystemTableNames> = GenericId<TableName>;\n\n  /**\n   * A type describing your Convex data model.\n   *\n   * This type includes information about what tables you have, the type of\n   * documents stored in those tables, and the indexes defined on them.\n   *\n   * This type is used to parameterize methods like \\`queryGeneric\\` and\n   * \\`mutationGeneric\\` to make them type-safe.\n   */\n  export type DataModel = DataModelFromSchemaDefinition<typeof schema>;\n  `;\n}\n\nexport async function staticDataModelDTS(\n  ctx: Context,\n  startPush: StartPushResponse,\n  rootComponent: ComponentDirectory,\n  componentDirectory: ComponentDirectory,\n) {\n  const definitionPath = toComponentDefinitionPath(\n    rootComponent,\n    componentDirectory,\n  );\n\n  const analysis = startPush.analysis[definitionPath];\n  if (!analysis) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `No analysis found for component ${definitionPath} orig: ${definitionPath}\\nin\\n${Object.keys(startPush.analysis).toString()}`,\n    });\n  }\n  if (!analysis.schema) {\n    return noSchemaDataModelDTS();\n  }\n\n  const lines = [\n    header(\"Generated data model types.\"),\n    `import type { DocumentByName, TableNamesInDataModel, SystemTableNames, AnyDataModel } from \"convex/server\";`,\n    `import type { GenericId } from \"convex/values\";`,\n  ];\n  for await (const line of codegenDataModel(ctx, analysis.schema)) {\n    lines.push(line);\n  }\n  lines.push(`\n    /**\n     * The names of all of your Convex tables.\n     */\n    export type TableNames = TableNamesInDataModel<DataModel>;\n\n    /**\n     * The type of a document stored in Convex.\n     *\n     * @typeParam TableName - A string literal type of the table name (like \"users\").\n     */\n    export type Doc<TableName extends TableNames> = DocumentByName<DataModel, TableName>;\n\n    /**\n     * An identifier for a document in Convex.\n     *\n     * Convex documents are uniquely identified by their \\`Id\\`, which is accessible\n     * on the \\`_id\\` field. To learn more, see [Document IDs](https://docs.convex.dev/using/document-ids).\n     *\n     * Documents can be loaded using \\`db.get(id)\\` in query and mutation functions.\n     *\n     * IDs are just strings at runtime, but this type can be used to distinguish them from other\n     * strings when type checking.\n     *\n     * @typeParam TableName - A string literal type of the table name (like \"users\").\n     */\n    export type Id<TableName extends TableNames | SystemTableNames> = GenericId<TableName>;\n    `);\n\n  return lines.join(\"\\n\");\n}\n\nasync function* codegenDataModel(ctx: Context, schema: AnalyzedSchema) {\n  yield `\n    /**\n     * A type describing your Convex data model.\n     *\n     * This type includes information about what tables you have, the type of\n     * documents stored in those tables, and the indexes defined on them.\n     *\n     * This type is used to parameterize methods like \\`queryGeneric\\` and\n     * \\`mutationGeneric\\` to make them type-safe.\n     */\n  `;\n  const tables = [...schema.tables];\n  tables.sort((a, b) => a.tableName.localeCompare(b.tableName));\n\n  yield `export type DataModel = {`;\n  for (const table of tables) {\n    yield `  ${table.tableName}:`;\n    yield* codegenTable(ctx, table);\n    yield `,`;\n  }\n  yield `}`;\n  if (!schema.schemaValidation) {\n    yield ` & AnyDataModel`;\n  }\n  yield `;`;\n}\n\nasync function* codegenTable(ctx: Context, table: TableDefinition) {\n  const documentType = await addSystemFields(\n    ctx,\n    table.tableName,\n    table.documentType,\n  );\n\n  const indexJson: Record<string, string[]> = {};\n  for (const index of table.indexes) {\n    indexJson[index.indexDescriptor] = index.fields;\n  }\n\n  yield `{`;\n  yield `  document: ${validatorToType(documentType, true)},`;\n\n  const fieldPaths = new Set<string>();\n  for (const fieldPath of extractFieldPaths(documentType)) {\n    fieldPaths.add(fieldPath.join(\".\"));\n  }\n  yield `  fieldPaths: ${stringLiteralUnionType(Array.from(fieldPaths).sort())},`;\n\n  yield `  indexes: {`;\n  const systemIndexes: SystemIndexes = {\n    by_id: [\"_id\"],\n    by_creation_time: [\"_creationTime\"],\n  };\n  const indexes: Record<string, string[]> = {};\n  for (const [indexDescriptor, fields] of Object.entries(systemIndexes)) {\n    indexes[indexDescriptor] = fields;\n  }\n  for (const index of table.indexes) {\n    if (indexes[index.indexDescriptor]) {\n      yield await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Duplicate index name ${index.indexDescriptor} in table ${table.tableName}.`,\n      });\n    }\n    indexes[index.indexDescriptor] = index.fields;\n  }\n  for (const [indexDescriptor, fields] of Object.entries(indexes)) {\n    yield `    \"${indexDescriptor}\": ${JSON.stringify(fields)},`;\n  }\n  yield `  },`;\n\n  yield `  searchIndexes: {`;\n  for (const index of table.searchIndexes ?? []) {\n    yield `    \"${index.indexDescriptor}\": {`;\n    yield `      searchField: \"${index.searchField}\",`;\n    yield `      filterFields: ${stringLiteralUnionType(index.filterFields)},`;\n    yield `    },`;\n  }\n  yield `  },`;\n\n  yield `  vectorIndexes: {`;\n  for (const index of table.vectorIndexes ?? []) {\n    yield `    \"${index.indexDescriptor}\": {`;\n    yield `      vectorField: \"${index.vectorField}\",`;\n    yield `      dimensions: ${index.dimensions},`;\n    yield `      filterFields: ${stringLiteralUnionType(index.filterFields)},`;\n    yield `    },`;\n  }\n  yield `  },`;\n  yield `}`;\n}\n\nconst SYSTEM_FIELDS = [\"_id\", \"_creationTime\"];\n\nasync function addSystemFields(\n  ctx: Context,\n  tableName: string,\n  validator: ConvexValidator,\n): Promise<ConvexValidator> {\n  if (validator.type === \"object\") {\n    return addSystemFieldsToObject(ctx, tableName, validator);\n  } else if (validator.type === \"any\") {\n    return { type: \"any\" };\n  } else if (validator.type === \"union\") {\n    const newSubValidators = [];\n    for (const subValidator of validator.value) {\n      const newSubValidator = await addSystemFieldsToObject(\n        ctx,\n        tableName,\n        subValidator,\n      );\n      newSubValidators.push(newSubValidator);\n    }\n    return { type: \"union\", value: newSubValidators };\n  } else {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Invalid top-level validator for ${tableName}.`,\n    });\n  }\n}\n\nasync function addSystemFieldsToObject(\n  ctx: Context,\n  tableName: string,\n  validator: ConvexValidator,\n): Promise<ConvexValidator> {\n  if (validator.type !== \"object\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `System fields can only be added to objects.`,\n    });\n  }\n  for (const systemField of SYSTEM_FIELDS) {\n    if (Object.hasOwn(validator.value, systemField)) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `System field ${systemField} present in table ${tableName}.`,\n      });\n    }\n  }\n  return {\n    type: \"object\",\n    value: {\n      ...validator.value,\n      _id: {\n        fieldType: { type: \"id\", tableName },\n        optional: false,\n      },\n      _creationTime: {\n        fieldType: { type: \"number\" },\n        optional: false,\n      },\n    },\n  };\n}\n\nfunction* extractFieldPaths(validator: ConvexValidator): Generator<string[]> {\n  if (validator.type === \"object\") {\n    for (const [fieldName, fieldValidator] of Object.entries(validator.value)) {\n      for (const subFieldPath of extractFieldPaths(fieldValidator.fieldType)) {\n        yield [fieldName, ...subFieldPath];\n      }\n    }\n  } else if (validator.type === \"union\") {\n    for (const subValidator of validator.value) {\n      yield* extractFieldPaths(subValidator);\n    }\n  } else {\n    yield [];\n  }\n}\n\nfunction stringLiteralUnionType(fields: string[]) {\n  if (fields.length === 0) {\n    return \"never\";\n  } else if (fields.length === 1) {\n    return `\"${fields[0]}\"`;\n  } else {\n    return fields.map((field) => `\"${field}\"`).join(\" | \");\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gCAGO;AAOP,oBAAuB;AACvB,+BAAgC;AAEzB,SAAS,uBAAuB;AACrC,SAAO;AAAA,QACL,sBAAO,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgDzC;AAEO,SAAS,sBAAsB;AACpC,SAAO;AAAA,QACL,sBAAO,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2CzC;AAEA,eAAsB,mBACpB,KACA,WACA,eACA,oBACA;AACA,QAAM,qBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAW,UAAU,SAAS,cAAc;AAClD,MAAI,CAAC,UAAU;AACb,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mCAAmC,cAAc,UAAU,cAAc;AAAA;AAAA,EAAS,OAAO,KAAK,UAAU,QAAQ,EAAE,SAAS,CAAC;AAAA,IAC9I,CAAC;AAAA,EACH;AACA,MAAI,CAAC,SAAS,QAAQ;AACpB,WAAO,qBAAqB;AAAA,EAC9B;AAEA,QAAM,QAAQ;AAAA,QACZ,sBAAO,6BAA6B;AAAA,IACpC;AAAA,IACA;AAAA,EACF;AACA,mBAAiB,QAAQ,iBAAiB,KAAK,SAAS,MAAM,GAAG;AAC/D,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,QAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KA2BR;AAEH,SAAO,MAAM,KAAK,IAAI;AACxB;AAEA,gBAAgB,iBAAiB,KAAc,QAAwB;AACrE,QAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWN,QAAM,SAAS,CAAC,GAAG,OAAO,MAAM;AAChC,SAAO,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,cAAc,EAAE,SAAS,CAAC;AAE5D,QAAM;AACN,aAAW,SAAS,QAAQ;AAC1B,UAAM,KAAK,MAAM,SAAS;AAC1B,WAAO,aAAa,KAAK,KAAK;AAC9B,UAAM;AAAA,EACR;AACA,QAAM;AACN,MAAI,CAAC,OAAO,kBAAkB;AAC5B,UAAM;AAAA,EACR;AACA,QAAM;AACR;AAEA,gBAAgB,aAAa,KAAc,OAAwB;AACjE,QAAM,eAAe,MAAM;AAAA,IACzB;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAEA,QAAM,YAAsC,CAAC;AAC7C,aAAW,SAAS,MAAM,SAAS;AACjC,cAAU,MAAM,eAAe,IAAI,MAAM;AAAA,EAC3C;AAEA,QAAM;AACN,QAAM,mBAAe,0CAAgB,cAAc,IAAI,CAAC;AAExD,QAAM,aAAa,oBAAI,IAAY;AACnC,aAAW,aAAa,kBAAkB,YAAY,GAAG;AACvD,eAAW,IAAI,UAAU,KAAK,GAAG,CAAC;AAAA,EACpC;AACA,QAAM,iBAAiB,uBAAuB,MAAM,KAAK,UAAU,EAAE,KAAK,CAAC,CAAC;AAE5E,QAAM;AACN,QAAM,gBAA+B;AAAA,IACnC,OAAO,CAAC,KAAK;AAAA,IACb,kBAAkB,CAAC,eAAe;AAAA,EACpC;AACA,QAAM,UAAoC,CAAC;AAC3C,aAAW,CAAC,iBAAiB,MAAM,KAAK,OAAO,QAAQ,aAAa,GAAG;AACrE,YAAQ,eAAe,IAAI;AAAA,EAC7B;AACA,aAAW,SAAS,MAAM,SAAS;AACjC,QAAI,QAAQ,MAAM,eAAe,GAAG;AAClC,YAAM,MAAM,IAAI,MAAM;AAAA,QACpB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,wBAAwB,MAAM,eAAe,aAAa,MAAM,SAAS;AAAA,MAC3F,CAAC;AAAA,IACH;AACA,YAAQ,MAAM,eAAe,IAAI,MAAM;AAAA,EACzC;AACA,aAAW,CAAC,iBAAiB,MAAM,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC/D,UAAM,QAAQ,eAAe,MAAM,KAAK,UAAU,MAAM,CAAC;AAAA,EAC3D;AACA,QAAM;AAEN,QAAM;AACN,aAAW,SAAS,MAAM,iBAAiB,CAAC,GAAG;AAC7C,UAAM,QAAQ,MAAM,eAAe;AACnC,UAAM,uBAAuB,MAAM,WAAW;AAC9C,UAAM,uBAAuB,uBAAuB,MAAM,YAAY,CAAC;AACvE,UAAM;AAAA,EACR;AACA,QAAM;AAEN,QAAM;AACN,aAAW,SAAS,MAAM,iBAAiB,CAAC,GAAG;AAC7C,UAAM,QAAQ,MAAM,eAAe;AACnC,UAAM,uBAAuB,MAAM,WAAW;AAC9C,UAAM,qBAAqB,MAAM,UAAU;AAC3C,UAAM,uBAAuB,uBAAuB,MAAM,YAAY,CAAC;AACvE,UAAM;AAAA,EACR;AACA,QAAM;AACN,QAAM;AACR;AAEA,MAAM,gBAAgB,CAAC,OAAO,eAAe;AAE7C,eAAe,gBACb,KACA,WACA,WAC0B;AAC1B,MAAI,UAAU,SAAS,UAAU;AAC/B,WAAO,wBAAwB,KAAK,WAAW,SAAS;AAAA,EAC1D,WAAW,UAAU,SAAS,OAAO;AACnC,WAAO,EAAE,MAAM,MAAM;AAAA,EACvB,WAAW,UAAU,SAAS,SAAS;AACrC,UAAM,mBAAmB,CAAC;AAC1B,eAAW,gBAAgB,UAAU,OAAO;AAC1C,YAAM,kBAAkB,MAAM;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,uBAAiB,KAAK,eAAe;AAAA,IACvC;AACA,WAAO,EAAE,MAAM,SAAS,OAAO,iBAAiB;AAAA,EAClD,OAAO;AACL,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mCAAmC,SAAS;AAAA,IAC9D,CAAC;AAAA,EACH;AACF;AAEA,eAAe,wBACb,KACA,WACA,WAC0B;AAC1B,MAAI,UAAU,SAAS,UAAU;AAC/B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,aAAW,eAAe,eAAe;AACvC,QAAI,OAAO,OAAO,UAAU,OAAO,WAAW,GAAG;AAC/C,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,gBAAgB,WAAW,qBAAqB,SAAS;AAAA,MAC3E,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,MACL,GAAG,UAAU;AAAA,MACb,KAAK;AAAA,QACH,WAAW,EAAE,MAAM,MAAM,UAAU;AAAA,QACnC,UAAU;AAAA,MACZ;AAAA,MACA,eAAe;AAAA,QACb,WAAW,EAAE,MAAM,SAAS;AAAA,QAC5B,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,UAAU,kBAAkB,WAAiD;AAC3E,MAAI,UAAU,SAAS,UAAU;AAC/B,eAAW,CAAC,WAAW,cAAc,KAAK,OAAO,QAAQ,UAAU,KAAK,GAAG;AACzE,iBAAW,gBAAgB,kBAAkB,eAAe,SAAS,GAAG;AACtE,cAAM,CAAC,WAAW,GAAG,YAAY;AAAA,MACnC;AAAA,IACF;AAAA,EACF,WAAW,UAAU,SAAS,SAAS;AACrC,eAAW,gBAAgB,UAAU,OAAO;AAC1C,aAAO,kBAAkB,YAAY;AAAA,IACvC;AAAA,EACF,OAAO;AACL,UAAM,CAAC;AAAA,EACT;AACF;AAEA,SAAS,uBAAuB,QAAkB;AAChD,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT,WAAW,OAAO,WAAW,GAAG;AAC9B,WAAO,IAAI,OAAO,CAAC,CAAC;AAAA,EACtB,OAAO;AACL,WAAO,OAAO,IAAI,CAAC,UAAU,IAAI,KAAK,GAAG,EAAE,KAAK,KAAK;AAAA,EACvD;AACF;", "names": []}