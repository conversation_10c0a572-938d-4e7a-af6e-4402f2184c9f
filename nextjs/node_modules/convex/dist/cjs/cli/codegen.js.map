{"version": 3, "sources": ["../../../src/cli/codegen.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport { runCodegen } from \"./lib/components.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nexport const codegen = new Command(\"codegen\")\n  .summary(\"Generate backend type definitions\")\n  .description(\n    \"Generate types in `convex/_generated/` based on the current contents of `convex/`.\",\n  )\n  .allowExcessArguments(false)\n  .option(\n    \"--dry-run\",\n    \"Print out the generated configuration to stdout instead of writing to convex directory\",\n  )\n  .addOption(new Option(\"--debug\").hideHelp())\n  .addOption(\n    new Option(\n      \"--typecheck <mode>\",\n      `Whether to check TypeScript files with \\`tsc --noEmit\\`.`,\n    )\n      .choices([\"enable\", \"try\", \"disable\"] as const)\n      .default(\"try\" as const),\n  )\n  .option(\n    \"--init\",\n    \"Also (over-)write the default convex/README.md and convex/tsconfig.json files, otherwise only written when creating a new Convex project.\",\n  )\n  .addOption(new Option(\"--admin-key <adminKey>\").hideHelp())\n  .addOption(new Option(\"--url <url>\").hideHelp())\n  .addOption(new Option(\"--live-component-sources\").hideHelp())\n  // Experimental option\n  .addOption(\n    new Option(\n      \"--commonjs\",\n      \"Generate CommonJS modules (CJS) instead of ECMAScript modules, the default. Bundlers typically take care of this conversion while bundling, so this setting is generally only useful for projects which do not use a bundler, typically Node.js projects. Convex functions can be written with either syntax.\",\n    ).hideHelp(),\n  )\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n\n    await runCodegen(ctx, deploymentSelection, {\n      dryRun: !!options.dryRun,\n      debug: !!options.debug,\n      typecheck: options.typecheck,\n      init: !!options.init,\n      commonjs: !!options.commonjs,\n      url: options.url,\n      adminKey: options.adminKey,\n      liveComponentSources: !!options.liveComponentSources,\n      debugNodeApis: false,\n    });\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAgC;AAChC,qBAA8B;AAC9B,wBAA2B;AAC3B,iCAAuC;AAChC,MAAM,UAAU,IAAI,6BAAQ,SAAS,EACzC,QAAQ,mCAAmC,EAC3C;AAAA,EACC;AACF,EACC,qBAAqB,KAAK,EAC1B;AAAA,EACC;AAAA,EACA;AACF,EACC,UAAU,IAAI,4BAAO,SAAS,EAAE,SAAS,CAAC,EAC1C;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,UAAU,OAAO,SAAS,CAAU,EAC7C,QAAQ,KAAc;AAC3B,EACC;AAAA,EACC;AAAA,EACA;AACF,EACC,UAAU,IAAI,4BAAO,wBAAwB,EAAE,SAAS,CAAC,EACzD,UAAU,IAAI,4BAAO,aAAa,EAAE,SAAS,CAAC,EAC9C,UAAU,IAAI,4BAAO,0BAA0B,EAAE,SAAS,CAAC,EAE3D;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EAAE,SAAS;AACb,EACC,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,UAAM,8BAAc,OAAO;AACvC,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,OAAO;AAErE,YAAM,8BAAW,KAAK,qBAAqB;AAAA,IACzC,QAAQ,CAAC,CAAC,QAAQ;AAAA,IAClB,OAAO,CAAC,CAAC,QAAQ;AAAA,IACjB,WAAW,QAAQ;AAAA,IACnB,MAAM,CAAC,CAAC,QAAQ;AAAA,IAChB,UAAU,CAAC,CAAC,QAAQ;AAAA,IACpB,KAAK,QAAQ;AAAA,IACb,UAAU,QAAQ;AAAA,IAClB,sBAAsB,CAAC,CAAC,QAAQ;AAAA,IAChC,eAAe;AAAA,EACjB,CAAC;AACH,CAAC;", "names": []}