{"version": 3, "sources": ["../../../../src/cli/lib/typecheck.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport path from \"path\";\nimport { Context } from \"../../bundler/context.js\";\nimport { logError, logFailure, showSpinner } from \"../../bundler/log.js\";\nimport * as Sentry from \"@sentry/node\";\nimport * as semver from \"semver\";\nimport { spawnAsync } from \"./utils/utils.js\";\n\nexport type TypecheckResult = \"cantTypeCheck\" | \"success\" | \"typecheckFailed\";\n\nexport type TypeCheckMode = \"enable\" | \"try\" | \"disable\";\n\ntype TypecheckResultHandler = (\n  result: TypecheckResult,\n  logSpecificError?: () => void,\n  // If given, we run it to print out errors.\n  // We expect it to throw or resolve to \"success\"\n  // if a concurrent change invalidated the error result.\n  runOnError?: () => Promise<\"success\">,\n) => Promise<void>;\n\n/**\n * Conditionally run a typecheck function and interpret the result.\n *\n * If typeCheckMode === \"disable\", never run the typecheck function.\n * If typeCheckMode === \"enable\", run the typecheck and crash if typechecking\n * fails or we can't find tsc.\n * If typeCheckMode === \"try\", try and run the typecheck. crash if typechecking\n * fails but don't worry if tsc is missing and we can't run it.\n */\nexport async function typeCheckFunctionsInMode(\n  ctx: Context,\n  typeCheckMode: TypeCheckMode,\n  functionsDir: string,\n): Promise<void> {\n  if (typeCheckMode === \"disable\") {\n    return;\n  }\n  await typeCheckFunctions(\n    ctx,\n    functionsDir,\n    async (result, logSpecificError, runOnError) => {\n      if (\n        (result === \"cantTypeCheck\" && typeCheckMode === \"enable\") ||\n        result === \"typecheckFailed\"\n      ) {\n        logSpecificError?.();\n        logError(\n          chalk.gray(\"To ignore failing typecheck, use `--typecheck=disable`.\"),\n        );\n        try {\n          const result = await runOnError?.();\n          // Concurrent change invalidated the error, don't fail\n          if (result === \"success\") {\n            return;\n          }\n        } catch {\n          // As expected, `runOnError` threw\n        }\n        await ctx.crash({\n          exitCode: 1,\n          errorType: \"invalid filesystem data\",\n          printedMessage: null,\n        });\n      }\n    },\n  );\n}\n\n// Runs TypeScript compiler to typecheck Convex query and mutation functions.\nexport async function typeCheckFunctions(\n  ctx: Context,\n  functionsDir: string,\n  handleResult: TypecheckResultHandler,\n): Promise<void> {\n  const tsconfig = path.join(functionsDir, \"tsconfig.json\");\n  if (!ctx.fs.exists(tsconfig)) {\n    return handleResult(\"cantTypeCheck\", () => {\n      logError(\n        \"Found no convex/tsconfig.json to use to typecheck Convex functions, so skipping typecheck.\",\n      );\n      logError(\"Run `npx convex codegen --init` to create one.\");\n    });\n  }\n  await runTsc(ctx, [\"--project\", functionsDir], handleResult);\n}\n\nasync function runTsc(\n  ctx: Context,\n  tscArgs: string[],\n  handleResult: TypecheckResultHandler,\n): Promise<void> {\n  // Check if tsc is even installed\n  const tscPath = path.join(\"node_modules\", \"typescript\", \"bin\", \"tsc\");\n  if (!ctx.fs.exists(tscPath)) {\n    return handleResult(\"cantTypeCheck\", () => {\n      logError(\n        chalk.gray(\"No TypeScript binary found, so skipping typecheck.\"),\n      );\n    });\n  }\n\n  // Check the TypeScript version matches the recommendation from Convex\n  const versionResult = await spawnAsync(ctx, process.execPath, [\n    tscPath,\n    \"--version\",\n  ]);\n\n  const version = versionResult.stdout.match(/Version (.*)/)?.[1] ?? null;\n  const hasOlderTypeScriptVersion = version && semver.lt(version, \"4.8.4\");\n\n  await runTscInner(ctx, tscPath, tscArgs, handleResult);\n\n  // Print this warning after any logs from running `tsc`\n  if (hasOlderTypeScriptVersion) {\n    logError(\n      chalk.yellow(\n        \"Convex works best with TypeScript version 4.8.4 or newer -- npm i --save-dev typescript@latest to update.\",\n      ),\n    );\n  }\n}\n\nasync function runTscInner(\n  ctx: Context,\n  tscPath: string,\n  tscArgs: string[],\n  handleResult: TypecheckResultHandler,\n) {\n  // Run `tsc` once and have it print out the files it touched. This output won't\n  // be very useful if there's an error, but we'll run it again to get a nice\n  // user-facing error in this exceptional case.\n  // The `--listFiles` command prints out files touched on success or error.\n  const result = await spawnAsync(ctx, process.execPath, [\n    tscPath,\n    ...tscArgs,\n    \"--listFiles\",\n  ]);\n  if (result.status === null) {\n    return handleResult(\"typecheckFailed\", () => {\n      logFailure(`TypeScript typecheck timed out.`);\n      if (result.error) {\n        logError(chalk.red(`${result.error.toString()}`));\n      }\n    });\n  }\n  // Okay, we may have failed `tsc` but at least it returned. Try to parse its\n  // output to discover which files it touched.\n  const filesTouched = result.stdout\n    .split(\"\\n\")\n    .map((s) => s.trim())\n    .filter((s) => s.length > 0);\n  let anyPathsFound = false;\n  for (const fileTouched of filesTouched) {\n    const absPath = path.resolve(fileTouched);\n    let st;\n    try {\n      st = ctx.fs.stat(absPath);\n      anyPathsFound = true;\n    } catch {\n      // Just move on if we have a bogus path from `tsc`. We'll log below if\n      // we fail to stat *any* of the paths emitted by `tsc`.\n      // TODO: Switch to using their JS API so we can get machine readable output.\n      continue;\n    }\n    ctx.fs.registerPath(absPath, st);\n  }\n  if (filesTouched.length > 0 && !anyPathsFound) {\n    const err = new Error(\n      `Failed to stat any files emitted by tsc (received ${filesTouched.length})`,\n    );\n    Sentry.captureException(err);\n  }\n\n  if (!result.error && result.status === 0) {\n    return handleResult(\"success\");\n  }\n\n  // This is the \"No inputs were found\", which is fine and we shouldn't\n  // report it to the user.\n  if (result.stdout.startsWith(\"error TS18003\")) {\n    return handleResult(\"success\");\n  }\n\n  // At this point we know that `tsc` failed. Rerun it without `--listFiles`\n  // and with stderr redirected to have it print out a nice error.\n  return handleResult(\n    \"typecheckFailed\",\n    () => {\n      logFailure(\"TypeScript typecheck via `tsc` failed.\");\n    },\n    async () => {\n      showSpinner(\"Collecting TypeScript errors\");\n      await spawnAsync(\n        ctx,\n        process.execPath,\n        [tscPath, ...tscArgs, \"--pretty\", \"true\"],\n        {\n          stdio: \"inherit\",\n        },\n      );\n      // If this passes, we had a concurrent file change that'll overlap with\n      // our observations in the first run. Invalidate our context's filesystem\n      // but allow the rest of the system to observe the success.\n      ctx.fs.invalidate();\n      return \"success\";\n    },\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,kBAAiB;AAEjB,iBAAkD;AAClD,aAAwB;AACxB,aAAwB;AACxB,mBAA2B;AAwB3B,eAAsB,yBACpB,KACA,eACA,cACe;AACf,MAAI,kBAAkB,WAAW;AAC/B;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO,QAAQ,kBAAkB,eAAe;AAC9C,UACG,WAAW,mBAAmB,kBAAkB,YACjD,WAAW,mBACX;AACA,2BAAmB;AACnB;AAAA,UACE,aAAAA,QAAM,KAAK,yDAAyD;AAAA,QACtE;AACA,YAAI;AACF,gBAAMC,UAAS,MAAM,aAAa;AAElC,cAAIA,YAAW,WAAW;AACxB;AAAA,UACF;AAAA,QACF,QAAQ;AAAA,QAER;AACA,cAAM,IAAI,MAAM;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAGA,eAAsB,mBACpB,KACA,cACA,cACe;AACf,QAAM,WAAW,YAAAC,QAAK,KAAK,cAAc,eAAe;AACxD,MAAI,CAAC,IAAI,GAAG,OAAO,QAAQ,GAAG;AAC5B,WAAO,aAAa,iBAAiB,MAAM;AACzC;AAAA,QACE;AAAA,MACF;AACA,+BAAS,gDAAgD;AAAA,IAC3D,CAAC;AAAA,EACH;AACA,QAAM,OAAO,KAAK,CAAC,aAAa,YAAY,GAAG,YAAY;AAC7D;AAEA,eAAe,OACb,KACA,SACA,cACe;AAEf,QAAM,UAAU,YAAAA,QAAK,KAAK,gBAAgB,cAAc,OAAO,KAAK;AACpE,MAAI,CAAC,IAAI,GAAG,OAAO,OAAO,GAAG;AAC3B,WAAO,aAAa,iBAAiB,MAAM;AACzC;AAAA,QACE,aAAAF,QAAM,KAAK,oDAAoD;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAM,gBAAgB,UAAM,yBAAW,KAAK,QAAQ,UAAU;AAAA,IAC5D;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UAAU,cAAc,OAAO,MAAM,cAAc,IAAI,CAAC,KAAK;AACnE,QAAM,4BAA4B,WAAW,OAAO,GAAG,SAAS,OAAO;AAEvE,QAAM,YAAY,KAAK,SAAS,SAAS,YAAY;AAGrD,MAAI,2BAA2B;AAC7B;AAAA,MACE,aAAAA,QAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,YACb,KACA,SACA,SACA,cACA;AAKA,QAAM,SAAS,UAAM,yBAAW,KAAK,QAAQ,UAAU;AAAA,IACrD;AAAA,IACA,GAAG;AAAA,IACH;AAAA,EACF,CAAC;AACD,MAAI,OAAO,WAAW,MAAM;AAC1B,WAAO,aAAa,mBAAmB,MAAM;AAC3C,iCAAW,iCAAiC;AAC5C,UAAI,OAAO,OAAO;AAChB,iCAAS,aAAAA,QAAM,IAAI,GAAG,OAAO,MAAM,SAAS,CAAC,EAAE,CAAC;AAAA,MAClD;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAM,eAAe,OAAO,OACzB,MAAM,IAAI,EACV,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EACnB,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC;AAC7B,MAAI,gBAAgB;AACpB,aAAW,eAAe,cAAc;AACtC,UAAM,UAAU,YAAAE,QAAK,QAAQ,WAAW;AACxC,QAAI;AACJ,QAAI;AACF,WAAK,IAAI,GAAG,KAAK,OAAO;AACxB,sBAAgB;AAAA,IAClB,QAAQ;AAIN;AAAA,IACF;AACA,QAAI,GAAG,aAAa,SAAS,EAAE;AAAA,EACjC;AACA,MAAI,aAAa,SAAS,KAAK,CAAC,eAAe;AAC7C,UAAM,MAAM,IAAI;AAAA,MACd,qDAAqD,aAAa,MAAM;AAAA,IAC1E;AACA,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AAEA,MAAI,CAAC,OAAO,SAAS,OAAO,WAAW,GAAG;AACxC,WAAO,aAAa,SAAS;AAAA,EAC/B;AAIA,MAAI,OAAO,OAAO,WAAW,eAAe,GAAG;AAC7C,WAAO,aAAa,SAAS;AAAA,EAC/B;AAIA,SAAO;AAAA,IACL;AAAA,IACA,MAAM;AACJ,iCAAW,wCAAwC;AAAA,IACrD;AAAA,IACA,YAAY;AACV,kCAAY,8BAA8B;AAC1C,gBAAM;AAAA,QACJ;AAAA,QACA,QAAQ;AAAA,QACR,CAAC,SAAS,GAAG,SAAS,YAAY,MAAM;AAAA,QACxC;AAAA,UACE,OAAO;AAAA,QACT;AAAA,MACF;AAIA,UAAI,GAAG,WAAW;AAClB,aAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": ["chalk", "result", "path"]}