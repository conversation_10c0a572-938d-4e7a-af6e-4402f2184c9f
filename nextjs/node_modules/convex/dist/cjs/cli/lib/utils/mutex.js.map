{"version": 3, "sources": ["../../../../../src/cli/lib/utils/mutex.ts"], "sourcesContent": ["export class Mutex {\n  currentlyRunning: Promise<void> | null = null;\n  waiting: Array<() => Promise<void>> = [];\n\n  async runExclusive<T>(fn: () => Promise<T>): Promise<T> {\n    const outerPromise = new Promise<T>((resolve, reject) => {\n      const wrappedCallback: () => Promise<void> = () => {\n        return fn()\n          .then((v: T) => resolve(v))\n          .catch((e: any) => reject(e));\n      };\n      this.enqueueCallbackForMutex(wrappedCallback);\n    });\n    return outerPromise;\n  }\n\n  private enqueueCallbackForMutex(callback: () => Promise<void>) {\n    if (this.currentlyRunning === null) {\n      this.currentlyRunning = callback().finally(() => {\n        const nextCb = this.waiting.shift();\n        if (nextCb === undefined) {\n          this.currentlyRunning = null;\n        } else {\n          this.enqueueCallbackForMutex(nextCb);\n        }\n      });\n      this.waiting.length = 0;\n    } else {\n      this.waiting.push(callback);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,MAAM,MAAM;AAAA,EAAZ;AACL,4CAAyC;AACzC,mCAAsC,CAAC;AAAA;AAAA,EAEvC,MAAM,aAAgB,IAAkC;AACtD,UAAM,eAAe,IAAI,QAAW,CAAC,SAAS,WAAW;AACvD,YAAM,kBAAuC,MAAM;AACjD,eAAO,GAAG,EACP,KAAK,CAAC,MAAS,QAAQ,CAAC,CAAC,EACzB,MAAM,CAAC,MAAW,OAAO,CAAC,CAAC;AAAA,MAChC;AACA,WAAK,wBAAwB,eAAe;AAAA,IAC9C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEQ,wBAAwB,UAA+B;AAC7D,QAAI,KAAK,qBAAqB,MAAM;AAClC,WAAK,mBAAmB,SAAS,EAAE,QAAQ,MAAM;AAC/C,cAAM,SAAS,KAAK,QAAQ,MAAM;AAClC,YAAI,WAAW,QAAW;AACxB,eAAK,mBAAmB;AAAA,QAC1B,OAAO;AACL,eAAK,wBAAwB,MAAM;AAAA,QACrC;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,SAAS;AAAA,IACxB,OAAO;AACL,WAAK,QAAQ,KAAK,QAAQ;AAAA,IAC5B;AAAA,EACF;AACF;", "names": []}