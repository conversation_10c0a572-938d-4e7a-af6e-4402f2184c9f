{"version": 3, "sources": ["../../../../../src/cli/lib/mcp/requestContext.ts"], "sourcesContent": ["import { BigBrainAuth, Context, ErrorType } from \"../../../bundler/context.js\";\nimport { Filesystem, nodeFs } from \"../../../bundler/fs.js\";\nimport { Ora } from \"ora\";\nimport {\n  DeploymentSelectionWithinProject,\n  deploymentSelectionWithinProjectSchema,\n  DeploymentSelectionOptions,\n} from \"../api.js\";\nimport { z } from \"zod\";\n\nexport interface McpOptions extends DeploymentSelectionOptions {\n  projectDir?: string;\n  disableTools?: string;\n  dangerouslyEnableProductionDeployments?: boolean;\n}\n\nexport class RequestContext implements Context {\n  fs: Filesystem;\n  deprecationMessagePrinted = false;\n  spinner: Ora | undefined;\n  _cleanupFns: Record<string, (exitCode: number, err?: any) => Promise<void>> =\n    {};\n  _bigBrainAuth: BigBrainAuth | null = null;\n  constructor(public options: McpOptions) {\n    this.fs = nodeFs;\n    this.deprecationMessagePrinted = false;\n  }\n\n  async crash(args: {\n    exitCode: number;\n    errorType?: ErrorType;\n    errForSentry?: any;\n    printedMessage: string | null;\n  }): Promise<never> {\n    const cleanupFns = this._cleanupFns;\n    this._cleanupFns = {};\n    for (const fn of Object.values(cleanupFns)) {\n      await fn(args.exitCode, args.errForSentry);\n    }\n    // eslint-disable-next-line no-restricted-syntax\n    throw new RequestCrash(args.exitCode, args.errorType, args.printedMessage);\n  }\n\n  flushAndExit() {\n    // eslint-disable-next-line no-restricted-syntax\n    throw new Error(\"Not implemented\");\n  }\n\n  registerCleanup(fn: (exitCode: number, err?: any) => Promise<void>): string {\n    const handle = crypto.randomUUID();\n    this._cleanupFns[handle] = fn;\n    return handle;\n  }\n\n  removeCleanup(handle: string) {\n    const value = this._cleanupFns[handle];\n    delete this._cleanupFns[handle];\n    return value ?? null;\n  }\n\n  bigBrainAuth(): BigBrainAuth | null {\n    return this._bigBrainAuth;\n  }\n\n  _updateBigBrainAuth(auth: BigBrainAuth | null): void {\n    this._bigBrainAuth = auth;\n  }\n\n  async decodeDeploymentSelector(encoded: string) {\n    const { projectDir, deployment } = decodeDeploymentSelector(encoded);\n    if (\n      deployment.kind === \"prod\" &&\n      !this.options.dangerouslyEnableProductionDeployments\n    ) {\n      return await this.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"Production deployments are disabled due to the --disable-production-deployments flag.\",\n      });\n    }\n    return { projectDir, deployment };\n  }\n\n  get productionDeploymentsDisabled() {\n    return !this.options.dangerouslyEnableProductionDeployments;\n  }\n}\n\nexport class RequestCrash {\n  printedMessage: string;\n  constructor(\n    private exitCode: number,\n    private errorType: ErrorType | undefined,\n    printedMessage: string | null,\n  ) {\n    this.printedMessage = printedMessage ?? \"Unknown error\";\n  }\n}\n\n// Unfortunately, MCP clients don't seem to handle nested JSON objects very\n// well (even though this is within spec). To work around this, encode the\n// deployment selectors as an obfuscated string that the MCP client can\n// opaquely pass around.\nexport function encodeDeploymentSelector(\n  projectDir: string,\n  deployment: DeploymentSelectionWithinProject,\n) {\n  const payload = {\n    projectDir,\n    deployment,\n  };\n  return `${deployment.kind}:${btoa(JSON.stringify(payload))}`;\n}\n\nconst payloadSchema = z.object({\n  projectDir: z.string(),\n  deployment: deploymentSelectionWithinProjectSchema,\n});\n\nfunction decodeDeploymentSelector(encoded: string) {\n  const [_, serializedPayload] = encoded.split(\":\");\n  return payloadSchema.parse(JSON.parse(atob(serializedPayload)));\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAmC;AAEnC,iBAIO;AACP,iBAAkB;AAQX,MAAM,eAAkC;AAAA,EAO7C,YAAmB,SAAqB;AAArB;AANnB;AACA,qDAA4B;AAC5B;AACA,uCACE,CAAC;AACH,yCAAqC;AAEnC,SAAK,KAAK;AACV,SAAK,4BAA4B;AAAA,EACnC;AAAA,EAEA,MAAM,MAAM,MAKO;AACjB,UAAM,aAAa,KAAK;AACxB,SAAK,cAAc,CAAC;AACpB,eAAW,MAAM,OAAO,OAAO,UAAU,GAAG;AAC1C,YAAM,GAAG,KAAK,UAAU,KAAK,YAAY;AAAA,IAC3C;AAEA,UAAM,IAAI,aAAa,KAAK,UAAU,KAAK,WAAW,KAAK,cAAc;AAAA,EAC3E;AAAA,EAEA,eAAe;AAEb,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AAAA,EAEA,gBAAgB,IAA4D;AAC1E,UAAM,SAAS,OAAO,WAAW;AACjC,SAAK,YAAY,MAAM,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA,EAEA,cAAc,QAAgB;AAC5B,UAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,WAAO,KAAK,YAAY,MAAM;AAC9B,WAAO,SAAS;AAAA,EAClB;AAAA,EAEA,eAAoC;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,oBAAoB,MAAiC;AACnD,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEA,MAAM,yBAAyB,SAAiB;AAC9C,UAAM,EAAE,YAAY,WAAW,IAAI,yBAAyB,OAAO;AACnE,QACE,WAAW,SAAS,UACpB,CAAC,KAAK,QAAQ,wCACd;AACA,aAAO,MAAM,KAAK,MAAM;AAAA,QACtB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,WAAO,EAAE,YAAY,WAAW;AAAA,EAClC;AAAA,EAEA,IAAI,gCAAgC;AAClC,WAAO,CAAC,KAAK,QAAQ;AAAA,EACvB;AACF;AAEO,MAAM,aAAa;AAAA,EAExB,YACU,UACA,WACR,gBACA;AAHQ;AACA;AAHV;AAME,SAAK,iBAAiB,kBAAkB;AAAA,EAC1C;AACF;AAMO,SAAS,yBACd,YACA,YACA;AACA,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,EACF;AACA,SAAO,GAAG,WAAW,IAAI,IAAI,KAAK,KAAK,UAAU,OAAO,CAAC,CAAC;AAC5D;AAEA,MAAM,gBAAgB,aAAE,OAAO;AAAA,EAC7B,YAAY,aAAE,OAAO;AAAA,EACrB,YAAY;AACd,CAAC;AAED,SAAS,yBAAyB,SAAiB;AACjD,QAAM,CAAC,GAAG,iBAAiB,IAAI,QAAQ,MAAM,GAAG;AAChD,SAAO,cAAc,MAAM,KAAK,MAAM,KAAK,iBAAiB,CAAC,CAAC;AAChE;", "names": []}