"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var runOneoffQuery_exports = {};
__export(runOneoffQuery_exports, {
  RunOneoffQueryTool: () => RunOneoffQueryTool
});
module.exports = __toCommonJS(runOneoffQuery_exports);
var import_zod = require("zod");
var import_api = require("../../api.js");
var import_deploymentSelection = require("../../deploymentSelection.js");
const inputSchema = import_zod.z.object({
  deploymentSelector: import_zod.z.string().describe(
    "Deployment selector (from the status tool) to run the query on."
  ),
  query: import_zod.z.string().describe(
    "The query to run. This should be valid JavaScript code that returns a value."
  )
});
const outputSchema = import_zod.z.object({
  result: import_zod.z.any().describe("The result returned by the query"),
  logLines: import_zod.z.array(import_zod.z.string()).describe("The log lines generated by the query")
});
const description = `
Run a one-off readonly query on your Convex deployment.

This tool executes a JavaScript string as a query in your Convex deployment.
The query should follow Convex guidelines and use the following setup:

\`\`\`js
import { query, internalQuery } from "convex:/_system/repl/wrappers.js";

export default query({
  handler: async (ctx) => {
    console.log("Write and test your query function here!");
  },
});
\`\`\`

Note that there are no imports available in this environment. The only import
you can use is the built-in "convex:/_system/repl/wrappers.js" module in the
template.

The function call is also completely sandboxed, so it can only read data and
cannot modify the database or access the network.

Returns the result and any log lines generated by the query.
`.trim();
const RunOneoffQueryTool = {
  name: "runOneoffQuery",
  description,
  inputSchema,
  outputSchema,
  handler: async (ctx, args) => {
    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(
      args.deploymentSelector
    );
    process.chdir(projectDir);
    const deploymentSelection = await (0, import_deploymentSelection.getDeploymentSelection)(ctx, ctx.options);
    const credentials = await (0, import_api.loadSelectedDeploymentCredentials)(
      ctx,
      deploymentSelection,
      deployment
    );
    try {
      const response = await fetch(`${credentials.url}/api/run_test_function`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          adminKey: credentials.adminKey,
          args: {},
          bundle: {
            path: "testQuery.js",
            source: args.query
          },
          format: "convex_encoded_json"
        })
      });
      if (!response.ok) {
        return await ctx.crash({
          exitCode: 1,
          errorType: "fatal",
          printedMessage: `HTTP error ${response.status}: ${await response.text()}`
        });
      }
      const result = await response.json();
      if (result.status !== "success") {
        return await ctx.crash({
          exitCode: 1,
          errorType: "fatal",
          printedMessage: `Query failed: ${JSON.stringify(result)}`
        });
      }
      return {
        result: result.value,
        logLines: result.logLines
      };
    } catch (err) {
      return await ctx.crash({
        exitCode: 1,
        errorType: "fatal",
        printedMessage: `Failed to run query: ${err.toString().trim()}`
      });
    }
  }
};
//# sourceMappingURL=runOneoffQuery.js.map
