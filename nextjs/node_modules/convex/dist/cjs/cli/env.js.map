{"version": 3, "sources": ["../../../src/cli/env.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport chalk from \"chalk\";\nimport { Context, oneoffContext } from \"../bundler/context.js\";\nimport {\n  DeploymentSelectionOptions,\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { ensureHasConvexDependency } from \"./lib/utils/utils.js\";\nimport {\n  envGetInDeployment,\n  envListInDeployment,\n  envRemoveInDeployment,\n  envSetInDeployment,\n} from \"./lib/env.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\n\nconst envSet = new Command(\"set\")\n  // Pretend value is required\n  .usage(\"[options] <name> <value>\")\n  .arguments(\"<name> [value]\")\n  .summary(\"Set a variable\")\n  .description(\n    \"Set a variable: `npx convex env set NAME value`\\n\" +\n      \"If the variable already exists, its value is updated.\\n\\n\" +\n      \"A single `NAME=value` argument is also supported.\",\n  )\n  .configureHelp({ showGlobalOptions: true })\n  .allowExcessArguments(false)\n  .action(async (originalName, originalValue, _options, cmd) => {\n    const options = cmd.optsWithGlobals();\n    const { ctx, deployment } = await selectEnvDeployment(options);\n    await ensureHasConvexDependency(ctx, \"env set\");\n    await envSetInDeployment(ctx, deployment, originalName, originalValue);\n  });\n\nasync function selectEnvDeployment(\n  options: DeploymentSelectionOptions,\n): Promise<{\n  ctx: Context;\n  deployment: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentNotice: string;\n  };\n}> {\n  const ctx = await oneoffContext(options);\n  const deploymentSelection = await getDeploymentSelection(ctx, options);\n  const selectionWithinProject =\n    deploymentSelectionWithinProjectFromOptions(options);\n  const {\n    adminKey,\n    url: deploymentUrl,\n    deploymentFields,\n  } = await loadSelectedDeploymentCredentials(\n    ctx,\n    deploymentSelection,\n    selectionWithinProject,\n  );\n  const deploymentNotice =\n    deploymentFields !== null\n      ? ` (on ${chalk.bold(deploymentFields.deploymentType)} deployment ${chalk.bold(deploymentFields.deploymentName)})`\n      : \"\";\n  return {\n    ctx,\n    deployment: {\n      deploymentUrl,\n      adminKey,\n      deploymentNotice,\n    },\n  };\n}\n\nconst envGet = new Command(\"get\")\n  .arguments(\"<name>\")\n  .summary(\"Print a variable's value\")\n  .description(\"Print a variable's value: `npx convex env get NAME`\")\n  .configureHelp({ showGlobalOptions: true })\n  .allowExcessArguments(false)\n  .action(async (envVarName, _options, cmd) => {\n    const options = cmd.optsWithGlobals();\n    const { ctx, deployment } = await selectEnvDeployment(options);\n    await ensureHasConvexDependency(ctx, \"env get\");\n    await envGetInDeployment(ctx, deployment, envVarName);\n  });\n\nconst envRemove = new Command(\"remove\")\n  .alias(\"rm\")\n  .alias(\"unset\")\n  .arguments(\"<name>\")\n  .summary(\"Unset a variable\")\n  .description(\n    \"Unset a variable: `npx convex env remove NAME`\\n\" +\n      \"If the variable doesn't exist, the command doesn't do anything and succeeds.\",\n  )\n  .configureHelp({ showGlobalOptions: true })\n  .allowExcessArguments(false)\n  .action(async (name, _options, cmd) => {\n    const options = cmd.optsWithGlobals();\n    const { ctx, deployment } = await selectEnvDeployment(options);\n    await ensureHasConvexDependency(ctx, \"env remove\");\n    await envRemoveInDeployment(ctx, deployment, name);\n  });\n\nconst envList = new Command(\"list\")\n  .summary(\"List all variables\")\n  .description(\"List all variables: `npx convex env list`\")\n  .configureHelp({ showGlobalOptions: true })\n  .allowExcessArguments(false)\n  .action(async (_options, cmd) => {\n    const options = cmd.optsWithGlobals();\n    const { ctx, deployment } = await selectEnvDeployment(options);\n    await ensureHasConvexDependency(ctx, \"env list\");\n    await envListInDeployment(ctx, deployment);\n  });\n\nexport const env = new Command(\"env\")\n  .summary(\"Set and view environment variables\")\n  .description(\n    \"Set and view environment variables on your deployment\\n\\n\" +\n      \"  Set a variable: `npx convex env set NAME value`\\n\" +\n      \"  Unset a variable: `npx convex env remove NAME`\\n\" +\n      \"  List all variables: `npx convex env list`\\n\" +\n      \"  Print a variable's value: `npx convex env get NAME`\\n\\n\" +\n      \"By default, this sets and views variables on your dev deployment.\",\n  )\n  .addCommand(envSet)\n  .addCommand(envGet)\n  .addCommand(envRemove)\n  .addCommand(envList)\n  .addHelpCommand(false)\n  .addDeploymentSelectionOptions(\n    actionDescription(\"Set and view environment variables on\"),\n  );\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,mBAAkB;AAClB,qBAAuC;AACvC,iBAIO;AACP,qBAAkC;AAClC,mBAA0C;AAC1C,iBAKO;AACP,iCAAuC;AAEvC,MAAM,SAAS,IAAI,6BAAQ,KAAK,EAE7B,MAAM,0BAA0B,EAChC,UAAU,gBAAgB,EAC1B,QAAQ,gBAAgB,EACxB;AAAA,EACC;AAGF,EACC,cAAc,EAAE,mBAAmB,KAAK,CAAC,EACzC,qBAAqB,KAAK,EAC1B,OAAO,OAAO,cAAc,eAAe,UAAU,QAAQ;AAC5D,QAAM,UAAU,IAAI,gBAAgB;AACpC,QAAM,EAAE,KAAK,WAAW,IAAI,MAAM,oBAAoB,OAAO;AAC7D,YAAM,wCAA0B,KAAK,SAAS;AAC9C,YAAM,+BAAmB,KAAK,YAAY,cAAc,aAAa;AACvE,CAAC;AAEH,eAAe,oBACb,SAQC;AACD,QAAM,MAAM,UAAM,8BAAc,OAAO;AACvC,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,OAAO;AACrE,QAAM,6BACJ,wDAA4C,OAAO;AACrD,QAAM;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL;AAAA,EACF,IAAI,UAAM;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,mBACJ,qBAAqB,OACjB,QAAQ,aAAAA,QAAM,KAAK,iBAAiB,cAAc,CAAC,eAAe,aAAAA,QAAM,KAAK,iBAAiB,cAAc,CAAC,MAC7G;AACN,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,MAAM,SAAS,IAAI,6BAAQ,KAAK,EAC7B,UAAU,QAAQ,EAClB,QAAQ,0BAA0B,EAClC,YAAY,qDAAqD,EACjE,cAAc,EAAE,mBAAmB,KAAK,CAAC,EACzC,qBAAqB,KAAK,EAC1B,OAAO,OAAO,YAAY,UAAU,QAAQ;AAC3C,QAAM,UAAU,IAAI,gBAAgB;AACpC,QAAM,EAAE,KAAK,WAAW,IAAI,MAAM,oBAAoB,OAAO;AAC7D,YAAM,wCAA0B,KAAK,SAAS;AAC9C,YAAM,+BAAmB,KAAK,YAAY,UAAU;AACtD,CAAC;AAEH,MAAM,YAAY,IAAI,6BAAQ,QAAQ,EACnC,MAAM,IAAI,EACV,MAAM,OAAO,EACb,UAAU,QAAQ,EAClB,QAAQ,kBAAkB,EAC1B;AAAA,EACC;AAEF,EACC,cAAc,EAAE,mBAAmB,KAAK,CAAC,EACzC,qBAAqB,KAAK,EAC1B,OAAO,OAAO,MAAM,UAAU,QAAQ;AACrC,QAAM,UAAU,IAAI,gBAAgB;AACpC,QAAM,EAAE,KAAK,WAAW,IAAI,MAAM,oBAAoB,OAAO;AAC7D,YAAM,wCAA0B,KAAK,YAAY;AACjD,YAAM,kCAAsB,KAAK,YAAY,IAAI;AACnD,CAAC;AAEH,MAAM,UAAU,IAAI,6BAAQ,MAAM,EAC/B,QAAQ,oBAAoB,EAC5B,YAAY,2CAA2C,EACvD,cAAc,EAAE,mBAAmB,KAAK,CAAC,EACzC,qBAAqB,KAAK,EAC1B,OAAO,OAAO,UAAU,QAAQ;AAC/B,QAAM,UAAU,IAAI,gBAAgB;AACpC,QAAM,EAAE,KAAK,WAAW,IAAI,MAAM,oBAAoB,OAAO;AAC7D,YAAM,wCAA0B,KAAK,UAAU;AAC/C,YAAM,gCAAoB,KAAK,UAAU;AAC3C,CAAC;AAEI,MAAM,MAAM,IAAI,6BAAQ,KAAK,EACjC,QAAQ,oCAAoC,EAC5C;AAAA,EACC;AAMF,EACC,WAAW,MAAM,EACjB,WAAW,MAAM,EACjB,WAAW,SAAS,EACpB,WAAW,OAAO,EAClB,eAAe,KAAK,EACpB;AAAA,MACC,kCAAkB,uCAAuC;AAC3D;", "names": ["chalk"]}