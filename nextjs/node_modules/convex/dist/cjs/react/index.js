"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var react_exports = {};
__export(react_exports, {
  ConvexProvider: () => import_client.ConvexProvider,
  ConvexReactClient: () => import_client.ConvexReactClient,
  useAction: () => import_client.useAction,
  useConvex: () => import_client.useConvex,
  useConvexConnectionState: () => import_client.useConvexConnectionState,
  useMutation: () => import_client.useMutation,
  useQueries: () => import_use_queries.useQueries,
  useQuery: () => import_client.useQuery,
  useSubscription: () => import_use_subscription.useSubscription
});
module.exports = __toCommonJS(react_exports);
__reExport(react_exports, require("./use_paginated_query.js"), module.exports);
var import_use_queries = require("./use_queries.js");
__reExport(react_exports, require("./auth_helpers.js"), module.exports);
__reExport(react_exports, require("./ConvexAuthState.js"), module.exports);
__reExport(react_exports, require("./hydration.js"), module.exports);
var import_use_subscription = require("./use_subscription.js");
var import_client = require("./client.js");
//# sourceMappingURL=index.js.map
