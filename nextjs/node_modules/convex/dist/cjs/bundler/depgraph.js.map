{"version": 3, "sources": ["../../../src/bundler/depgraph.ts"], "sourcesContent": ["/**\n * Normally esbuild can output a metafile containing the dependency\n * graph. However if bundling fails (say no dependency can be found)\n * then no metafile is produced.\n *\n * This plugin produces a similar dependency graph even in incomplete\n * bundling runs that are aborted early due to an error.\n *\n * It is WAY SLOWER!\n *\n * This enables a bundler error to be annotated with an import trace\n * describing why that file was imported.\n */\nimport * as esbuild from \"esbuild\";\nimport * as path from \"path\";\n\n// Interface for the tracer object returned by the plugin\ninterface ImportTracer {\n  /**\n   * Traces all import chains from a specific entry point to the specified file.\n   * @param entryPoint The entry point to start the trace from.\n   * @param filename The file to trace import chains to.\n   * @returns An array of import chains, each chain being an array of file paths.\n   */\n  traceImportChains(entryPoint: string, filename: string): string[][];\n\n  /**\n   * Returns a copy of the entire dependency graph.\n   * @returns A map where keys are importers and values are sets of imported files.\n   */\n  getDependencyGraph(): Map<string, Set<string>>;\n}\n\n// Interface for the combined plugin and tracer\ninterface ImportTracerPlugin {\n  plugin: esbuild.Plugin;\n  tracer: ImportTracer;\n}\n\n/**\n * Creates an esbuild plugin that tracks import dependencies.\n * The plugin builds a dependency graph during bundling without\n * reimplementing module resolution logic.\n *\n * @returns An object containing the plugin and a tracer for analyzing import chains.\n */\nfunction createImportTracerPlugin(): ImportTracerPlugin {\n  // Dependency graph: Map<importer, Set<imported>>\n  const dependencyGraph = new Map<string, Set<string>>();\n  // Set of entry points\n  const entryPoints = new Set<string>();\n  // Set of imports currently being processed to avoid infinite recursion\n  const processingImports = new Set<string>();\n\n  const plugin: esbuild.Plugin = {\n    name: \"import-tracer\",\n    setup(build) {\n      // Reset state on new build\n      build.onStart(() => {\n        dependencyGraph.clear();\n        entryPoints.clear();\n        processingImports.clear();\n      });\n\n      // Capture entry points\n      build.onResolve({ filter: /.*/ }, (args) => {\n        if (args.kind === \"entry-point\") {\n          entryPoints.add(args.path);\n        }\n        return null; // Continue with normal resolution\n      });\n\n      // Track resolved imports\n      build.onResolve({ filter: /.*/ }, async (args) => {\n        if (\n          args.importer &&\n          (args.kind === \"import-statement\" ||\n            args.kind === \"require-call\" ||\n            args.kind === \"dynamic-import\" ||\n            args.kind === \"require-resolve\")\n        ) {\n          const importKey = `${args.importer}:${args.path}`;\n\n          // Avoid infinite recursion\n          if (processingImports.has(importKey)) {\n            return null;\n          }\n\n          try {\n            processingImports.add(importKey);\n\n            //console.log(\"-------------> \", args.path);\n            // Use esbuild's resolution logic - this lets us avoid\n            // reimplementing module resolution ourselves\n            const result = await build.resolve(args.path, {\n              // Does it work to pretendit's always an import???\n              kind: \"import-statement\",\n              resolveDir: args.resolveDir,\n            });\n\n            if (result.errors.length === 0) {\n              // Record the dependency relationship\n              if (!dependencyGraph.has(args.importer)) {\n                dependencyGraph.set(args.importer, new Set());\n              }\n              dependencyGraph.get(args.importer)!.add(result.path);\n            }\n          } finally {\n            processingImports.delete(importKey);\n          }\n        }\n\n        return null; // Let esbuild continue with normal resolution\n      });\n    },\n  };\n\n  const tracer: ImportTracer = {\n    traceImportChains(entryPoint: string, filename: string): string[][] {\n      const resolvedEntryPoint = path.resolve(entryPoint);\n\n      // Find shortest path using BFS\n      const findShortestPath = (\n        start: string,\n        target: string,\n      ): string[] | null => {\n        const queue: { node: string; path: string[] }[] = [\n          { node: start, path: [start] },\n        ];\n        const visited = new Set<string>([start]);\n\n        while (queue.length > 0) {\n          const { node, path } = queue.shift()!;\n\n          if (node === target) {\n            return path;\n          }\n\n          const imports = dependencyGraph.get(node) || new Set();\n          for (const imp of imports) {\n            if (!visited.has(imp)) {\n              visited.add(imp);\n              queue.push({ node: imp, path: [...path, imp] });\n            }\n          }\n        }\n\n        return null;\n      };\n\n      const result = findShortestPath(resolvedEntryPoint, filename);\n      return result ? [result] : [];\n    },\n\n    getDependencyGraph(): Map<string, Set<string>> {\n      // Return a deep copy of the dependency graph\n      const copy = new Map<string, Set<string>>();\n      for (const [key, value] of dependencyGraph.entries()) {\n        copy.set(key, new Set(value));\n      }\n      return copy;\n    },\n  };\n\n  return { plugin, tracer };\n}\n\nexport default createImportTracerPlugin;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,WAAsB;AAgCtB,SAAS,2BAA+C;AAEtD,QAAM,kBAAkB,oBAAI,IAAyB;AAErD,QAAM,cAAc,oBAAI,IAAY;AAEpC,QAAM,oBAAoB,oBAAI,IAAY;AAE1C,QAAM,SAAyB;AAAA,IAC7B,MAAM;AAAA,IACN,MAAM,OAAO;AAEX,YAAM,QAAQ,MAAM;AAClB,wBAAgB,MAAM;AACtB,oBAAY,MAAM;AAClB,0BAAkB,MAAM;AAAA,MAC1B,CAAC;AAGD,YAAM,UAAU,EAAE,QAAQ,KAAK,GAAG,CAAC,SAAS;AAC1C,YAAI,KAAK,SAAS,eAAe;AAC/B,sBAAY,IAAI,KAAK,IAAI;AAAA,QAC3B;AACA,eAAO;AAAA,MACT,CAAC;AAGD,YAAM,UAAU,EAAE,QAAQ,KAAK,GAAG,OAAO,SAAS;AAChD,YACE,KAAK,aACJ,KAAK,SAAS,sBACb,KAAK,SAAS,kBACd,KAAK,SAAS,oBACd,KAAK,SAAS,oBAChB;AACA,gBAAM,YAAY,GAAG,KAAK,QAAQ,IAAI,KAAK,IAAI;AAG/C,cAAI,kBAAkB,IAAI,SAAS,GAAG;AACpC,mBAAO;AAAA,UACT;AAEA,cAAI;AACF,8BAAkB,IAAI,SAAS;AAK/B,kBAAM,SAAS,MAAM,MAAM,QAAQ,KAAK,MAAM;AAAA;AAAA,cAE5C,MAAM;AAAA,cACN,YAAY,KAAK;AAAA,YACnB,CAAC;AAED,gBAAI,OAAO,OAAO,WAAW,GAAG;AAE9B,kBAAI,CAAC,gBAAgB,IAAI,KAAK,QAAQ,GAAG;AACvC,gCAAgB,IAAI,KAAK,UAAU,oBAAI,IAAI,CAAC;AAAA,cAC9C;AACA,8BAAgB,IAAI,KAAK,QAAQ,EAAG,IAAI,OAAO,IAAI;AAAA,YACrD;AAAA,UACF,UAAE;AACA,8BAAkB,OAAO,SAAS;AAAA,UACpC;AAAA,QACF;AAEA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,SAAuB;AAAA,IAC3B,kBAAkB,YAAoB,UAA8B;AAClE,YAAM,qBAAqB,KAAK,QAAQ,UAAU;AAGlD,YAAM,mBAAmB,CACvB,OACA,WACoB;AACpB,cAAM,QAA4C;AAAA,UAChD,EAAE,MAAM,OAAO,MAAM,CAAC,KAAK,EAAE;AAAA,QAC/B;AACA,cAAM,UAAU,oBAAI,IAAY,CAAC,KAAK,CAAC;AAEvC,eAAO,MAAM,SAAS,GAAG;AACvB,gBAAM,EAAE,MAAM,MAAAA,MAAK,IAAI,MAAM,MAAM;AAEnC,cAAI,SAAS,QAAQ;AACnB,mBAAOA;AAAA,UACT;AAEA,gBAAM,UAAU,gBAAgB,IAAI,IAAI,KAAK,oBAAI,IAAI;AACrD,qBAAW,OAAO,SAAS;AACzB,gBAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACrB,sBAAQ,IAAI,GAAG;AACf,oBAAM,KAAK,EAAE,MAAM,KAAK,MAAM,CAAC,GAAGA,OAAM,GAAG,EAAE,CAAC;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,iBAAiB,oBAAoB,QAAQ;AAC5D,aAAO,SAAS,CAAC,MAAM,IAAI,CAAC;AAAA,IAC9B;AAAA,IAEA,qBAA+C;AAE7C,YAAM,OAAO,oBAAI,IAAyB;AAC1C,iBAAW,CAAC,KAAK,KAAK,KAAK,gBAAgB,QAAQ,GAAG;AACpD,aAAK,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,EAAE,QAAQ,OAAO;AAC1B;AAEA,IAAO,mBAAQ;", "names": ["path"]}