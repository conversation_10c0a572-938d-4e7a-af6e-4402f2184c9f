{"version": 3, "sources": ["../../../src/server/query.ts"], "sourcesContent": ["import {\n  DocumentByInfo,\n  GenericTableInfo,\n  IndexNames,\n  NamedIndex,\n  NamedSearchIndex,\n  SearchIndexNames,\n} from \"./data_model.js\";\nimport { ExpressionOrValue, FilterBuilder } from \"./filter_builder.js\";\nimport { IndexRange, IndexRangeBuilder } from \"./index_range_builder.js\";\nimport { PaginationResult, PaginationOptions } from \"./pagination.js\";\nimport { SearchFilter, SearchFilterBuilder } from \"./search_filter_builder.js\";\n\n/**\n * The {@link QueryInitializer} interface is the entry point for building a {@link Query}\n * over a Convex database table.\n *\n * There are two types of queries:\n * 1. Full table scans: Queries created with {@link QueryInitializer.fullTableScan} which\n * iterate over all of the documents in the table in insertion order.\n * 2. Indexed Queries: Queries created with {@link QueryInitializer.withIndex} which iterate\n * over an index range in index order.\n *\n * For convenience, {@link QueryInitializer} extends the {@link Query} interface, implicitly\n * starting a full table scan.\n *\n * @public\n */\nexport interface QueryInitializer<TableInfo extends GenericTableInfo>\n  extends Query<TableInfo> {\n  /**\n   * Query by reading all of the values out of this table.\n   *\n   * This query's cost is relative to the size of the entire table, so this\n   * should only be used on tables that will stay very small (say between a few\n   * hundred and a few thousand documents) and are updated infrequently.\n   *\n   * @returns - The {@link Query} that iterates over every document of the table.\n   */\n  fullTableScan(): Query<TableInfo>;\n\n  /**\n   * Query by reading documents from an index on this table.\n   *\n   * This query's cost is relative to the number of documents that match the\n   * index range expression.\n   *\n   * Results will be returned in index order.\n   *\n   * To learn about indexes, see [Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param indexName - The name of the index to query.\n   * @param indexRange - An optional index range constructed with the supplied\n   *  {@link IndexRangeBuilder}. An index range is a description of which\n   * documents Convex should consider when running the query. If no index\n   * range is present, the query will consider all documents in the index.\n   * @returns - The query that yields documents in the index.\n   */\n  withIndex<IndexName extends IndexNames<TableInfo>>(\n    indexName: IndexName,\n    indexRange?: (\n      q: IndexRangeBuilder<\n        DocumentByInfo<TableInfo>,\n        NamedIndex<TableInfo, IndexName>\n      >,\n    ) => IndexRange,\n  ): Query<TableInfo>;\n\n  /**\n   * Query by running a full text search against a search index.\n   *\n   * Search queries must always search for some text within the index's\n   * `searchField`. This query can optionally add equality filters for any\n   * `filterFields` specified in the index.\n   *\n   * Documents will be returned in relevance order based on how well they\n   * match the search text.\n   *\n   * To learn about full text search, see [Indexes](https://docs.convex.dev/text-search).\n   *\n   * @param indexName - The name of the search index to query.\n   * @param searchFilter - A search filter expression constructed with the\n   * supplied {@link SearchFilterBuilder}. This defines the full text search to run\n   * along with equality filtering to run within the search index.\n   * @returns - A query that searches for matching documents, returning them\n   * in relevancy order.\n   */\n  withSearchIndex<IndexName extends SearchIndexNames<TableInfo>>(\n    indexName: IndexName,\n    searchFilter: (\n      q: SearchFilterBuilder<\n        DocumentByInfo<TableInfo>,\n        NamedSearchIndex<TableInfo, IndexName>\n      >,\n    ) => SearchFilter,\n  ): OrderedQuery<TableInfo>;\n\n  /**\n   * The number of documents in the table.\n   *\n   * @internal\n   */\n  count(): Promise<number>;\n}\n\n/**\n * The {@link Query} interface allows functions to read values out of the database.\n *\n * **If you only need to load an object by ID, use `db.get(id)` instead.**\n *\n * Executing a query consists of calling\n * 1. (Optional) {@link Query.order} to define the order\n * 2. (Optional) {@link Query.filter} to refine the results\n * 3. A *consumer* method to obtain the results\n *\n * Queries are lazily evaluated. No work is done until iteration begins, so constructing and\n * extending a query is free. The query is executed incrementally as the results are iterated over,\n * so early terminating also reduces the cost of the query.\n *\n * It is more efficient to use `filter` expression rather than executing JavaScript to filter.\n *\n * |                                              | |\n * |----------------------------------------------|-|\n * | **Ordering**                                 | |\n * | [`order(\"asc\")`](#order)                     | Define the order of query results. |\n * |                                              | |\n * | **Filtering**                                | |\n * | [`filter(...)`](#filter)                     | Filter the query results to only the values that match some condition. |\n * |                                              | |\n * | **Consuming**                                | Execute a query and return results in different ways. |\n * | [`[Symbol.asyncIterator]()`](#asynciterator) | The query's results can be iterated over using a `for await..of` loop. |\n * | [`collect()`](#collect)                      | Return all of the results as an array. |\n * | [`take(n: number)`](#take)                   | Return the first `n` results as an array. |\n * | [`first()`](#first)                          | Return the first result. |\n * | [`unique()`](#unique)                        | Return the only result, and throw if there is more than one result. |\n *\n * To learn more about how to write queries, see [Querying the Database](https://docs.convex.dev/using/database-queries).\n *\n * @public\n */\nexport interface Query<TableInfo extends GenericTableInfo>\n  extends OrderedQuery<TableInfo> {\n  /**\n   * Define the order of the query output.\n   *\n   * Use `\"asc\"` for an ascending order and `\"desc\"` for a descending order. If not specified, the order defaults to ascending.\n   * @param order - The order to return results in.\n   */\n  order(order: \"asc\" | \"desc\"): OrderedQuery<TableInfo>;\n}\n\n/**\n * A {@link Query} with an order that has already been defined.\n *\n * @public\n */\nexport interface OrderedQuery<TableInfo extends GenericTableInfo>\n  extends AsyncIterable<DocumentByInfo<TableInfo>> {\n  /**\n   * Filter the query output, returning only the values for which `predicate` evaluates to true.\n   *\n   * @param predicate - An {@link Expression} constructed with the supplied {@link FilterBuilder} that specifies which documents to keep.\n   * @returns - A new {@link OrderedQuery} with the given filter predicate applied.\n   */\n  filter(\n    predicate: (q: FilterBuilder<TableInfo>) => ExpressionOrValue<boolean>,\n  ): this;\n\n  /**\n   * Take only the first `n` results from the pipeline so far.\n   *\n   * @param n - Limit for the number of results at this stage of the query pipeline.\n   * @returns - A new {@link OrderedQuery} with the specified limit applied.\n   *\n   * @internal\n   */\n  limit(n: number): this;\n\n  /**\n   * Load a page of `n` results and obtain a {@link Cursor} for loading more.\n   *\n   * Note: If this is called from a reactive query function the number of\n   * results may not match `paginationOpts.numItems`!\n   *\n   * `paginationOpts.numItems` is only an initial value. After the first invocation,\n   * `paginate` will return all items in the original query range. This ensures\n   * that all pages will remain adjacent and non-overlapping.\n   *\n   * @param paginationOpts - A {@link PaginationOptions} object containing the number\n   * of items to load and the cursor to start at.\n   * @returns A {@link PaginationResult} containing the page of results and a\n   * cursor to continue paginating.\n   */\n  paginate(\n    paginationOpts: PaginationOptions,\n  ): Promise<PaginationResult<DocumentByInfo<TableInfo>>>;\n\n  /**\n   * Execute the query and return all of the results as an array.\n   *\n   * Note: when processing a query with a lot of results, it's often better to use the `Query` as an\n   * `AsyncIterable` instead.\n   *\n   * @returns - An array of all of the query's results.\n   */\n  collect(): Promise<Array<DocumentByInfo<TableInfo>>>;\n\n  /**\n   * Execute the query and return the first `n` results.\n   *\n   * @param n - The number of items to take.\n   * @returns - An array of the first `n` results of the query (or less if the\n   * query doesn't have `n` results).\n   */\n  take(n: number): Promise<Array<DocumentByInfo<TableInfo>>>;\n\n  /**\n   * Execute the query and return the first result if there is one.\n   *\n   * @returns - The first value of the query or `null` if the query returned no results.\n   * */\n  first(): Promise<DocumentByInfo<TableInfo> | null>;\n\n  /**\n   * Execute the query and return the singular result if there is one.\n   *\n   * @returns - The single result returned from the query or null if none exists.\n   * @throws  Will throw an error if the query returns more than one result.\n   */\n  unique(): Promise<DocumentByInfo<TableInfo> | null>;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}