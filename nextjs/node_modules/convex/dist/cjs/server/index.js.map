{"version": 3, "sources": ["../../../src/server/index.ts"], "sourcesContent": ["/**\n * Utilities for implementing server-side Convex query and mutation functions.\n *\n * ## Usage\n *\n * ### Code Generation\n *\n * This module is typically used alongside generated server code.\n *\n * To generate the server code, run `npx convex dev` in your Convex project.\n * This will create a `convex/_generated/server.js` file with the following\n * functions, typed for your schema:\n * - [query](https://docs.convex.dev/generated-api/server#query)\n * - [mutation](https://docs.convex.dev/generated-api/server#mutation)\n *\n * If you aren't using TypeScript and code generation, you can use these untyped\n * functions instead:\n * - {@link queryGeneric}\n * - {@link mutationGeneric}\n *\n * ### Example\n *\n * Convex functions are defined by using either the `query` or\n * `mutation` wrappers.\n *\n * Queries receive a `db` that implements the {@link GenericDatabaseReader} interface.\n *\n * ```js\n * import { query } from \"./_generated/server\";\n *\n * export default query({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your (read-only) code here!\n *   },\n * });\n * ```\n *\n * If your function needs to write to the database, such as inserting, updating,\n * or deleting documents, use `mutation` instead which provides a `db` that\n * implements the {@link GenericDatabaseWriter} interface.\n *\n * ```js\n * import { mutation } from \"./_generated/server\";\n *\n * export default mutation({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your mutation code here!\n *   },\n * });\n * ```\n * @module\n */\n\nexport type {\n  Auth,\n  UserIdentity,\n  UserIdentityAttributes,\n} from \"./authentication.js\";\nexport * from \"./database.js\";\nexport type {\n  GenericDocument,\n  GenericFieldPaths,\n  GenericIndexFields,\n  GenericTableIndexes,\n  GenericSearchIndexConfig,\n  GenericTableSearchIndexes,\n  GenericVectorIndexConfig,\n  GenericTableVectorIndexes,\n  FieldTypeFromFieldPath,\n  FieldTypeFromFieldPathInner,\n  GenericTableInfo,\n  DocumentByInfo,\n  FieldPaths,\n  Indexes,\n  IndexNames,\n  NamedIndex,\n  SearchIndexes,\n  SearchIndexNames,\n  NamedSearchIndex,\n  VectorIndexes,\n  VectorIndexNames,\n  NamedVectorIndex,\n  GenericDataModel,\n  AnyDataModel,\n  TableNamesInDataModel,\n  NamedTableInfo,\n  DocumentByName,\n} from \"./data_model.js\";\n\nexport type {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"./filter_builder.js\";\nexport {\n  actionGeneric,\n  httpActionGeneric,\n  mutationGeneric,\n  queryGeneric,\n  internalActionGeneric,\n  internalMutationGeneric,\n  internalQueryGeneric,\n} from \"./impl/registration_impl.js\";\nexport type { IndexRange, IndexRangeBuilder } from \"./index_range_builder.js\";\nexport * from \"./pagination.js\";\nexport type { OrderedQuery, Query, QueryInitializer } from \"./query.js\";\nexport type {\n  ArgsArray,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  ActionBuilder,\n  MutationBuilder,\n  MutationBuilderWithTable,\n  QueryBuilder,\n  QueryBuilderWithTable,\n  HttpActionBuilder,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericMutationCtxWithTable,\n  GenericQueryCtx,\n  GenericQueryCtxWithTable,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n  PublicHttpAction,\n  UnvalidatedFunction,\n  ValidatedFunction,\n  ReturnValueForOptionalValidator,\n  ArgsArrayForOptionalValidator,\n  ArgsArrayToObject,\n  DefaultArgsForOptionalValidator,\n} from \"./registration.js\";\nexport * from \"./search_filter_builder.js\";\nexport * from \"./storage.js\";\nexport type { Scheduler, SchedulableFunctionReference } from \"./scheduler.js\";\nexport { cronJobs } from \"./cron.js\";\nexport type { CronJob, Crons } from \"./cron.js\";\nexport type {\n  SystemFields,\n  IdField,\n  WithoutSystemFields,\n  WithOptionalSystemFields,\n  SystemIndexes,\n  IndexTiebreakerField,\n} from \"./system_fields.js\";\nexport { httpRouter, HttpRouter, ROUTABLE_HTTP_METHODS } from \"./router.js\";\nexport type {\n  RoutableMethod,\n  RouteSpec,\n  RouteSpecWithPath,\n  RouteSpecWithPathPrefix,\n} from \"./router.js\";\nexport {\n  anyApi,\n  getFunctionName,\n  makeFunctionReference,\n  filterApi,\n} from \"./api.js\";\nexport type {\n  ApiFromModules,\n  AnyApi,\n  FilterApi,\n  FunctionType,\n  FunctionReference,\n  FunctionArgs,\n  OptionalRestArgs,\n  PartialApi,\n  ArgsAndOptions,\n  FunctionReturnType,\n} from \"./api.js\";\nexport {\n  defineApp,\n  defineComponent,\n  componentsGeneric,\n  createFunctionHandle,\n  type AnyChildComponents,\n} from \"./components/index.js\";\n/**\n * @internal\n */\nexport { currentSystemUdfInComponent } from \"./components/index.js\";\nexport { getFunctionAddress } from \"./components/index.js\";\nexport type {\n  ComponentDefinition,\n  AnyComponents,\n  FunctionHandle,\n} from \"./components/index.js\";\n\n/**\n * @internal\n */\nexport type { Index, SearchIndex, VectorIndex } from \"./schema.js\";\n\nexport type {\n  SearchIndexConfig,\n  VectorIndexConfig,\n  TableDefinition,\n  SchemaDefinition,\n  DefineSchemaOptions,\n  GenericSchema,\n  DataModelFromSchemaDefinition,\n  SystemDataModel,\n  SystemTableNames,\n} from \"./schema.js\";\nexport { defineTable, defineSchema } from \"./schema.js\";\n\nexport type {\n  VectorSearch,\n  VectorSearchQuery,\n  VectorFilterBuilder,\n  FilterExpression,\n} from \"./vector_search.js\";\n\n/**\n * @public\n */\nexport type { BetterOmit, Expand } from \"../type_utils.js\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0DA,2BAAc,0BA1Dd;AA8FA,+BAQO;AAEP,2BAAc,4BAxGd;AAoIA,2BAAc,uCApId;AAqIA,2BAAc,yBArId;AAuIA,kBAAyB;AAUzB,oBAA8D;AAO9D,iBAKO;AAaP,wBAMO;AAIP,IAAAA,qBAA4C;AAC5C,IAAAA,qBAAmC;AAuBnC,oBAA0C;", "names": ["import_components"]}