{"version": 3, "sources": ["../../../src/server/schema.ts"], "sourcesContent": ["/**\n * Utilities for defining the schema of your Convex project.\n *\n * ## Usage\n *\n * Schemas should be placed in a `schema.ts` file in your `convex/` directory.\n *\n * Schema definitions should be built using {@link defineSchema},\n * {@link defineTable}, and {@link values.v}. Make sure to export the schema as the\n * default export.\n *\n * ```ts\n * import { defineSchema, defineTable } from \"convex/server\";\n * import { v } from \"convex/values\";\n *\n *  export default defineSchema({\n *    messages: defineTable({\n *      body: v.string(),\n *      user: v.id(\"users\"),\n *    }),\n *    users: defineTable({\n *      name: v.string(),\n *    }),\n *  });\n * ```\n *\n * To learn more about schemas, see [Defining a Schema](https://docs.convex.dev/using/schemas).\n * @module\n */\nimport {\n  AnyDataModel,\n  GenericDataModel,\n  GenericTableIndexes,\n  GenericTableSearchIndexes,\n  GenericTableVectorIndexes,\n  TableNamesInDataModel,\n} from \"../server/data_model.js\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  IndexTiebreakerField,\n  SystemFields,\n  SystemIndexes,\n} from \"../server/system_fields.js\";\nimport { Expand } from \"../type_utils.js\";\nimport {\n  GenericValidator,\n  ObjectType,\n  isValidator,\n  v,\n} from \"../values/validator.js\";\nimport { VObject, Validator } from \"../values/validators.js\";\n\n/**\n * Extract all of the index field paths within a {@link Validator}.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractFieldPaths<T extends Validator<any, any, any>> =\n  // Add in the system fields available in index definitions.\n  // This should be everything except for `_id` because thats added to indexes\n  // automatically.\n  T[\"fieldPaths\"] | keyof SystemFields;\n\n/**\n * Extract the {@link GenericDocument} within a {@link Validator} and\n * add on the system fields.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractDocument<T extends Validator<any, any, any>> =\n  // Add the system fields to `Value` (except `_id` because it depends on\n  //the table name) and trick TypeScript into expanding them.\n  Expand<SystemFields & T[\"type\"]>;\n\nexport interface DbIndexConfig<\n  FirstFieldPath extends string,\n  RestFieldPaths extends string[],\n> {\n  /**\n   * The fields to index, in order. Must specify at least one field.\n   */\n  fields: [FirstFieldPath, ...RestFieldPaths];\n}\n\n/**\n * The configuration for a full text search index.\n *\n * @public\n */\nexport interface SearchIndexConfig<\n  SearchField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for full text search.\n   *\n   * This must be a field of type `string`.\n   */\n  searchField: SearchField;\n\n  /**\n   * Additional fields to index for fast filtering when running search queries.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * The configuration for a vector index.\n *\n * @public\n */\nexport interface VectorIndexConfig<\n  VectorField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for vector search.\n   *\n   * This must be a field of type `v.array(v.float64())` (or a union)\n   */\n  vectorField: VectorField;\n  /**\n   * The length of the vectors indexed. This must be between 2 and 2048 inclusive.\n   */\n  dimensions: number;\n  /**\n   * Additional fields to index for fast filtering when running vector searches.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * @internal\n */\nexport type VectorIndex = {\n  indexDescriptor: string;\n  vectorField: string;\n  dimensions: number;\n  filterFields: string[];\n};\n\n/**\n * @internal\n */\nexport type Index = {\n  indexDescriptor: string;\n  fields: string[];\n};\n\n/**\n * @internal\n */\nexport type SearchIndex = {\n  indexDescriptor: string;\n  searchField: string;\n  filterFields: string[];\n};\n\n/**\n * Options for defining an index.\n *\n * @public\n */\nexport interface IndexOptions {\n  /**\n   * Whether the index should be staged.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   */\n  staged?: boolean;\n}\n\n/**\n * The definition of a table within a schema.\n *\n * This should be produced by using {@link defineTable}.\n * @public\n */\nexport class TableDefinition<\n  DocumentType extends Validator<any, any, any> = Validator<any, any, any>,\n  Indexes extends GenericTableIndexes = {},\n  SearchIndexes extends GenericTableSearchIndexes = {},\n  VectorIndexes extends GenericTableVectorIndexes = {},\n> {\n  private indexes: Index[];\n  private stagedDbIndexes: Index[];\n  private searchIndexes: SearchIndex[];\n  private stagedSearchIndexes: SearchIndex[];\n  private vectorIndexes: VectorIndex[];\n  private stagedVectorIndexes: VectorIndex[];\n  // The type of documents stored in this table.\n  validator: DocumentType;\n\n  /**\n   * @internal\n   */\n  constructor(documentType: DocumentType) {\n    this.indexes = [];\n    this.stagedDbIndexes = [];\n    this.searchIndexes = [];\n    this.stagedSearchIndexes = [];\n    this.vectorIndexes = [];\n    this.stagedVectorIndexes = [];\n    this.validator = documentType;\n  }\n\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Returns indexes defined on this table.\n   * Intended for the advanced use cases of dynamically deciding which index to use for a query.\n   * If you think you need this, please chime in on ths issue in the Convex JS GitHub repo.\n   * https://github.com/get-convex/convex-js/issues/49\n   */\n  \" indexes\"(): { indexDescriptor: string; fields: string[] }[] {\n    return this.indexes;\n  }\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The index configuration object.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      DbIndexConfig<FirstFieldPath, RestFieldPaths> &\n        IndexOptions & { staged?: false }\n    >,\n  ): TableDefinition<\n    DocumentType,\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  >;\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param fields - The fields to index, in order. Must specify at least one\n   * field.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    fields: [FirstFieldPath, ...RestFieldPaths],\n  ): TableDefinition<\n    DocumentType,\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  >;\n\n  /**\n   * Define a staged index on this table.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The index configuration object.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      DbIndexConfig<FirstFieldPath, RestFieldPaths> &\n        IndexOptions & { staged: true }\n    >,\n  ): TableDefinition<DocumentType, Indexes, SearchIndexes, VectorIndexes>;\n\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    indexConfig:\n      | Expand<DbIndexConfig<FirstFieldPath, RestFieldPaths> & IndexOptions>\n      | [FirstFieldPath, ...RestFieldPaths],\n  ) {\n    if (Array.isArray(indexConfig)) {\n      // indexConfig is [FirstFieldPath, ...RestFieldPaths]\n      this.indexes.push({\n        indexDescriptor: name,\n        fields: indexConfig,\n      });\n    } else if (indexConfig.staged) {\n      // indexConfig is object with fields and staged: true\n      this.stagedDbIndexes.push({\n        indexDescriptor: name,\n        fields: indexConfig.fields,\n      });\n    } else {\n      // indexConfig is object with fields (and maybe staged: false/undefined)\n      this.indexes.push({\n        indexDescriptor: name,\n        fields: indexConfig.fields,\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Define a search index on this table.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      SearchIndexConfig<SearchField, FilterFields> &\n        IndexOptions & { staged?: false }\n    >,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    // Update `SearchIndexes` to include the new index and use `Expand` to make\n    // the types look pretty in editors.\n    Expand<\n      SearchIndexes &\n        Record<\n          IndexName,\n          {\n            searchField: SearchField;\n            filterFields: FilterFields;\n          }\n        >\n    >,\n    VectorIndexes\n  >;\n\n  /**\n   * Define a staged search index on this table.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      SearchIndexConfig<SearchField, FilterFields> &\n        IndexOptions & { staged: true }\n    >,\n  ): TableDefinition<DocumentType, Indexes, SearchIndexes, VectorIndexes>;\n\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      SearchIndexConfig<SearchField, FilterFields> & IndexOptions\n    >,\n  ) {\n    if (indexConfig.staged) {\n      this.stagedSearchIndexes.push({\n        indexDescriptor: name,\n        searchField: indexConfig.searchField,\n        filterFields: indexConfig.filterFields || [],\n      });\n    } else {\n      this.searchIndexes.push({\n        indexDescriptor: name,\n        searchField: indexConfig.searchField,\n        filterFields: indexConfig.filterFields || [],\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Define a vector index on this table.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      VectorIndexConfig<VectorField, FilterFields> &\n        IndexOptions & { staged?: false }\n    >,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    Expand<\n      VectorIndexes &\n        Record<\n          IndexName,\n          {\n            vectorField: VectorField;\n            dimensions: number;\n            filterFields: FilterFields;\n          }\n        >\n    >\n  >;\n\n  /**\n   * Define a staged vector index on this table.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      VectorIndexConfig<VectorField, FilterFields> &\n        IndexOptions & { staged: true }\n    >,\n  ): TableDefinition<DocumentType, Indexes, SearchIndexes, VectorIndexes>;\n\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      VectorIndexConfig<VectorField, FilterFields> & IndexOptions\n    >,\n  ) {\n    if (indexConfig.staged) {\n      this.stagedVectorIndexes.push({\n        indexDescriptor: name,\n        vectorField: indexConfig.vectorField,\n        dimensions: indexConfig.dimensions,\n        filterFields: indexConfig.filterFields || [],\n      });\n    } else {\n      this.vectorIndexes.push({\n        indexDescriptor: name,\n        vectorField: indexConfig.vectorField,\n        dimensions: indexConfig.dimensions,\n        filterFields: indexConfig.filterFields || [],\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Work around for https://github.com/microsoft/TypeScript/issues/57035\n   */\n  protected self(): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    return this;\n  }\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export() {\n    const documentType = this.validator.json;\n    if (typeof documentType !== \"object\") {\n      throw new Error(\n        \"Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)\",\n      );\n    }\n\n    return {\n      indexes: this.indexes,\n      stagedDbIndexes: this.stagedDbIndexes,\n      searchIndexes: this.searchIndexes,\n      stagedSearchIndexes: this.stagedSearchIndexes,\n      vectorIndexes: this.vectorIndexes,\n      stagedVectorIndexes: this.stagedVectorIndexes,\n      documentType,\n    };\n  }\n}\n\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Validator<Record<string, any>, \"required\", any>,\n>(documentSchema: DocumentSchema): TableDefinition<DocumentSchema>;\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Record<string, GenericValidator>,\n>(\n  documentSchema: DocumentSchema,\n): TableDefinition<VObject<ObjectType<DocumentSchema>, DocumentSchema>>;\nexport function defineTable<\n  DocumentSchema extends\n    | Validator<Record<string, any>, \"required\", any>\n    | Record<string, GenericValidator>,\n>(documentSchema: DocumentSchema): TableDefinition<any, any, any> {\n  if (isValidator(documentSchema)) {\n    return new TableDefinition(documentSchema);\n  } else {\n    return new TableDefinition(v.object(documentSchema));\n  }\n}\n\n/**\n * A type describing the schema of a Convex project.\n *\n * This should be constructed using {@link defineSchema}, {@link defineTable},\n * and {@link v}.\n * @public\n */\nexport type GenericSchema = Record<string, TableDefinition>;\n\n/**\n *\n * The definition of a Convex project schema.\n *\n * This should be produced by using {@link defineSchema}.\n * @public\n */\nexport class SchemaDefinition<\n  Schema extends GenericSchema,\n  StrictTableTypes extends boolean,\n> {\n  public tables: Schema;\n  public strictTableNameTypes!: StrictTableTypes;\n  public readonly schemaValidation: boolean;\n\n  /**\n   * @internal\n   */\n  constructor(tables: Schema, options?: DefineSchemaOptions<StrictTableTypes>) {\n    this.tables = tables;\n    this.schemaValidation =\n      options?.schemaValidation === undefined ? true : options.schemaValidation;\n  }\n\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export(): string {\n    return JSON.stringify({\n      tables: Object.entries(this.tables).map(([tableName, definition]) => {\n        const {\n          indexes,\n          stagedDbIndexes,\n          searchIndexes,\n          stagedSearchIndexes,\n          vectorIndexes,\n          stagedVectorIndexes,\n          documentType,\n        } = definition.export();\n        return {\n          tableName,\n          indexes,\n          stagedDbIndexes,\n          searchIndexes,\n          stagedSearchIndexes,\n          vectorIndexes,\n          stagedVectorIndexes,\n          documentType,\n        };\n      }),\n      schemaValidation: this.schemaValidation,\n    });\n  }\n}\n\n/**\n * Options for {@link defineSchema}.\n *\n * @public\n */\nexport interface DefineSchemaOptions<StrictTableNameTypes extends boolean> {\n  /**\n   * Whether Convex should validate at runtime that all documents match\n   * your schema.\n   *\n   * If `schemaValidation` is `true`, Convex will:\n   * 1. Check that all existing documents match your schema when your schema\n   * is pushed.\n   * 2. Check that all insertions and updates match your schema during mutations.\n   *\n   * If `schemaValidation` is `false`, Convex will not validate that new or\n   * existing documents match your schema. You'll still get schema-specific\n   * TypeScript types, but there will be no validation at runtime that your\n   * documents match those types.\n   *\n   * By default, `schemaValidation` is `true`.\n   */\n  schemaValidation?: boolean;\n\n  /**\n   * Whether the TypeScript types should allow accessing tables not in the schema.\n   *\n   * If `strictTableNameTypes` is `true`, using tables not listed in the schema\n   * will generate a TypeScript compilation error.\n   *\n   * If `strictTableNameTypes` is `false`, you'll be able to access tables not\n   * listed in the schema and their document type will be `any`.\n   *\n   * `strictTableNameTypes: false` is useful for rapid prototyping.\n   *\n   * Regardless of the value of `strictTableNameTypes`, your schema will only\n   * validate documents in the tables listed in the schema. You can still create\n   * and modify other tables on the dashboard or in JavaScript mutations.\n   *\n   * By default, `strictTableNameTypes` is `true`.\n   */\n  strictTableNameTypes?: StrictTableNameTypes;\n}\n\n/**\n * Define the schema of this Convex project.\n *\n * This should be exported from a `schema.ts` file in your `convex/` directory\n * like:\n *\n * ```ts\n * export default defineSchema({\n *   ...\n * });\n * ```\n *\n * @param schema - A map from table name to {@link TableDefinition} for all of\n * the tables in this project.\n * @param options - Optional configuration. See {@link DefineSchemaOptions} for\n * a full description.\n * @returns The schema.\n *\n * @public\n */\nexport function defineSchema<\n  Schema extends GenericSchema,\n  StrictTableNameTypes extends boolean = true,\n>(\n  schema: Schema,\n  options?: DefineSchemaOptions<StrictTableNameTypes>,\n): SchemaDefinition<Schema, StrictTableNameTypes> {\n  return new SchemaDefinition(schema, options);\n}\n\n/**\n * Internal type used in Convex code generation!\n *\n * Convert a {@link SchemaDefinition} into a {@link server.GenericDataModel}.\n *\n * @public\n */\nexport type DataModelFromSchemaDefinition<\n  SchemaDef extends SchemaDefinition<any, boolean>,\n> = MaybeMakeLooseDataModel<\n  {\n    [TableName in keyof SchemaDef[\"tables\"] &\n      string]: SchemaDef[\"tables\"][TableName] extends TableDefinition<\n      infer DocumentType,\n      infer Indexes,\n      infer SearchIndexes,\n      infer VectorIndexes\n    >\n      ? {\n          // We've already added all of the system fields except for `_id`.\n          // Add that here.\n          document: Expand<IdField<TableName> & ExtractDocument<DocumentType>>;\n          fieldPaths:\n            | keyof IdField<TableName>\n            | ExtractFieldPaths<DocumentType>;\n          indexes: Expand<Indexes & SystemIndexes>;\n          searchIndexes: SearchIndexes;\n          vectorIndexes: VectorIndexes;\n        }\n      : never;\n  },\n  SchemaDef[\"strictTableNameTypes\"]\n>;\n\ntype MaybeMakeLooseDataModel<\n  DataModel extends GenericDataModel,\n  StrictTableNameTypes extends boolean,\n> = StrictTableNameTypes extends true\n  ? DataModel\n  : Expand<DataModel & AnyDataModel>;\n\nconst _systemSchema = defineSchema({\n  _scheduled_functions: defineTable({\n    name: v.string(),\n    args: v.array(v.any()),\n    scheduledTime: v.float64(),\n    completedTime: v.optional(v.float64()),\n    state: v.union(\n      v.object({ kind: v.literal(\"pending\") }),\n      v.object({ kind: v.literal(\"inProgress\") }),\n      v.object({ kind: v.literal(\"success\") }),\n      v.object({ kind: v.literal(\"failed\"), error: v.string() }),\n      v.object({ kind: v.literal(\"canceled\") }),\n    ),\n  }),\n  _storage: defineTable({\n    sha256: v.string(),\n    size: v.float64(),\n    contentType: v.optional(v.string()),\n  }),\n});\n\nexport interface SystemDataModel\n  extends DataModelFromSchemaDefinition<typeof _systemSchema> {}\n\nexport type SystemTableNames = TableNamesInDataModel<SystemDataModel>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CA,uBAKO;AAwIA,MAAM,gBAKX;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,cAA4B;AAZxC,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AAER;AAAA;AAME,SAAK,UAAU,CAAC;AAChB,SAAK,kBAAkB,CAAC;AACxB,SAAK,gBAAgB,CAAC;AACtB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,gBAAgB,CAAC;AACtB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAA8D;AAC5D,WAAO,KAAK;AAAA,EACd;AAAA,EA4FA,MAKE,MACA,aAGA;AACA,QAAI,MAAM,QAAQ,WAAW,GAAG;AAE9B,WAAK,QAAQ,KAAK;AAAA,QAChB,iBAAiB;AAAA,QACjB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,WAAW,YAAY,QAAQ;AAE7B,WAAK,gBAAgB,KAAK;AAAA,QACxB,iBAAiB;AAAA,QACjB,QAAQ,YAAY;AAAA,MACtB,CAAC;AAAA,IACH,OAAO;AAEL,WAAK,QAAQ,KAAK;AAAA,QAChB,iBAAiB;AAAA,QACjB,QAAQ,YAAY;AAAA,MACtB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAmEA,YAKE,MACA,aAGA;AACA,QAAI,YAAY,QAAQ;AACtB,WAAK,oBAAoB,KAAK;AAAA,QAC5B,iBAAiB;AAAA,QACjB,aAAa,YAAY;AAAA,QACzB,cAAc,YAAY,gBAAgB,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH,OAAO;AACL,WAAK,cAAc,KAAK;AAAA,QACtB,iBAAiB;AAAA,QACjB,aAAa,YAAY;AAAA,QACzB,cAAc,YAAY,gBAAgB,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAkEA,YAKE,MACA,aAGA;AACA,QAAI,YAAY,QAAQ;AACtB,WAAK,oBAAoB,KAAK;AAAA,QAC5B,iBAAiB;AAAA,QACjB,aAAa,YAAY;AAAA,QACzB,YAAY,YAAY;AAAA,QACxB,cAAc,YAAY,gBAAgB,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH,OAAO;AACL,WAAK,cAAc,KAAK;AAAA,QACtB,iBAAiB;AAAA,QACjB,aAAa,YAAY;AAAA,QACzB,YAAY,YAAY;AAAA,QACxB,cAAc,YAAY,gBAAgB,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKU,OAKR;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,UAAM,eAAe,KAAK,UAAU;AACpC,QAAI,OAAO,iBAAiB,UAAU;AACpC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,iBAAiB,KAAK;AAAA,MACtB,eAAe,KAAK;AAAA,MACpB,qBAAqB,KAAK;AAAA,MAC1B,eAAe,KAAK;AAAA,MACpB,qBAAqB,KAAK;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF;AA4DO,SAAS,YAId,gBAAgE;AAChE,UAAI,8BAAY,cAAc,GAAG;AAC/B,WAAO,IAAI,gBAAgB,cAAc;AAAA,EAC3C,OAAO;AACL,WAAO,IAAI,gBAAgB,mBAAE,OAAO,cAAc,CAAC;AAAA,EACrD;AACF;AAkBO,MAAM,iBAGX;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,QAAgB,SAAiD;AAP7E,wBAAO;AACP,wBAAO;AACP,wBAAgB;AAMd,SAAK,SAAS;AACd,SAAK,mBACH,SAAS,qBAAqB,SAAY,OAAO,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAiB;AACf,WAAO,KAAK,UAAU;AAAA,MACpB,QAAQ,OAAO,QAAQ,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,WAAW,UAAU,MAAM;AACnE,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,WAAW,OAAO;AACtB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,kBAAkB,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAkEO,SAAS,aAId,QACA,SACgD;AAChD,SAAO,IAAI,iBAAiB,QAAQ,OAAO;AAC7C;AA2CA,MAAM,gBAAgB,aAAa;AAAA,EACjC,sBAAsB,YAAY;AAAA,IAChC,MAAM,mBAAE,OAAO;AAAA,IACf,MAAM,mBAAE,MAAM,mBAAE,IAAI,CAAC;AAAA,IACrB,eAAe,mBAAE,QAAQ;AAAA,IACzB,eAAe,mBAAE,SAAS,mBAAE,QAAQ,CAAC;AAAA,IACrC,OAAO,mBAAE;AAAA,MACP,mBAAE,OAAO,EAAE,MAAM,mBAAE,QAAQ,SAAS,EAAE,CAAC;AAAA,MACvC,mBAAE,OAAO,EAAE,MAAM,mBAAE,QAAQ,YAAY,EAAE,CAAC;AAAA,MAC1C,mBAAE,OAAO,EAAE,MAAM,mBAAE,QAAQ,SAAS,EAAE,CAAC;AAAA,MACvC,mBAAE,OAAO,EAAE,MAAM,mBAAE,QAAQ,QAAQ,GAAG,OAAO,mBAAE,OAAO,EAAE,CAAC;AAAA,MACzD,mBAAE,OAAO,EAAE,MAAM,mBAAE,QAAQ,UAAU,EAAE,CAAC;AAAA,IAC1C;AAAA,EACF,CAAC;AAAA,EACD,UAAU,YAAY;AAAA,IACpB,QAAQ,mBAAE,OAAO;AAAA,IACjB,MAAM,mBAAE,QAAQ;AAAA,IAChB,aAAa,mBAAE,SAAS,mBAAE,OAAO,CAAC;AAAA,EACpC,CAAC;AACH,CAAC;", "names": []}