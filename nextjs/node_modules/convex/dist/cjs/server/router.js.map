{"version": 3, "sources": ["../../../src/server/router.ts"], "sourcesContent": ["import { performJsSyscall } from \"./impl/syscall.js\";\nimport { PublicHttpAction } from \"./registration.js\";\n\n// Note: this list is duplicated in the dashboard.\n/**\n * A list of the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport const ROUTABLE_HTTP_METHODS = [\n  \"GET\",\n  \"POST\",\n  \"PUT\",\n  \"DELETE\",\n  \"OPTIONS\",\n  \"PATCH\",\n] as const;\n/**\n * A type representing the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport type RoutableMethod = (typeof ROUTABLE_HTTP_METHODS)[number];\n\nexport function normalizeMethod(\n  method: RoutableMethod | \"HEAD\",\n): RoutableMethod {\n  // This router routes HEAD requests as GETs, letting <PERSON><PERSON><PERSON> strip thee response\n  // bodies are response bodies afterward.\n  if (method === \"HEAD\") return \"GET\";\n  return method;\n}\n\n/**\n * Return a new {@link HttpRouter} object.\n *\n * @public\n */\nexport const httpRouter = () => new HttpRouter();\n\n/**\n * A type representing a route to an HTTP action using an exact request URL path match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPath = {\n  /**\n   * Exact HTTP request path to route.\n   */\n  path: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action using a request URL path prefix match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPathPrefix = {\n  /**\n   * An HTTP request path prefix to route. Requests with a path starting with this value\n   * will be routed to the HTTP action.\n   */\n  pathPrefix: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpec = RouteSpecWithPath | RouteSpecWithPathPrefix;\n\n/**\n * HTTP router for specifying the paths and methods of {@link httpActionGeneric}s\n *\n * An example `convex/http.js` file might look like this.\n *\n * ```js\n * import { httpRouter } from \"convex/server\";\n * import { getMessagesByAuthor } from \"./getMessagesByAuthor\";\n * import { httpAction } from \"./_generated/server\";\n *\n * const http = httpRouter();\n *\n * // HTTP actions can be defined inline...\n * http.route({\n *   path: \"/message\",\n *   method: \"POST\",\n *   handler: httpAction(async ({ runMutation }, request) => {\n *     const { author, body } = await request.json();\n *\n *     await runMutation(api.sendMessage.default, { body, author });\n *     return new Response(null, {\n *       status: 200,\n *     });\n *   })\n * });\n *\n * // ...or they can be imported from other files.\n * http.route({\n *   path: \"/getMessagesByAuthor\",\n *   method: \"GET\",\n *   handler: getMessagesByAuthor,\n * });\n *\n * // Convex expects the router to be the default export of `convex/http.js`.\n * export default http;\n * ```\n *\n * @public\n */\nexport class HttpRouter {\n  exactRoutes: Map<string, Map<RoutableMethod, PublicHttpAction>> = new Map();\n  prefixRoutes: Map<RoutableMethod, Map<string, PublicHttpAction>> = new Map();\n  isRouter: true = true;\n\n  /**\n   * Specify an HttpAction to be used to respond to requests\n   * for an HTTP method (e.g. \"GET\") and a path or pathPrefix.\n   *\n   * Paths must begin with a slash. Path prefixes must also end in a slash.\n   *\n   * ```js\n   * // matches `/profile` (but not `/profile/`)\n   * http.route({ path: \"/profile\", method: \"GET\", handler: getProfile})\n   *\n   * // matches `/profiles/`, `/profiles/abc`, and `/profiles/a/c/b` (but not `/profile`)\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile})\n   * ```\n   */\n  route = (spec: RouteSpec) => {\n    if (!spec.handler) throw new Error(`route requires handler`);\n    if (!spec.method) throw new Error(`route requires method`);\n    const { method, handler } = spec;\n    if (!ROUTABLE_HTTP_METHODS.includes(method)) {\n      throw new Error(\n        `'${method}' is not an allowed HTTP method (like GET, POST, PUT etc.)`,\n      );\n    }\n\n    if (\"path\" in spec) {\n      if (\"pathPrefix\" in spec) {\n        throw new Error(\n          `Invalid httpRouter route: cannot contain both 'path' and 'pathPrefix'`,\n        );\n      }\n      if (!spec.path.startsWith(\"/\")) {\n        throw new Error(`path '${spec.path}' does not start with a /`);\n      }\n      if (spec.path.startsWith(\"/.files/\") || spec.path === \"/.files\") {\n        throw new Error(`path '${spec.path}' is reserved`);\n      }\n      const methods: Map<RoutableMethod, PublicHttpAction> =\n        this.exactRoutes.has(spec.path)\n          ? this.exactRoutes.get(spec.path)!\n          : new Map();\n      if (methods.has(method)) {\n        throw new Error(\n          `Path '${spec.path}' for method ${method} already in use`,\n        );\n      }\n      methods.set(method, handler);\n      this.exactRoutes.set(spec.path, methods);\n    } else if (\"pathPrefix\" in spec) {\n      if (!spec.pathPrefix.startsWith(\"/\")) {\n        throw new Error(\n          `pathPrefix '${spec.pathPrefix}' does not start with a /`,\n        );\n      }\n      if (!spec.pathPrefix.endsWith(\"/\")) {\n        throw new Error(`pathPrefix ${spec.pathPrefix} must end with a /`);\n      }\n      if (spec.pathPrefix.startsWith(\"/.files/\")) {\n        throw new Error(`pathPrefix '${spec.pathPrefix}' is reserved`);\n      }\n      const prefixes =\n        this.prefixRoutes.get(method) || new Map<string, PublicHttpAction>();\n      if (prefixes.has(spec.pathPrefix)) {\n        throw new Error(\n          `${spec.method} pathPrefix ${spec.pathPrefix} is already defined`,\n        );\n      }\n      prefixes.set(spec.pathPrefix, handler);\n      this.prefixRoutes.set(method, prefixes);\n    } else {\n      throw new Error(\n        `Invalid httpRouter route entry: must contain either field 'path' or 'pathPrefix'`,\n      );\n    }\n  };\n\n  /**\n   * Returns a list of routed HTTP actions.\n   *\n   * These are used to populate the list of routes shown in the Functions page of the Convex dashboard.\n   *\n   * @returns - an array of [path, method, endpoint] tuples.\n   */\n  getRoutes = (): Array<\n    Readonly<[string, RoutableMethod, PublicHttpAction]>\n  > => {\n    const exactPaths: string[] = [...this.exactRoutes.keys()].sort();\n    const exact = exactPaths.flatMap((path) =>\n      [...this.exactRoutes.get(path)!.keys()]\n        .sort()\n        .map(\n          (method) =>\n            [path, method, this.exactRoutes.get(path)!.get(method)!] as const,\n        ),\n    );\n\n    const prefixPathMethods = [...this.prefixRoutes.keys()].sort();\n    const prefixes = prefixPathMethods.flatMap((method) =>\n      [...this.prefixRoutes.get(method)!.keys()]\n        .sort()\n        .map(\n          (pathPrefix) =>\n            [\n              `${pathPrefix}*`,\n              method,\n              this.prefixRoutes.get(method)!.get(pathPrefix)!,\n            ] as const,\n        ),\n    );\n\n    return [...exact, ...prefixes];\n  };\n\n  /**\n   * Returns the appropriate HTTP action and its routed request path and method.\n   *\n   * The path and method returned are used for logging and metrics, and should\n   * match up with one of the routes returned by `getRoutes`.\n   *\n   * For example,\n   *\n   * ```js\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile});\n   *\n   * http.lookup(\"/profile/abc\", \"GET\") // returns [getProfile, \"GET\", \"/profile/*\"]\n   *```\n   *\n   * @returns - a tuple [{@link PublicHttpAction}, method, path] or null.\n   */\n  lookup = (\n    path: string,\n    method: RoutableMethod | \"HEAD\",\n  ): Readonly<[PublicHttpAction, RoutableMethod, string]> | null => {\n    method = normalizeMethod(method);\n    const exactMatch = this.exactRoutes.get(path)?.get(method);\n    if (exactMatch) return [exactMatch, method, path];\n\n    const prefixes = this.prefixRoutes.get(method) || new Map();\n    const prefixesSorted = [...prefixes.entries()].sort(\n      ([prefixA, _a], [prefixB, _b]) => prefixB.length - prefixA.length,\n    );\n    for (const [pathPrefix, endpoint] of prefixesSorted) {\n      if (path.startsWith(pathPrefix)) {\n        return [endpoint, method, `${pathPrefix}*`];\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Given a JSON string representation of a Request object, return a Response\n   * by routing the request and running the appropriate endpoint or returning\n   * a 404 Response.\n   *\n   * @param argsStr - a JSON string representing a Request object.\n   *\n   * @returns - a Response object.\n   */\n  runRequest = async (\n    argsStr: string,\n    requestRoute: string,\n  ): Promise<string> => {\n    const request = performJsSyscall(\"requestFromConvexJson\", {\n      convexJson: JSON.parse(argsStr),\n    });\n\n    let pathname = requestRoute;\n    if (!pathname || typeof pathname !== \"string\") {\n      pathname = new URL(request.url).pathname;\n    }\n\n    const method = request.method;\n    const match = this.lookup(pathname, method as RoutableMethod);\n    if (!match) {\n      const response = new Response(`No HttpAction routed for ${pathname}`, {\n        status: 404,\n      });\n      return JSON.stringify(\n        performJsSyscall(\"convexJsonFromResponse\", { response }),\n      );\n    }\n    const [endpoint, _method, _path] = match;\n    const response = await endpoint.invokeHttpAction(request);\n    return JSON.stringify(\n      performJsSyscall(\"convexJsonFromResponse\", { response }),\n    );\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAiC;AAa1B,MAAM,wBAAwB;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAYO,SAAS,gBACd,QACgB;AAGhB,MAAI,WAAW,OAAQ,QAAO;AAC9B,SAAO;AACT;AAOO,MAAM,aAAa,MAAM,IAAI,WAAW;AA+FxC,MAAM,WAAW;AAAA,EAAjB;AACL,uCAAkE,oBAAI,IAAI;AAC1E,wCAAmE,oBAAI,IAAI;AAC3E,oCAAiB;AAgBjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAAQ,CAAC,SAAoB;AAC3B,UAAI,CAAC,KAAK,QAAS,OAAM,IAAI,MAAM,wBAAwB;AAC3D,UAAI,CAAC,KAAK,OAAQ,OAAM,IAAI,MAAM,uBAAuB;AACzD,YAAM,EAAE,QAAQ,QAAQ,IAAI;AAC5B,UAAI,CAAC,sBAAsB,SAAS,MAAM,GAAG;AAC3C,cAAM,IAAI;AAAA,UACR,IAAI,MAAM;AAAA,QACZ;AAAA,MACF;AAEA,UAAI,UAAU,MAAM;AAClB,YAAI,gBAAgB,MAAM;AACxB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,KAAK,KAAK,WAAW,GAAG,GAAG;AAC9B,gBAAM,IAAI,MAAM,SAAS,KAAK,IAAI,2BAA2B;AAAA,QAC/D;AACA,YAAI,KAAK,KAAK,WAAW,UAAU,KAAK,KAAK,SAAS,WAAW;AAC/D,gBAAM,IAAI,MAAM,SAAS,KAAK,IAAI,eAAe;AAAA,QACnD;AACA,cAAM,UACJ,KAAK,YAAY,IAAI,KAAK,IAAI,IAC1B,KAAK,YAAY,IAAI,KAAK,IAAI,IAC9B,oBAAI,IAAI;AACd,YAAI,QAAQ,IAAI,MAAM,GAAG;AACvB,gBAAM,IAAI;AAAA,YACR,SAAS,KAAK,IAAI,gBAAgB,MAAM;AAAA,UAC1C;AAAA,QACF;AACA,gBAAQ,IAAI,QAAQ,OAAO;AAC3B,aAAK,YAAY,IAAI,KAAK,MAAM,OAAO;AAAA,MACzC,WAAW,gBAAgB,MAAM;AAC/B,YAAI,CAAC,KAAK,WAAW,WAAW,GAAG,GAAG;AACpC,gBAAM,IAAI;AAAA,YACR,eAAe,KAAK,UAAU;AAAA,UAChC;AAAA,QACF;AACA,YAAI,CAAC,KAAK,WAAW,SAAS,GAAG,GAAG;AAClC,gBAAM,IAAI,MAAM,cAAc,KAAK,UAAU,oBAAoB;AAAA,QACnE;AACA,YAAI,KAAK,WAAW,WAAW,UAAU,GAAG;AAC1C,gBAAM,IAAI,MAAM,eAAe,KAAK,UAAU,eAAe;AAAA,QAC/D;AACA,cAAM,WACJ,KAAK,aAAa,IAAI,MAAM,KAAK,oBAAI,IAA8B;AACrE,YAAI,SAAS,IAAI,KAAK,UAAU,GAAG;AACjC,gBAAM,IAAI;AAAA,YACR,GAAG,KAAK,MAAM,eAAe,KAAK,UAAU;AAAA,UAC9C;AAAA,QACF;AACA,iBAAS,IAAI,KAAK,YAAY,OAAO;AACrC,aAAK,aAAa,IAAI,QAAQ,QAAQ;AAAA,MACxC,OAAO;AACL,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qCAAY,MAEP;AACH,YAAM,aAAuB,CAAC,GAAG,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK;AAC/D,YAAM,QAAQ,WAAW;AAAA,QAAQ,CAAC,SAChC,CAAC,GAAG,KAAK,YAAY,IAAI,IAAI,EAAG,KAAK,CAAC,EACnC,KAAK,EACL;AAAA,UACC,CAAC,WACC,CAAC,MAAM,QAAQ,KAAK,YAAY,IAAI,IAAI,EAAG,IAAI,MAAM,CAAE;AAAA,QAC3D;AAAA,MACJ;AAEA,YAAM,oBAAoB,CAAC,GAAG,KAAK,aAAa,KAAK,CAAC,EAAE,KAAK;AAC7D,YAAM,WAAW,kBAAkB;AAAA,QAAQ,CAAC,WAC1C,CAAC,GAAG,KAAK,aAAa,IAAI,MAAM,EAAG,KAAK,CAAC,EACtC,KAAK,EACL;AAAA,UACC,CAAC,eACC;AAAA,YACE,GAAG,UAAU;AAAA,YACb;AAAA,YACA,KAAK,aAAa,IAAI,MAAM,EAAG,IAAI,UAAU;AAAA,UAC/C;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO,CAAC,GAAG,OAAO,GAAG,QAAQ;AAAA,IAC/B;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAAS,CACP,MACA,WACgE;AAChE,eAAS,gBAAgB,MAAM;AAC/B,YAAM,aAAa,KAAK,YAAY,IAAI,IAAI,GAAG,IAAI,MAAM;AACzD,UAAI,WAAY,QAAO,CAAC,YAAY,QAAQ,IAAI;AAEhD,YAAM,WAAW,KAAK,aAAa,IAAI,MAAM,KAAK,oBAAI,IAAI;AAC1D,YAAM,iBAAiB,CAAC,GAAG,SAAS,QAAQ,CAAC,EAAE;AAAA,QAC7C,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,MAAM,QAAQ,SAAS,QAAQ;AAAA,MAC7D;AACA,iBAAW,CAAC,YAAY,QAAQ,KAAK,gBAAgB;AACnD,YAAI,KAAK,WAAW,UAAU,GAAG;AAC/B,iBAAO,CAAC,UAAU,QAAQ,GAAG,UAAU,GAAG;AAAA,QAC5C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCAAa,OACX,SACA,iBACoB;AACpB,YAAM,cAAU,iCAAiB,yBAAyB;AAAA,QACxD,YAAY,KAAK,MAAM,OAAO;AAAA,MAChC,CAAC;AAED,UAAI,WAAW;AACf,UAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7C,mBAAW,IAAI,IAAI,QAAQ,GAAG,EAAE;AAAA,MAClC;AAEA,YAAM,SAAS,QAAQ;AACvB,YAAM,QAAQ,KAAK,OAAO,UAAU,MAAwB;AAC5D,UAAI,CAAC,OAAO;AACV,cAAMA,YAAW,IAAI,SAAS,4BAA4B,QAAQ,IAAI;AAAA,UACpE,QAAQ;AAAA,QACV,CAAC;AACD,eAAO,KAAK;AAAA,cACV,iCAAiB,0BAA0B,EAAE,UAAAA,UAAS,CAAC;AAAA,QACzD;AAAA,MACF;AACA,YAAM,CAAC,UAAU,SAAS,KAAK,IAAI;AACnC,YAAM,WAAW,MAAM,SAAS,iBAAiB,OAAO;AACxD,aAAO,KAAK;AAAA,YACV,iCAAiB,0BAA0B,EAAE,SAAS,CAAC;AAAA,MACzD;AAAA,IACF;AAAA;AACF;", "names": ["response"]}