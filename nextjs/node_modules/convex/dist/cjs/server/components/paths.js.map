{"version": 3, "sources": ["../../../../src/server/components/paths.ts"], "sourcesContent": ["import { functionName } from \"../functionName.js\";\n\nexport const toReferencePath = Symbol.for(\"toReferencePath\");\n\n// Multiple instances of the same Symbol.for() are equal at runtime but not\n// at type-time, so `[toReferencePath]` properties aren't used in types.\n// Use this function to set the property invisibly.\nexport function setReferencePath<T>(obj: T, value: string) {\n  (obj as any)[toReferencePath] = value;\n}\n\nexport function extractReferencePath(reference: any): string | null {\n  return reference[toReferencePath] ?? null;\n}\n\nexport function isFunctionHandle(s: string): boolean {\n  return s.startsWith(\"function://\");\n}\n\nexport function getFunctionAddress(functionReference: any) {\n  // The `run*` syscalls expect either a UDF path at \"name\" or a serialized\n  // reference at \"reference\". Dispatch on `functionReference` to coerce\n  // it to one or the other.\n  let functionAddress;\n\n  // Legacy path for passing in UDF paths directly as function references.\n  if (typeof functionReference === \"string\") {\n    if (isFunctionHandle(functionReference)) {\n      functionAddress = { functionHandle: functionReference };\n    } else {\n      functionAddress = { name: functionReference };\n    }\n  }\n  // Path for passing in a `FunctionReference`, either from `api` or directly\n  // created from a UDF path with `makeFunctionReference`.\n  else if (functionReference[functionName]) {\n    functionAddress = { name: functionReference[functionName] };\n  }\n  // Reference to a component's function derived from `app` or `component`.\n  else {\n    const referencePath = extractReferencePath(functionReference);\n    if (!referencePath) {\n      throw new Error(`${functionReference} is not a functionReference`);\n    }\n    functionAddress = { reference: referencePath };\n  }\n  return functionAddress;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA6B;AAEtB,MAAM,kBAAkB,OAAO,IAAI,iBAAiB;AAKpD,SAAS,iBAAoB,KAAQ,OAAe;AACzD,EAAC,IAAY,eAAe,IAAI;AAClC;AAEO,SAAS,qBAAqB,WAA+B;AAClE,SAAO,UAAU,eAAe,KAAK;AACvC;AAEO,SAAS,iBAAiB,GAAoB;AACnD,SAAO,EAAE,WAAW,aAAa;AACnC;AAEO,SAAS,mBAAmB,mBAAwB;AAIzD,MAAI;AAGJ,MAAI,OAAO,sBAAsB,UAAU;AACzC,QAAI,iBAAiB,iBAAiB,GAAG;AACvC,wBAAkB,EAAE,gBAAgB,kBAAkB;AAAA,IACxD,OAAO;AACL,wBAAkB,EAAE,MAAM,kBAAkB;AAAA,IAC9C;AAAA,EACF,WAGS,kBAAkB,gCAAY,GAAG;AACxC,sBAAkB,EAAE,MAAM,kBAAkB,gCAAY,EAAE;AAAA,EAC5D,OAEK;AACH,UAAM,gBAAgB,qBAAqB,iBAAiB;AAC5D,QAAI,CAAC,eAAe;AAClB,YAAM,IAAI,MAAM,GAAG,iBAAiB,6BAA6B;AAAA,IACnE;AACA,sBAAkB,EAAE,WAAW,cAAc;AAAA,EAC/C;AACA,SAAO;AACT;", "names": []}