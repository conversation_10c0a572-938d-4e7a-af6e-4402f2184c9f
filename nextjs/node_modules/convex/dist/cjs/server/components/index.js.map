{"version": 3, "sources": ["../../../../src/server/components/index.ts"], "sourcesContent": ["import { PropertyValidators, convexToJson } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport {\n  AnyFunctionReference,\n  FunctionReference,\n  FunctionType,\n} from \"../api.js\";\nimport { performAsyncSyscall } from \"../impl/syscall.js\";\nimport { DefaultFunctionArgs } from \"../registration.js\";\nimport {\n  AppDefinitionAnalysis,\n  ComponentDefinitionAnalysis,\n  ComponentDefinitionType,\n} from \"./definition.js\";\nimport {\n  getFunctionAddress,\n  setReferencePath,\n  toReferencePath,\n} from \"./paths.js\";\nexport { getFunctionAddress } from \"./paths.js\";\n\n/**\n * A serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type FunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n> = string & FunctionReference<Type, \"internal\", Args, ReturnType>;\n\n/**\n * Create a serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport async function createFunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs,\n  ReturnType,\n>(\n  functionReference: FunctionReference<\n    Type,\n    \"public\" | \"internal\",\n    Args,\n    ReturnType\n  >,\n): Promise<FunctionHandle<Type, Args, ReturnType>> {\n  const address = getFunctionAddress(functionReference);\n  return await performAsyncSyscall(\"1.0/createFunctionHandle\", {\n    ...address,\n    version,\n  });\n}\n\ninterface ComponentExports {\n  [key: string]: FunctionReference<any, any, any, any> | ComponentExports;\n}\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component definition directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ComponentDefinition<Exports extends ComponentExports = any> = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n\n  /**\n   * Internal type-only property tracking exports provided.\n   *\n   * @deprecated This is a type-only property, don't use it.\n   */\n  __exports: Exports;\n};\n\ntype ComponentDefinitionExports<T extends ComponentDefinition<any>> =\n  T[\"__exports\"];\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component-aware convex directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type AppDefinition = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n};\n\ninterface ExportTree {\n  // Tree with serialized `Reference`s as leaves.\n  [key: string]: string | ExportTree;\n}\n\ntype CommonDefinitionData = {\n  _isRoot: boolean;\n  _childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][];\n  _exportTree: ExportTree;\n};\n\ntype ComponentDefinitionData = CommonDefinitionData & {\n  _args: PropertyValidators;\n  _name: string;\n  _onInitCallbacks: Record<string, (argsStr: string) => string>;\n};\ntype AppDefinitionData = CommonDefinitionData;\n\n/**\n * Used to refer to an already-installed component.\n */\nclass InstalledComponent<Definition extends ComponentDefinition<any>> {\n  /**\n   * @internal\n   */\n  _definition: Definition;\n\n  /**\n   * @internal\n   */\n  _name: string;\n\n  constructor(definition: Definition, name: string) {\n    this._definition = definition;\n    this._name = name;\n    setReferencePath(this, `_reference/childComponent/${name}`);\n  }\n\n  get exports(): ComponentDefinitionExports<Definition> {\n    return createExports(this._name, []);\n  }\n}\n\nfunction createExports(name: string, pathParts: string[]): any {\n  const handler: ProxyHandler<any> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createExports(name, newParts);\n      } else if (prop === toReferencePath) {\n        let reference = `_reference/childComponent/${name}`;\n        for (const part of pathParts) {\n          reference += `/${part}`;\n        }\n        return reference;\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nfunction use<Definition extends ComponentDefinition<any>>(\n  this: CommonDefinitionData,\n  definition: Definition,\n  options?: {\n    name?: string;\n  },\n): InstalledComponent<Definition> {\n  // At runtime an imported component will have this shape.\n  const importedComponentDefinition =\n    definition as unknown as ImportedComponentDefinition;\n  if (typeof importedComponentDefinition.componentDefinitionPath !== \"string\") {\n    throw new Error(\n      \"Component definition does not have the required componentDefinitionPath property. This code only works in Convex runtime.\",\n    );\n  }\n  const name =\n    options?.name ||\n    // added recently\n    importedComponentDefinition.defaultName ||\n    // can be removed once backend is out\n    importedComponentDefinition.componentDefinitionPath.split(\"/\").pop()!;\n  this._childComponents.push([name, importedComponentDefinition, {}]);\n  return new InstalledComponent(definition, name);\n}\n\n/**\n * The runtime type of a ComponentDefinition. TypeScript will claim\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ImportedComponentDefinition = {\n  componentDefinitionPath: string;\n  defaultName: string;\n};\n\nfunction exportAppForAnalysis(\n  this: ComponentDefinition<any> & AppDefinitionData,\n): AppDefinitionAnalysis {\n  const definitionType = { type: \"app\" as const };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\nfunction serializeExportTree(tree: ExportTree): any {\n  const branch: any[] = [];\n  for (const [key, child] of Object.entries(tree)) {\n    let node;\n    if (typeof child === \"string\") {\n      node = { type: \"leaf\", leaf: child };\n    } else {\n      node = serializeExportTree(child);\n    }\n    branch.push([key, node]);\n  }\n  return { type: \"branch\", branch };\n}\n\nfunction serializeChildComponents(\n  childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][],\n): {\n  name: string;\n  path: string;\n  args: [string, { type: \"value\"; value: string }][] | null;\n}[] {\n  return childComponents.map(([name, definition, p]) => {\n    let args: [string, { type: \"value\"; value: string }][] | null = null;\n    if (p !== null) {\n      args = [];\n      for (const [name, value] of Object.entries(p)) {\n        if (value !== undefined) {\n          args.push([\n            name,\n            { type: \"value\", value: JSON.stringify(convexToJson(value)) },\n          ]);\n        }\n      }\n    }\n    // we know that components carry this extra information\n    const path = definition.componentDefinitionPath;\n    if (!path)\n      throw new Error(\n        \"no .componentPath for component definition \" +\n          JSON.stringify(definition, null, 2),\n      );\n\n    return {\n      name: name!,\n      path: path!,\n      args,\n    };\n  });\n}\n\nfunction exportComponentForAnalysis(\n  this: ComponentDefinition<any> & ComponentDefinitionData,\n): ComponentDefinitionAnalysis {\n  const args: [string, { type: \"value\"; value: string }][] = Object.entries(\n    this._args,\n  ).map(([name, validator]) => [\n    name,\n    {\n      type: \"value\",\n      value: JSON.stringify(validator.json),\n    },\n  ]);\n  const definitionType: ComponentDefinitionType = {\n    type: \"childComponent\" as const,\n    name: this._name,\n    args,\n  };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    name: this._name,\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\n// This is what is actually contained in a ComponentDefinition.\ntype RuntimeComponentDefinition = Omit<ComponentDefinition<any>, \"__exports\"> &\n  ComponentDefinitionData & {\n    export: () => ComponentDefinitionAnalysis;\n  };\ntype RuntimeAppDefinition = AppDefinition &\n  AppDefinitionData & {\n    export: () => AppDefinitionAnalysis;\n  };\n\n/**\n * Define a component, a piece of a Convex deployment with namespaced resources.\n *\n * The default\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * @param name Name must be alphanumeric plus underscores. Typically these are\n * lowercase with underscores like `\"onboarding_flow_tracker\"`.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineComponent<Exports extends ComponentExports = any>(\n  name: string,\n): ComponentDefinition<Exports> {\n  const ret: RuntimeComponentDefinition = {\n    _isRoot: false,\n    _name: name,\n    _args: {},\n    _childComponents: [],\n    _exportTree: {},\n    _onInitCallbacks: {},\n\n    export: exportComponentForAnalysis,\n    use,\n\n    // pretend to conform to ComponentDefinition, which temporarily expects __args\n    ...({} as { __args: any; __exports: any }),\n  };\n  return ret as any as ComponentDefinition<Exports>;\n}\n\n/**\n * Attach components, reuseable pieces of a Convex deployment, to this Convex app.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineApp(): AppDefinition {\n  const ret: RuntimeAppDefinition = {\n    _isRoot: true,\n    _childComponents: [],\n    _exportTree: {},\n\n    export: exportAppForAnalysis,\n    use,\n  };\n  return ret as AppDefinition;\n}\n\ntype AnyInterfaceType = {\n  [key: string]: AnyInterfaceType;\n} & AnyFunctionReference;\nexport type AnyComponentReference = Record<string, AnyInterfaceType>;\n\nexport type AnyChildComponents = Record<string, AnyComponentReference>;\n\n/**\n * @internal\n */\nexport function currentSystemUdfInComponent(\n  componentId: string,\n): AnyComponentReference {\n  return {\n    [toReferencePath]: `_reference/currentSystemUdfInComponent/${componentId}`,\n  };\n}\n\nfunction createChildComponents(\n  root: string,\n  pathParts: string[],\n): AnyChildComponents {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createChildComponents(root, newParts);\n      } else if (prop === toReferencePath) {\n        if (pathParts.length < 1) {\n          const found = [root, ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`${root}.childComponent.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        return `_reference/childComponent/` + pathParts.join(\"/\");\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nexport const componentsGeneric = () => createChildComponents(\"components\", []);\n\nexport type AnyComponents = AnyChildComponents;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAiD;AACjD,eAAwB;AAMxB,qBAAoC;AAOpC,mBAIO;AACP,IAAAA,gBAAmC;AAkCnC,eAAsB,qBAKpB,mBAMiD;AACjD,QAAM,cAAU,iCAAmB,iBAAiB;AACpD,SAAO,UAAM,oCAAoB,4BAA4B;AAAA,IAC3D,GAAG;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAyFA,MAAM,mBAAgE;AAAA,EAWpE,YAAY,YAAwB,MAAc;AAPlD;AAAA;AAAA;AAAA;AAKA;AAAA;AAAA;AAAA;AAGE,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,uCAAiB,MAAM,6BAA6B,IAAI,EAAE;AAAA,EAC5D;AAAA,EAEA,IAAI,UAAkD;AACpD,WAAO,cAAc,KAAK,OAAO,CAAC,CAAC;AAAA,EACrC;AACF;AAEA,SAAS,cAAc,MAAc,WAA0B;AAC7D,QAAM,UAA6B;AAAA,IACjC,IAAI,GAAG,MAAuB;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,WAAW,CAAC,GAAG,WAAW,IAAI;AACpC,eAAO,cAAc,MAAM,QAAQ;AAAA,MACrC,WAAW,SAAS,8BAAiB;AACnC,YAAI,YAAY,6BAA6B,IAAI;AACjD,mBAAW,QAAQ,WAAW;AAC5B,uBAAa,IAAI,IAAI;AAAA,QACvB;AACA,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEA,SAAS,IAEP,YACA,SAGgC;AAEhC,QAAM,8BACJ;AACF,MAAI,OAAO,4BAA4B,4BAA4B,UAAU;AAC3E,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,QAAM,OACJ,SAAS;AAAA,EAET,4BAA4B;AAAA,EAE5B,4BAA4B,wBAAwB,MAAM,GAAG,EAAE,IAAI;AACrE,OAAK,iBAAiB,KAAK,CAAC,MAAM,6BAA6B,CAAC,CAAC,CAAC;AAClE,SAAO,IAAI,mBAAmB,YAAY,IAAI;AAChD;AAgBA,SAAS,uBAEgB;AACvB,QAAM,iBAAiB,EAAE,MAAM,MAAe;AAC9C,QAAM,kBAAkB,yBAAyB,KAAK,gBAAgB;AACtE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,YAAY,CAAC;AAAA,IACb,SAAS,oBAAoB,KAAK,WAAW;AAAA,EAC/C;AACF;AAEA,SAAS,oBAAoB,MAAuB;AAClD,QAAM,SAAgB,CAAC;AACvB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC/C,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,EAAE,MAAM,QAAQ,MAAM,MAAM;AAAA,IACrC,OAAO;AACL,aAAO,oBAAoB,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,CAAC,KAAK,IAAI,CAAC;AAAA,EACzB;AACA,SAAO,EAAE,MAAM,UAAU,OAAO;AAClC;AAEA,SAAS,yBACP,iBASE;AACF,SAAO,gBAAgB,IAAI,CAAC,CAAC,MAAM,YAAY,CAAC,MAAM;AACpD,QAAI,OAA4D;AAChE,QAAI,MAAM,MAAM;AACd,aAAO,CAAC;AACR,iBAAW,CAACC,OAAM,KAAK,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC7C,YAAI,UAAU,QAAW;AACvB,eAAK,KAAK;AAAA,YACRA;AAAA,YACA,EAAE,MAAM,SAAS,OAAO,KAAK,cAAU,4BAAa,KAAK,CAAC,EAAE;AAAA,UAC9D,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,UAAM,OAAO,WAAW;AACxB,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR,gDACE,KAAK,UAAU,YAAY,MAAM,CAAC;AAAA,MACtC;AAEF,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,6BAEsB;AAC7B,QAAM,OAAqD,OAAO;AAAA,IAChE,KAAK;AAAA,EACP,EAAE,IAAI,CAAC,CAAC,MAAM,SAAS,MAAM;AAAA,IAC3B;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO,KAAK,UAAU,UAAU,IAAI;AAAA,IACtC;AAAA,EACF,CAAC;AACD,QAAM,iBAA0C;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM,KAAK;AAAA,IACX;AAAA,EACF;AACA,QAAM,kBAAkB,yBAAyB,KAAK,gBAAgB;AACtE,SAAO;AAAA,IACL,MAAM,KAAK;AAAA,IACX;AAAA,IACA;AAAA,IACA,YAAY,CAAC;AAAA,IACb,SAAS,oBAAoB,KAAK,WAAW;AAAA,EAC/C;AACF;AA0BO,SAAS,gBACd,MAC8B;AAC9B,QAAM,MAAkC;AAAA,IACtC,SAAS;AAAA,IACT,OAAO;AAAA,IACP,OAAO,CAAC;AAAA,IACR,kBAAkB,CAAC;AAAA,IACnB,aAAa,CAAC;AAAA,IACd,kBAAkB,CAAC;AAAA,IAEnB,QAAQ;AAAA,IACR;AAAA;AAAA,IAGA,GAAI,CAAC;AAAA,EACP;AACA,SAAO;AACT;AAQO,SAAS,YAA2B;AACzC,QAAM,MAA4B;AAAA,IAChC,SAAS;AAAA,IACT,kBAAkB,CAAC;AAAA,IACnB,aAAa,CAAC;AAAA,IAEd,QAAQ;AAAA,IACR;AAAA,EACF;AACA,SAAO;AACT;AAYO,SAAS,4BACd,aACuB;AACvB,SAAO;AAAA,IACL,CAAC,4BAAe,GAAG,0CAA0C,WAAW;AAAA,EAC1E;AACF;AAEA,SAAS,sBACP,MACA,WACoB;AACpB,QAAM,UAAgC;AAAA,IACpC,IAAI,GAAG,MAAuB;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,WAAW,CAAC,GAAG,WAAW,IAAI;AACpC,eAAO,sBAAsB,MAAM,QAAQ;AAAA,MAC7C,WAAW,SAAS,8BAAiB;AACnC,YAAI,UAAU,SAAS,GAAG;AACxB,gBAAM,QAAQ,CAAC,MAAM,GAAG,SAAS,EAAE,KAAK,GAAG;AAC3C,gBAAM,IAAI;AAAA,YACR,4CAA4C,IAAI,4CAA4C,KAAK;AAAA,UACnG;AAAA,QACF;AACA,eAAO,+BAA+B,UAAU,KAAK,GAAG;AAAA,MAC1D,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEO,MAAM,oBAAoB,MAAM,sBAAsB,cAAc,CAAC,CAAC;", "names": ["import_paths", "name"]}