import { ConvexReactClient } from "convex/react";

// Initialize Convex client
const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Export the convex client for use in components
export { convex };

// Utility functions for common operations
export const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString();
};

export const formatDateTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

export const formatTime = (timeString: string): string => {
  // Convert 24-hour format to 12-hour format
  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

export const isUpcomingShow = (showDate: number): boolean => {
  return showDate > Date.now();
};

export const isPastShow = (showDate: number): boolean => {
  return showDate < Date.now();
};

export const getShowStatusColor = (status: string): string => {
  switch (status) {
    case 'coming_soon':
      return 'text-blue-600';
    case 'early_bird':
      return 'text-green-600';
    case 'regular_sale':
      return 'text-yellow-600';
    case 'low_availability':
      return 'text-orange-600';
    case 'sold_out':
      return 'text-red-600';
    case 'completed':
      return 'text-gray-600';
    case 'cancelled':
      return 'text-red-800';
    default:
      return 'text-gray-600';
  }
};

export const getUserRoleColor = (role: string): string => {
  switch (role) {
    case 'admin':
    case 'super_admin':
      return 'text-red-600';
    case 'staff':
      return 'text-purple-600';
    case 'artist':
      return 'text-blue-600';
    case 'vendor':
      return 'text-green-600';
    case 'crew':
      return 'text-orange-600';
    case 'attendee':
      return 'text-yellow-600';
    default:
      return 'text-gray-600';
  }
};

export const formatUserRole = (role: string): string => {
  switch (role) {
    case 'super_admin':
      return 'Super Admin';
    default:
      return role.charAt(0).toUpperCase() + role.slice(1);
  }
};

export const formatAdminLevel = (level: string): string => {
  switch (level) {
    case 'super_admin':
      return 'Super Admin';
    default:
      return level.charAt(0).toUpperCase() + level.slice(1);
  }
};

// Error handling utilities
export const handleConvexError = (error: unknown): string => {
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }
  return 'An unexpected error occurred';
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone);
};

export const validateTimeFormat = (time: string): boolean => {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

// Date utilities
export const getDateRange = (days: number): { start: number; end: number } => {
  const now = new Date();
  const start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const end = new Date(start.getTime() + (days * 24 * 60 * 60 * 1000));

  return {
    start: start.getTime(),
    end: end.getTime(),
  };
};

export const getUpcomingDateRange = (): { start: number; end: number } => {
  const now = Date.now();
  const oneYear = 365 * 24 * 60 * 60 * 1000;

  return {
    start: now,
    end: now + oneYear,
  };
};

export const getPastDateRange = (): { start: number; end: number } => {
  const now = Date.now();
  const oneYear = 365 * 24 * 60 * 60 * 1000;

  return {
    start: now - oneYear,
    end: now,
  };
};

// Permission utilities
export const hasPermission = (
  userPermissions: Record<string, boolean> | null | undefined,
  requiredPermission: string
): boolean => {
  if (!userPermissions) return false;
  return userPermissions[requiredPermission] === true;
};

export const isAdmin = (userRole: string): boolean => {
  return ['admin', 'staff', 'super_admin'].includes(userRole);
};

export const isSuperAdmin = (adminLevel?: string): boolean => {
  return adminLevel === 'super_admin';
};

// Local storage utilities for caching
export const getCachedData = (key: string): unknown => {
  if (typeof window === 'undefined') return null;

  try {
    const cached = localStorage.getItem(key);
    if (cached) {
      const parsed = JSON.parse(cached);
      // Check if data is less than 5 minutes old
      if (Date.now() - parsed.timestamp < 5 * 60 * 1000) {
        return parsed.data;
      }
    }
  } catch (error) {
    console.error('Error reading cached data:', error);
  }

  return null;
};

export const setCachedData = (key: string, data: unknown): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(key, JSON.stringify({
      data,
      timestamp: Date.now(),
    }));
  } catch (error) {
    console.error('Error caching data:', error);
  }
};
