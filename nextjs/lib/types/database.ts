import { Doc, Id } from "../../convex/_generated/dataModel";

// User types
export type User = Doc<"users">;
export type UserRole = User["role"];
export type UserId = Id<"users">;

export interface UserWithDetails extends User {
  attendedShowsDetails?: Show[];
}

// City types
export type City = Doc<"cities">;
export type CityId = Id<"cities">;

export interface CityWithCounts extends City {
  totalVenues?: number;
  activeVenues?: number;
  totalShows?: number;
  upcomingShows?: number;
  pastShows?: number;
}

// Venue types
export type Venue = Doc<"venues">;
export type VenueId = Id<"venues">;
export type ParkingOption = "free" | "paid" | "lot" | "street" | "valet" | "rideshare_area";

export interface VenueWithCity extends Venue {
  city?: City;
}

export interface VenueWithCounts extends Venue {
  totalShows?: number;
  upcomingShows?: number;
  pastShows?: number;
}

// Show types
export type Show = Doc<"shows">;
export type ShowId = Id<"shows">;
export type ShowStatus = Show["status"];

export interface ShowWithDetails extends Show {
  city?: City;
  venue?: Venue;
}

export interface TicketPrice {
  earlyBird?: number;
  regular: number;
  vip?: number;
}

// Admin user types
export type AdminUser = Doc<"adminUsers">;
export type AdminLevel = AdminUser["adminLevel"];
export type AdminPermissions = AdminUser["permissions"];

export interface AdminUserWithDetails extends AdminUser {
  user?: User;
  createdByUser?: User;
}

// Artist application types
export type ArtistApplication = Doc<"artistApplications">;
export type ArtistApplicationId = Id<"artistApplications">;
export type ApplicationStatus = ArtistApplication["status"];
export type ExperienceLevel = ArtistApplication["experienceLevel"];

export interface ArtistApplicationWithDetails extends ArtistApplication {
  user?: User;
  show?: Show;
  photos?: ApplicationPhoto[];
}

// Vendor application types
export type VendorApplication = Doc<"vendorApplications">;
export type VendorApplicationId = Id<"vendorApplications">;
export type BoothSize = VendorApplication["boothSizeNeeded"];

export interface VendorApplicationWithDetails extends VendorApplication {
  user?: User;
  show?: Show;
}

// Application photo types
export type ApplicationPhoto = Doc<"applicationPhotos">;
export type ApplicationPhotoId = Id<"applicationPhotos">;

export interface ApplicationPhotoWithDetails extends ApplicationPhoto {
  application?: ArtistApplication;
  user?: User;
}

// Form types for creating/updating records
export interface CreateUserForm {
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  profileImage?: string;
}

export interface UpdateUserProfileForm {
  firstName?: string;
  lastName?: string;
  phone?: string;
  bio?: string;
  location?: string;
  profileImage?: string;
}

export interface CreateCityForm {
  name: string;
  state: string;
  country: string;
  showsPerYear: number;
  notes?: string;
}

export interface UpdateCityForm {
  name?: string;
  state?: string;
  country?: string;
  showsPerYear?: number;
  isActive?: boolean;
  notes?: string;
}

export interface CreateVenueForm {
  cityId: CityId;
  name: string;
  address: string;
  capacity?: number;
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  notes?: string;
  parkingOptions?: ParkingOption[];
}

export interface UpdateVenueForm {
  name?: string;
  address?: string;
  capacity?: number;
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  notes?: string;
  isActive?: boolean;
  parkingOptions?: ParkingOption[];
}

export interface CreateShowForm {
  cityId: CityId;
  venueId: VenueId;
  title: string;
  description?: string;
  date: number;
  startTime: string;
  endTime: string;
  ticketPrice: TicketPrice;
  earlyBirdStartDate?: number;
  earlyBirdEndDate?: number;
  regularSaleStartDate: number;
  regularSaleEndDate: number;
  maxAttendees?: number;
  eventbriteEventId?: string;
  eventbriteUrl?: string;
}

export interface UpdateShowForm {
  title?: string;
  description?: string;
  date?: number;
  startTime?: string;
  endTime?: string;
  ticketPrice?: TicketPrice;
  earlyBirdStartDate?: number;
  earlyBirdEndDate?: number;
  regularSaleStartDate?: number;
  regularSaleEndDate?: number;
  maxAttendees?: number;
  status?: ShowStatus;
  eventbriteEventId?: string;
  eventbriteUrl?: string;
}

export interface CreateAdminUserForm {
  userId: UserId;
  adminLevel: AdminLevel;
  permissions: AdminPermissions;
  createdBy: UserId;
}

export interface CreateArtistApplicationForm {
  userId: UserId;
  showId: ShowId;
  artistStatement: string;
  medium: string;
  experienceLevel: ExperienceLevel;
  specialRequirements?: string;
}

export interface UpdateArtistApplicationForm {
  status?: ApplicationStatus;
  reviewedBy?: UserId;
  adminNotes?: string;
  boothNumber?: string;
  setupNotes?: string;
}

export interface CreateVendorApplicationForm {
  userId: UserId;
  showId: ShowId;
  businessName: string;
  productDescription: string;
  boothSizeNeeded: BoothSize;
  electricalNeeded: boolean;
  specialRequirements?: string;
}

export interface UpdateVendorApplicationForm {
  status?: ApplicationStatus;
  reviewedBy?: UserId;
  adminNotes?: string;
  boothNumber?: string;
  setupNotes?: string;
}

export interface CreateApplicationPhotoForm {
  artistApplicationId: ArtistApplicationId;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  caption?: string;
  uploadOrder: number;
}

// API response types
export interface PaginatedResponse<T> {
  page: T[];
  isDone: boolean;
  continueCursor: string;
}

// Search and filter types
export interface UserSearchParams {
  searchTerm?: string;
  role?: UserRole;
  limit?: number;
  cursor?: string;
}

export interface ShowSearchParams {
  cityId?: CityId;
  venueId?: VenueId;
  status?: ShowStatus;
  startDate?: number;
  endDate?: number;
  limit?: number;
  cursor?: string;
}

export interface VenueSearchParams {
  cityId?: CityId;
  searchTerm?: string;
  isActive?: boolean;
}

// Utility types
export type DatabaseTable = "users" | "cities" | "venues" | "shows" | "adminUsers" | "artistApplications" | "vendorApplications" | "applicationPhotos";

export interface DatabaseError {
  message: string;
  code?: string;
  table?: DatabaseTable;
}

// Constants
export const USER_ROLES = [
  "user",
  "attendee",
  "artist",
  "vendor",
  "crew",
  "admin",
  "staff"
] as const;

export const ADMIN_LEVELS = [
  "staff",
  "admin",
  "super_admin"
] as const;

export const SHOW_STATUSES = [
  "coming_soon",
  "early_bird",
  "regular_sale",
  "low_availability",
  "sold_out",
  "completed",
  "cancelled"
] as const;

export const PARKING_OPTIONS = [
  "free",
  "paid",
  "lot",
  "street",
  "valet",
  "rideshare_area"
] as const;

export const APPLICATION_STATUSES = [
  "pending",
  "under_review",
  "approved",
  "rejected",
  "waitlisted"
] as const;

export const EXPERIENCE_LEVELS = [
  "beginner",
  "intermediate",
  "advanced",
  "professional"
] as const;

export const BOOTH_SIZES = [
  "small",
  "medium",
  "large",
  "custom"
] as const;

export const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp"
] as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
export const MAX_PHOTOS_PER_APPLICATION = 3;
